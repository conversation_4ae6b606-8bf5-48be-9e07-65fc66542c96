!!python/object:nerfstudio.engine.trainer.TrainerConfig
_target: !!python/name:nerfstudio.engine.trainer.Trainer ''
data: &id002 !!python/object/apply:pathlib.WindowsPath
- nerfstudio_reconstruction
- nerfstudio_data
experiment_name: nerfstudio_data
gradient_accumulation_steps: {}
load_checkpoint: null
load_config: null
load_dir: null
load_scheduler: true
load_step: null
log_gradients: false
logging: !!python/object:nerfstudio.configs.base_config.LoggingConfig
  local_writer: !!python/object:nerfstudio.configs.base_config.LocalWriterConfig
    _target: !!python/name:nerfstudio.utils.writer.LocalWriter ''
    enable: true
    max_log_size: 10
    stats_to_track: !!python/tuple
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Train Iter (time)
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Train Rays / Sec
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Test PSNR
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Vis Rays / Sec
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Test Rays / Sec
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - ETA (time)
  max_buffer_size: 20
  profiler: basic
  relative_log_dir: !!python/object/apply:pathlib.WindowsPath []
  steps_per_log: 10
machine: !!python/object:nerfstudio.configs.base_config.MachineConfig
  device_type: cuda
  dist_url: auto
  machine_rank: 0
  num_devices: 1
  num_machines: 1
  seed: 42
max_num_iterations: 5000
method_name: vanilla-nerf
mixed_precision: false
optimizers:
  fields:
    optimizer: !!python/object:nerfstudio.engine.optimizers.RAdamOptimizerConfig
      _target: &id001 !!python/name:torch.optim.radam.RAdam ''
      eps: 1.0e-08
      lr: 0.0005
      max_norm: null
      weight_decay: 0
    scheduler: null
  temporal_distortion:
    optimizer: !!python/object:nerfstudio.engine.optimizers.RAdamOptimizerConfig
      _target: *id001
      eps: 1.0e-08
      lr: 0.0005
      max_norm: null
      weight_decay: 0
    scheduler: null
output_dir: !!python/object/apply:pathlib.WindowsPath
- nerfstudio_reconstruction
- training_outputs
pipeline: !!python/object:nerfstudio.pipelines.base_pipeline.VanillaPipelineConfig
  _target: !!python/name:nerfstudio.pipelines.base_pipeline.VanillaPipeline ''
  datamanager: !!python/object:nerfstudio.data.datamanagers.base_datamanager.VanillaDataManagerConfig
    _target: !!python/name:nerfstudio.data.datamanagers.base_datamanager.VanillaDataManager ''
    cache_images_type: float32
    camera_optimizer: null
    camera_res_scale_factor: 1.0
    collate_fn: !!python/name:nerfstudio.data.utils.nerfstudio_collate.nerfstudio_collate ''
    data: *id002
    dataparser: !!python/object:nerfstudio.data.dataparsers.blender_dataparser.BlenderDataParserConfig
      _target: !!python/name:nerfstudio.data.dataparsers.blender_dataparser.Blender ''
      alpha_color: white
      data: !!python/object/apply:pathlib.WindowsPath
      - data
      - blender
      - lego
      ply_path: null
      scale_factor: 1.0
    eval_image_indices: !!python/tuple
    - 0
    eval_num_images_to_sample_from: .inf
    eval_num_rays_per_batch: 1024
    eval_num_times_to_repeat_images: .inf
    images_on_gpu: false
    masks_on_gpu: false
    patch_size: 1
    pixel_sampler: !!python/object:nerfstudio.data.pixel_samplers.PixelSamplerConfig
      _target: !!python/name:nerfstudio.data.pixel_samplers.PixelSampler ''
      fisheye_crop_radius: null
      ignore_mask: false
      is_equirectangular: false
      keep_full_image: false
      max_num_iterations: 100
      num_rays_per_batch: 4096
      rejection_sample_mask: true
    train_num_images_to_sample_from: .inf
    train_num_rays_per_batch: 1024
    train_num_times_to_repeat_images: .inf
  model: !!python/object:nerfstudio.models.vanilla_nerf.VanillaModelConfig
    _target: !!python/name:nerfstudio.models.vanilla_nerf.NeRFModel ''
    background_color: white
    collider_params:
      far_plane: 6.0
      near_plane: 2.0
    enable_collider: true
    enable_temporal_distortion: false
    eval_num_rays_per_chunk: 4096
    loss_coefficients:
      rgb_loss_coarse: 1.0
      rgb_loss_fine: 1.0
    num_coarse_samples: 64
    num_importance_samples: 128
    prompt: null
    temporal_distortion_params:
      kind: !!python/object/apply:nerfstudio.field_components.temporal_distortions.TemporalDistortionKind
      - dnerf
    use_gradient_scaling: false
project_name: nerfstudio-project
prompt: null
relative_model_dir: !!python/object/apply:pathlib.WindowsPath
- nerfstudio_models
save_only_latest_checkpoint: true
start_paused: false
steps_per_eval_all_images: 25000
steps_per_eval_batch: 500
steps_per_eval_image: 500
steps_per_save: 1000
timestamp: 2025-07-09_122333
use_grad_scaler: false
viewer: !!python/object:nerfstudio.configs.base_config.ViewerConfig
  camera_frustum_scale: 0.1
  default_composite_depth: true
  image_format: jpeg
  jpeg_quality: 75
  make_share_url: false
  max_num_display_images: 512
  num_rays_per_chunk: 32768
  quit_on_train_completion: true
  relative_log_filename: viewer_log_filename.txt
  websocket_host: 0.0.0.0
  websocket_port: null
  websocket_port_default: 7007
vis: wandb
