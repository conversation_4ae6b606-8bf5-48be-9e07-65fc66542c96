{"version": 3, "file": "6739.b86fe9f9325e098414af.js?v=b86fe9f9325e098414af", "mappings": ";;;;;;;;;;AAAA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,mCAAmC,cAAc;AACjD;AACA;;AAEA;AACA;AACA,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;AACA,8BAA8B;AAC9B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,IAAI;AACpC;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,qHAAqH;AACrH;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,EAAE,mBAAmB,EAAE,EAAE,IAAI;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,EAAE;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,GAAG;AACb;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,uBAAuB,OAAO;AAC9B;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,2CAA2C;AAC3C;;AAEA;AACA,mBAAmB;AACnB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,gBAAgB,eAAe;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA,iDAAiD;AACjD;AACA;AACA;AACA,gBAAgB;AAChB,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,gBAAgB;AAChB;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,iBAAiB;AACjB;AACA,iBAAiB;AACjB;AACA,MAAM,eAAe;AACrB;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA,QAAQ;AACR;AACA;AACA,UAAU,aAAa;AACvB;AACA,UAAU;AACV;AACA,UAAU;AACV;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,0BAA0B;AAC1B,oBAAoB,oBAAoB,yBAAyB;AACjE;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/stylus.js"], "sourcesContent": ["// developer.mozilla.org/en-US/docs/Web/HTML/Element\nvar tagKeywords_ = [\"a\",\"abbr\",\"address\",\"area\",\"article\",\"aside\",\"audio\", \"b\", \"base\",\"bdi\", \"bdo\",\"bgsound\",\"blockquote\",\"body\",\"br\",\"button\",\"canvas\",\"caption\",\"cite\", \"code\",\"col\",\"colgroup\",\"data\",\"datalist\",\"dd\",\"del\",\"details\",\"dfn\",\"div\", \"dl\",\"dt\",\"em\",\"embed\",\"fieldset\",\"figcaption\",\"figure\",\"footer\",\"form\",\"h1\", \"h2\",\"h3\",\"h4\",\"h5\",\"h6\",\"head\",\"header\",\"hgroup\",\"hr\",\"html\",\"i\",\"iframe\", \"img\",\"input\",\"ins\",\"kbd\",\"keygen\",\"label\",\"legend\",\"li\",\"link\",\"main\",\"map\", \"mark\",\"marquee\",\"menu\",\"menuitem\",\"meta\",\"meter\",\"nav\",\"nobr\",\"noframes\", \"noscript\",\"object\",\"ol\",\"optgroup\",\"option\",\"output\",\"p\",\"param\",\"pre\", \"progress\",\"q\",\"rp\",\"rt\",\"ruby\",\"s\",\"samp\",\"script\",\"section\",\"select\", \"small\",\"source\",\"span\",\"strong\",\"style\",\"sub\",\"summary\",\"sup\",\"table\",\"tbody\",\"td\",\"textarea\",\"tfoot\",\"th\",\"thead\",\"time\",\"tr\",\"track\", \"u\",\"ul\",\"var\",\"video\"];\n\n// github.com/codemirror/CodeMirror/blob/master/mode/css/css.js\n// Note, \"url-prefix\" should precede \"url\" in order to match correctly in documentTypesRegexp\nvar documentTypes_ = [\"domain\", \"regexp\", \"url-prefix\", \"url\"];\nvar mediaTypes_ = [\"all\",\"aural\",\"braille\",\"handheld\",\"print\",\"projection\",\"screen\",\"tty\",\"tv\",\"embossed\"];\nvar mediaFeatures_ = [\"width\",\"min-width\",\"max-width\",\"height\",\"min-height\",\"max-height\",\"device-width\",\"min-device-width\",\"max-device-width\",\"device-height\",\"min-device-height\",\"max-device-height\",\"aspect-ratio\",\"min-aspect-ratio\",\"max-aspect-ratio\",\"device-aspect-ratio\",\"min-device-aspect-ratio\",\"max-device-aspect-ratio\",\"color\",\"min-color\",\"max-color\",\"color-index\",\"min-color-index\",\"max-color-index\",\"monochrome\",\"min-monochrome\",\"max-monochrome\",\"resolution\",\"min-resolution\",\"max-resolution\",\"scan\",\"grid\",\"dynamic-range\",\"video-dynamic-range\"];\nvar propertyKeywords_ = [\"align-content\",\"align-items\",\"align-self\",\"alignment-adjust\",\"alignment-baseline\",\"anchor-point\",\"animation\",\"animation-delay\",\"animation-direction\",\"animation-duration\",\"animation-fill-mode\",\"animation-iteration-count\",\"animation-name\",\"animation-play-state\",\"animation-timing-function\",\"appearance\",\"azimuth\",\"backface-visibility\",\"background\",\"background-attachment\",\"background-clip\",\"background-color\",\"background-image\",\"background-origin\",\"background-position\",\"background-repeat\",\"background-size\",\"baseline-shift\",\"binding\",\"bleed\",\"bookmark-label\",\"bookmark-level\",\"bookmark-state\",\"bookmark-target\",\"border\",\"border-bottom\",\"border-bottom-color\",\"border-bottom-left-radius\",\"border-bottom-right-radius\",\"border-bottom-style\",\"border-bottom-width\",\"border-collapse\",\"border-color\",\"border-image\",\"border-image-outset\",\"border-image-repeat\",\"border-image-slice\",\"border-image-source\",\"border-image-width\",\"border-left\",\"border-left-color\",\"border-left-style\",\"border-left-width\",\"border-radius\",\"border-right\",\"border-right-color\",\"border-right-style\",\"border-right-width\",\"border-spacing\",\"border-style\",\"border-top\",\"border-top-color\",\"border-top-left-radius\",\"border-top-right-radius\",\"border-top-style\",\"border-top-width\",\"border-width\",\"bottom\",\"box-decoration-break\",\"box-shadow\",\"box-sizing\",\"break-after\",\"break-before\",\"break-inside\",\"caption-side\",\"clear\",\"clip\",\"color\",\"color-profile\",\"column-count\",\"column-fill\",\"column-gap\",\"column-rule\",\"column-rule-color\",\"column-rule-style\",\"column-rule-width\",\"column-span\",\"column-width\",\"columns\",\"content\",\"counter-increment\",\"counter-reset\",\"crop\",\"cue\",\"cue-after\",\"cue-before\",\"cursor\",\"direction\",\"display\",\"dominant-baseline\",\"drop-initial-after-adjust\",\"drop-initial-after-align\",\"drop-initial-before-adjust\",\"drop-initial-before-align\",\"drop-initial-size\",\"drop-initial-value\",\"elevation\",\"empty-cells\",\"fit\",\"fit-position\",\"flex\",\"flex-basis\",\"flex-direction\",\"flex-flow\",\"flex-grow\",\"flex-shrink\",\"flex-wrap\",\"float\",\"float-offset\",\"flow-from\",\"flow-into\",\"font\",\"font-feature-settings\",\"font-family\",\"font-kerning\",\"font-language-override\",\"font-size\",\"font-size-adjust\",\"font-stretch\",\"font-style\",\"font-synthesis\",\"font-variant\",\"font-variant-alternates\",\"font-variant-caps\",\"font-variant-east-asian\",\"font-variant-ligatures\",\"font-variant-numeric\",\"font-variant-position\",\"font-weight\",\"grid\",\"grid-area\",\"grid-auto-columns\",\"grid-auto-flow\",\"grid-auto-position\",\"grid-auto-rows\",\"grid-column\",\"grid-column-end\",\"grid-column-start\",\"grid-row\",\"grid-row-end\",\"grid-row-start\",\"grid-template\",\"grid-template-areas\",\"grid-template-columns\",\"grid-template-rows\",\"hanging-punctuation\",\"height\",\"hyphens\",\"icon\",\"image-orientation\",\"image-rendering\",\"image-resolution\",\"inline-box-align\",\"justify-content\",\"left\",\"letter-spacing\",\"line-break\",\"line-height\",\"line-stacking\",\"line-stacking-ruby\",\"line-stacking-shift\",\"line-stacking-strategy\",\"list-style\",\"list-style-image\",\"list-style-position\",\"list-style-type\",\"margin\",\"margin-bottom\",\"margin-left\",\"margin-right\",\"margin-top\",\"marker-offset\",\"marks\",\"marquee-direction\",\"marquee-loop\",\"marquee-play-count\",\"marquee-speed\",\"marquee-style\",\"max-height\",\"max-width\",\"min-height\",\"min-width\",\"move-to\",\"nav-down\",\"nav-index\",\"nav-left\",\"nav-right\",\"nav-up\",\"object-fit\",\"object-position\",\"opacity\",\"order\",\"orphans\",\"outline\",\"outline-color\",\"outline-offset\",\"outline-style\",\"outline-width\",\"overflow\",\"overflow-style\",\"overflow-wrap\",\"overflow-x\",\"overflow-y\",\"padding\",\"padding-bottom\",\"padding-left\",\"padding-right\",\"padding-top\",\"page\",\"page-break-after\",\"page-break-before\",\"page-break-inside\",\"page-policy\",\"pause\",\"pause-after\",\"pause-before\",\"perspective\",\"perspective-origin\",\"pitch\",\"pitch-range\",\"play-during\",\"position\",\"presentation-level\",\"punctuation-trim\",\"quotes\",\"region-break-after\",\"region-break-before\",\"region-break-inside\",\"region-fragment\",\"rendering-intent\",\"resize\",\"rest\",\"rest-after\",\"rest-before\",\"richness\",\"right\",\"rotation\",\"rotation-point\",\"ruby-align\",\"ruby-overhang\",\"ruby-position\",\"ruby-span\",\"shape-image-threshold\",\"shape-inside\",\"shape-margin\",\"shape-outside\",\"size\",\"speak\",\"speak-as\",\"speak-header\",\"speak-numeral\",\"speak-punctuation\",\"speech-rate\",\"stress\",\"string-set\",\"tab-size\",\"table-layout\",\"target\",\"target-name\",\"target-new\",\"target-position\",\"text-align\",\"text-align-last\",\"text-decoration\",\"text-decoration-color\",\"text-decoration-line\",\"text-decoration-skip\",\"text-decoration-style\",\"text-emphasis\",\"text-emphasis-color\",\"text-emphasis-position\",\"text-emphasis-style\",\"text-height\",\"text-indent\",\"text-justify\",\"text-outline\",\"text-overflow\",\"text-shadow\",\"text-size-adjust\",\"text-space-collapse\",\"text-transform\",\"text-underline-position\",\"text-wrap\",\"top\",\"transform\",\"transform-origin\",\"transform-style\",\"transition\",\"transition-delay\",\"transition-duration\",\"transition-property\",\"transition-timing-function\",\"unicode-bidi\",\"vertical-align\",\"visibility\",\"voice-balance\",\"voice-duration\",\"voice-family\",\"voice-pitch\",\"voice-range\",\"voice-rate\",\"voice-stress\",\"voice-volume\",\"volume\",\"white-space\",\"widows\",\"width\",\"will-change\",\"word-break\",\"word-spacing\",\"word-wrap\",\"z-index\",\"clip-path\",\"clip-rule\",\"mask\",\"enable-background\",\"filter\",\"flood-color\",\"flood-opacity\",\"lighting-color\",\"stop-color\",\"stop-opacity\",\"pointer-events\",\"color-interpolation\",\"color-interpolation-filters\",\"color-rendering\",\"fill\",\"fill-opacity\",\"fill-rule\",\"image-rendering\",\"marker\",\"marker-end\",\"marker-mid\",\"marker-start\",\"shape-rendering\",\"stroke\",\"stroke-dasharray\",\"stroke-dashoffset\",\"stroke-linecap\",\"stroke-linejoin\",\"stroke-miterlimit\",\"stroke-opacity\",\"stroke-width\",\"text-rendering\",\"baseline-shift\",\"dominant-baseline\",\"glyph-orientation-horizontal\",\"glyph-orientation-vertical\",\"text-anchor\",\"writing-mode\",\"font-smoothing\",\"osx-font-smoothing\"];\nvar nonStandardPropertyKeywords_ = [\"scrollbar-arrow-color\",\"scrollbar-base-color\",\"scrollbar-dark-shadow-color\",\"scrollbar-face-color\",\"scrollbar-highlight-color\",\"scrollbar-shadow-color\",\"scrollbar-3d-light-color\",\"scrollbar-track-color\",\"shape-inside\",\"searchfield-cancel-button\",\"searchfield-decoration\",\"searchfield-results-button\",\"searchfield-results-decoration\",\"zoom\"];\nvar fontProperties_ = [\"font-family\",\"src\",\"unicode-range\",\"font-variant\",\"font-feature-settings\",\"font-stretch\",\"font-weight\",\"font-style\"];\nvar colorKeywords_ = [\"aliceblue\",\"antiquewhite\",\"aqua\",\"aquamarine\",\"azure\",\"beige\",\"bisque\",\"black\",\"blanchedalmond\",\"blue\",\"blueviolet\",\"brown\",\"burlywood\",\"cadetblue\",\"chartreuse\",\"chocolate\",\"coral\",\"cornflowerblue\",\"cornsilk\",\"crimson\",\"cyan\",\"darkblue\",\"darkcyan\",\"darkgoldenrod\",\"darkgray\",\"darkgreen\",\"darkkhaki\",\"darkmagenta\",\"darkolivegreen\",\"darkorange\",\"darkorchid\",\"darkred\",\"darksalmon\",\"darkseagreen\",\"darkslateblue\",\"darkslategray\",\"darkturquoise\",\"darkviolet\",\"deeppink\",\"deepskyblue\",\"dimgray\",\"dodgerblue\",\"firebrick\",\"floralwhite\",\"forestgreen\",\"fuchsia\",\"gainsboro\",\"ghostwhite\",\"gold\",\"goldenrod\",\"gray\",\"grey\",\"green\",\"greenyellow\",\"honeydew\",\"hotpink\",\"indianred\",\"indigo\",\"ivory\",\"khaki\",\"lavender\",\"lavenderblush\",\"lawngreen\",\"lemonchiffon\",\"lightblue\",\"lightcoral\",\"lightcyan\",\"lightgoldenrodyellow\",\"lightgray\",\"lightgreen\",\"lightpink\",\"lightsalmon\",\"lightseagreen\",\"lightskyblue\",\"lightslategray\",\"lightsteelblue\",\"lightyellow\",\"lime\",\"limegreen\",\"linen\",\"magenta\",\"maroon\",\"mediumaquamarine\",\"mediumblue\",\"mediumorchid\",\"mediumpurple\",\"mediumseagreen\",\"mediumslateblue\",\"mediumspringgreen\",\"mediumturquoise\",\"mediumvioletred\",\"midnightblue\",\"mintcream\",\"mistyrose\",\"moccasin\",\"navajowhite\",\"navy\",\"oldlace\",\"olive\",\"olivedrab\",\"orange\",\"orangered\",\"orchid\",\"palegoldenrod\",\"palegreen\",\"paleturquoise\",\"palevioletred\",\"papayawhip\",\"peachpuff\",\"peru\",\"pink\",\"plum\",\"powderblue\",\"purple\",\"rebeccapurple\",\"red\",\"rosybrown\",\"royalblue\",\"saddlebrown\",\"salmon\",\"sandybrown\",\"seagreen\",\"seashell\",\"sienna\",\"silver\",\"skyblue\",\"slateblue\",\"slategray\",\"snow\",\"springgreen\",\"steelblue\",\"tan\",\"teal\",\"thistle\",\"tomato\",\"turquoise\",\"violet\",\"wheat\",\"white\",\"whitesmoke\",\"yellow\",\"yellowgreen\"];\nvar valueKeywords_ = [\"above\",\"absolute\",\"activeborder\",\"additive\",\"activecaption\",\"afar\",\"after-white-space\",\"ahead\",\"alias\",\"all\",\"all-scroll\",\"alphabetic\",\"alternate\",\"always\",\"amharic\",\"amharic-abegede\",\"antialiased\",\"appworkspace\",\"arabic-indic\",\"armenian\",\"asterisks\",\"attr\",\"auto\",\"avoid\",\"avoid-column\",\"avoid-page\",\"avoid-region\",\"background\",\"backwards\",\"baseline\",\"below\",\"bidi-override\",\"binary\",\"bengali\",\"blink\",\"block\",\"block-axis\",\"bold\",\"bolder\",\"border\",\"border-box\",\"both\",\"bottom\",\"break\",\"break-all\",\"break-word\",\"bullets\",\"button\",\"buttonface\",\"buttonhighlight\",\"buttonshadow\",\"buttontext\",\"calc\",\"cambodian\",\"capitalize\",\"caps-lock-indicator\",\"caption\",\"captiontext\",\"caret\",\"cell\",\"center\",\"checkbox\",\"circle\",\"cjk-decimal\",\"cjk-earthly-branch\",\"cjk-heavenly-stem\",\"cjk-ideographic\",\"clear\",\"clip\",\"close-quote\",\"col-resize\",\"collapse\",\"column\",\"compact\",\"condensed\",\"conic-gradient\",\"contain\",\"content\",\"contents\",\"content-box\",\"context-menu\",\"continuous\",\"copy\",\"counter\",\"counters\",\"cover\",\"crop\",\"cross\",\"crosshair\",\"currentcolor\",\"cursive\",\"cyclic\",\"dashed\",\"decimal\",\"decimal-leading-zero\",\"default\",\"default-button\",\"destination-atop\",\"destination-in\",\"destination-out\",\"destination-over\",\"devanagari\",\"disc\",\"discard\",\"disclosure-closed\",\"disclosure-open\",\"document\",\"dot-dash\",\"dot-dot-dash\",\"dotted\",\"double\",\"down\",\"e-resize\",\"ease\",\"ease-in\",\"ease-in-out\",\"ease-out\",\"element\",\"ellipse\",\"ellipsis\",\"embed\",\"end\",\"ethiopic\",\"ethiopic-abegede\",\"ethiopic-abegede-am-et\",\"ethiopic-abegede-gez\",\"ethiopic-abegede-ti-er\",\"ethiopic-abegede-ti-et\",\"ethiopic-halehame-aa-er\",\"ethiopic-halehame-aa-et\",\"ethiopic-halehame-am-et\",\"ethiopic-halehame-gez\",\"ethiopic-halehame-om-et\",\"ethiopic-halehame-sid-et\",\"ethiopic-halehame-so-et\",\"ethiopic-halehame-ti-er\",\"ethiopic-halehame-ti-et\",\"ethiopic-halehame-tig\",\"ethiopic-numeric\",\"ew-resize\",\"expanded\",\"extends\",\"extra-condensed\",\"extra-expanded\",\"fantasy\",\"fast\",\"fill\",\"fixed\",\"flat\",\"flex\",\"footnotes\",\"forwards\",\"from\",\"geometricPrecision\",\"georgian\",\"graytext\",\"groove\",\"gujarati\",\"gurmukhi\",\"hand\",\"hangul\",\"hangul-consonant\",\"hebrew\",\"help\",\"hidden\",\"hide\",\"high\",\"higher\",\"highlight\",\"highlighttext\",\"hiragana\",\"hiragana-iroha\",\"horizontal\",\"hsl\",\"hsla\",\"icon\",\"ignore\",\"inactiveborder\",\"inactivecaption\",\"inactivecaptiontext\",\"infinite\",\"infobackground\",\"infotext\",\"inherit\",\"initial\",\"inline\",\"inline-axis\",\"inline-block\",\"inline-flex\",\"inline-table\",\"inset\",\"inside\",\"intrinsic\",\"invert\",\"italic\",\"japanese-formal\",\"japanese-informal\",\"justify\",\"kannada\",\"katakana\",\"katakana-iroha\",\"keep-all\",\"khmer\",\"korean-hangul-formal\",\"korean-hanja-formal\",\"korean-hanja-informal\",\"landscape\",\"lao\",\"large\",\"larger\",\"left\",\"level\",\"lighter\",\"line-through\",\"linear\",\"linear-gradient\",\"lines\",\"list-item\",\"listbox\",\"listitem\",\"local\",\"logical\",\"loud\",\"lower\",\"lower-alpha\",\"lower-armenian\",\"lower-greek\",\"lower-hexadecimal\",\"lower-latin\",\"lower-norwegian\",\"lower-roman\",\"lowercase\",\"ltr\",\"malayalam\",\"match\",\"matrix\",\"matrix3d\",\"media-play-button\",\"media-slider\",\"media-sliderthumb\",\"media-volume-slider\",\"media-volume-sliderthumb\",\"medium\",\"menu\",\"menulist\",\"menulist-button\",\"menutext\",\"message-box\",\"middle\",\"min-intrinsic\",\"mix\",\"mongolian\",\"monospace\",\"move\",\"multiple\",\"myanmar\",\"n-resize\",\"narrower\",\"ne-resize\",\"nesw-resize\",\"no-close-quote\",\"no-drop\",\"no-open-quote\",\"no-repeat\",\"none\",\"normal\",\"not-allowed\",\"nowrap\",\"ns-resize\",\"numbers\",\"numeric\",\"nw-resize\",\"nwse-resize\",\"oblique\",\"octal\",\"open-quote\",\"optimizeLegibility\",\"optimizeSpeed\",\"oriya\",\"oromo\",\"outset\",\"outside\",\"outside-shape\",\"overlay\",\"overline\",\"padding\",\"padding-box\",\"painted\",\"page\",\"paused\",\"persian\",\"perspective\",\"plus-darker\",\"plus-lighter\",\"pointer\",\"polygon\",\"portrait\",\"pre\",\"pre-line\",\"pre-wrap\",\"preserve-3d\",\"progress\",\"push-button\",\"radial-gradient\",\"radio\",\"read-only\",\"read-write\",\"read-write-plaintext-only\",\"rectangle\",\"region\",\"relative\",\"repeat\",\"repeating-linear-gradient\",\"repeating-radial-gradient\",\"repeating-conic-gradient\",\"repeat-x\",\"repeat-y\",\"reset\",\"reverse\",\"rgb\",\"rgba\",\"ridge\",\"right\",\"rotate\",\"rotate3d\",\"rotateX\",\"rotateY\",\"rotateZ\",\"round\",\"row-resize\",\"rtl\",\"run-in\",\"running\",\"s-resize\",\"sans-serif\",\"scale\",\"scale3d\",\"scaleX\",\"scaleY\",\"scaleZ\",\"scroll\",\"scrollbar\",\"scroll-position\",\"se-resize\",\"searchfield\",\"searchfield-cancel-button\",\"searchfield-decoration\",\"searchfield-results-button\",\"searchfield-results-decoration\",\"semi-condensed\",\"semi-expanded\",\"separate\",\"serif\",\"show\",\"sidama\",\"simp-chinese-formal\",\"simp-chinese-informal\",\"single\",\"skew\",\"skewX\",\"skewY\",\"skip-white-space\",\"slide\",\"slider-horizontal\",\"slider-vertical\",\"sliderthumb-horizontal\",\"sliderthumb-vertical\",\"slow\",\"small\",\"small-caps\",\"small-caption\",\"smaller\",\"solid\",\"somali\",\"source-atop\",\"source-in\",\"source-out\",\"source-over\",\"space\",\"spell-out\",\"square\",\"square-button\",\"standard\",\"start\",\"static\",\"status-bar\",\"stretch\",\"stroke\",\"sub\",\"subpixel-antialiased\",\"super\",\"sw-resize\",\"symbolic\",\"symbols\",\"table\",\"table-caption\",\"table-cell\",\"table-column\",\"table-column-group\",\"table-footer-group\",\"table-header-group\",\"table-row\",\"table-row-group\",\"tamil\",\"telugu\",\"text\",\"text-bottom\",\"text-top\",\"textarea\",\"textfield\",\"thai\",\"thick\",\"thin\",\"threeddarkshadow\",\"threedface\",\"threedhighlight\",\"threedlightshadow\",\"threedshadow\",\"tibetan\",\"tigre\",\"tigrinya-er\",\"tigrinya-er-abegede\",\"tigrinya-et\",\"tigrinya-et-abegede\",\"to\",\"top\",\"trad-chinese-formal\",\"trad-chinese-informal\",\"translate\",\"translate3d\",\"translateX\",\"translateY\",\"translateZ\",\"transparent\",\"ultra-condensed\",\"ultra-expanded\",\"underline\",\"up\",\"upper-alpha\",\"upper-armenian\",\"upper-greek\",\"upper-hexadecimal\",\"upper-latin\",\"upper-norwegian\",\"upper-roman\",\"uppercase\",\"urdu\",\"url\",\"var\",\"vertical\",\"vertical-text\",\"visible\",\"visibleFill\",\"visiblePainted\",\"visibleStroke\",\"visual\",\"w-resize\",\"wait\",\"wave\",\"wider\",\"window\",\"windowframe\",\"windowtext\",\"words\",\"x-large\",\"x-small\",\"xor\",\"xx-large\",\"xx-small\",\"bicubic\",\"optimizespeed\",\"grayscale\",\"row\",\"row-reverse\",\"wrap\",\"wrap-reverse\",\"column-reverse\",\"flex-start\",\"flex-end\",\"space-between\",\"space-around\", \"unset\"];\n\nvar wordOperatorKeywords_ = [\"in\",\"and\",\"or\",\"not\",\"is not\",\"is a\",\"is\",\"isnt\",\"defined\",\"if unless\"],\n    blockKeywords_ = [\"for\",\"if\",\"else\",\"unless\", \"from\", \"to\"],\n    commonAtoms_ = [\"null\",\"true\",\"false\",\"href\",\"title\",\"type\",\"not-allowed\",\"readonly\",\"disabled\"],\n    commonDef_ = [\"@font-face\", \"@keyframes\", \"@media\", \"@viewport\", \"@page\", \"@host\", \"@supports\", \"@block\", \"@css\"];\n\nvar hintWords = tagKeywords_.concat(documentTypes_,mediaTypes_,mediaFeatures_,\n                                    propertyKeywords_,nonStandardPropertyKeywords_,\n                                    colorKeywords_,valueKeywords_,fontProperties_,\n                                    wordOperatorKeywords_,blockKeywords_,\n                                    commonAtoms_,commonDef_);\n\nfunction wordRegexp(words) {\n  words = words.sort(function(a,b){return b > a;});\n  return new RegExp(\"^((\" + words.join(\")|(\") + \"))\\\\b\");\n}\n\nfunction keySet(array) {\n  var keys = {};\n  for (var i = 0; i < array.length; ++i) keys[array[i]] = true;\n  return keys;\n}\n\nfunction escapeRegExp(text) {\n  return text.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, \"\\\\$&\");\n}\n\nvar tagKeywords = keySet(tagKeywords_),\n    tagVariablesRegexp = /^(a|b|i|s|col|em)$/i,\n    propertyKeywords = keySet(propertyKeywords_),\n    nonStandardPropertyKeywords = keySet(nonStandardPropertyKeywords_),\n    valueKeywords = keySet(valueKeywords_),\n    colorKeywords = keySet(colorKeywords_),\n    documentTypes = keySet(documentTypes_),\n    documentTypesRegexp = wordRegexp(documentTypes_),\n    mediaFeatures = keySet(mediaFeatures_),\n    mediaTypes = keySet(mediaTypes_),\n    fontProperties = keySet(fontProperties_),\n    operatorsRegexp = /^\\s*([.]{2,3}|&&|\\|\\||\\*\\*|[?!=:]?=|[-+*\\/%<>]=?|\\?:|\\~)/,\n    wordOperatorKeywordsRegexp = wordRegexp(wordOperatorKeywords_),\n    blockKeywords = keySet(blockKeywords_),\n    vendorPrefixesRegexp = new RegExp(/^\\-(moz|ms|o|webkit)-/i),\n    commonAtoms = keySet(commonAtoms_),\n    firstWordMatch = \"\",\n    states = {},\n    ch,\n    style,\n    type,\n    override;\n\n/**\n * Tokenizers\n */\nfunction tokenBase(stream, state) {\n  firstWordMatch = stream.string.match(/(^[\\w-]+\\s*=\\s*$)|(^\\s*[\\w-]+\\s*=\\s*[\\w-])|(^\\s*(\\.|#|@|\\$|\\&|\\[|\\d|\\+|::?|\\{|\\>|~|\\/)?\\s*[\\w-]*([a-z0-9-]|\\*|\\/\\*)(\\(|,)?)/);\n  state.context.line.firstWord = firstWordMatch ? firstWordMatch[0].replace(/^\\s*/, \"\") : \"\";\n  state.context.line.indent = stream.indentation();\n  ch = stream.peek();\n\n  // Line comment\n  if (stream.match(\"//\")) {\n    stream.skipToEnd();\n    return [\"comment\", \"comment\"];\n  }\n  // Block comment\n  if (stream.match(\"/*\")) {\n    state.tokenize = tokenCComment;\n    return tokenCComment(stream, state);\n  }\n  // String\n  if (ch == \"\\\"\" || ch == \"'\") {\n    stream.next();\n    state.tokenize = tokenString(ch);\n    return state.tokenize(stream, state);\n  }\n  // Def\n  if (ch == \"@\") {\n    stream.next();\n    stream.eatWhile(/[\\w\\\\-]/);\n    return [\"def\", stream.current()];\n  }\n  // ID selector or Hex color\n  if (ch == \"#\") {\n    stream.next();\n    // Hex color\n    if (stream.match(/^[0-9a-f]{3}([0-9a-f]([0-9a-f]{2}){0,2})?\\b(?!-)/i)) {\n      return [\"atom\", \"atom\"];\n    }\n    // ID selector\n    if (stream.match(/^[a-z][\\w-]*/i)) {\n      return [\"builtin\", \"hash\"];\n    }\n  }\n  // Vendor prefixes\n  if (stream.match(vendorPrefixesRegexp)) {\n    return [\"meta\", \"vendor-prefixes\"];\n  }\n  // Numbers\n  if (stream.match(/^-?[0-9]?\\.?[0-9]/)) {\n    stream.eatWhile(/[a-z%]/i);\n    return [\"number\", \"unit\"];\n  }\n  // !important|optional\n  if (ch == \"!\") {\n    stream.next();\n    return [stream.match(/^(important|optional)/i) ? \"keyword\": \"operator\", \"important\"];\n  }\n  // Class\n  if (ch == \".\" && stream.match(/^\\.[a-z][\\w-]*/i)) {\n    return [\"qualifier\", \"qualifier\"];\n  }\n  // url url-prefix domain regexp\n  if (stream.match(documentTypesRegexp)) {\n    if (stream.peek() == \"(\") state.tokenize = tokenParenthesized;\n    return [\"property\", \"word\"];\n  }\n  // Mixins / Functions\n  if (stream.match(/^[a-z][\\w-]*\\(/i)) {\n    stream.backUp(1);\n    return [\"keyword\", \"mixin\"];\n  }\n  // Block mixins\n  if (stream.match(/^(\\+|-)[a-z][\\w-]*\\(/i)) {\n    stream.backUp(1);\n    return [\"keyword\", \"block-mixin\"];\n  }\n  // Parent Reference BEM naming\n  if (stream.string.match(/^\\s*&/) && stream.match(/^[-_]+[a-z][\\w-]*/)) {\n    return [\"qualifier\", \"qualifier\"];\n  }\n  // / Root Reference & Parent Reference\n  if (stream.match(/^(\\/|&)(-|_|:|\\.|#|[a-z])/)) {\n    stream.backUp(1);\n    return [\"variableName.special\", \"reference\"];\n  }\n  if (stream.match(/^&{1}\\s*$/)) {\n    return [\"variableName.special\", \"reference\"];\n  }\n  // Word operator\n  if (stream.match(wordOperatorKeywordsRegexp)) {\n    return [\"operator\", \"operator\"];\n  }\n  // Word\n  if (stream.match(/^\\$?[-_]*[a-z0-9]+[\\w-]*/i)) {\n    // Variable\n    if (stream.match(/^(\\.|\\[)[\\w-\\'\\\"\\]]+/i, false)) {\n      if (!wordIsTag(stream.current())) {\n        stream.match('.');\n        return [\"variable\", \"variable-name\"];\n      }\n    }\n    return [\"variable\", \"word\"];\n  }\n  // Operators\n  if (stream.match(operatorsRegexp)) {\n    return [\"operator\", stream.current()];\n  }\n  // Delimiters\n  if (/[:;,{}\\[\\]\\(\\)]/.test(ch)) {\n    stream.next();\n    return [null, ch];\n  }\n  // Non-detected items\n  stream.next();\n  return [null, null];\n}\n\n/**\n * Token comment\n */\nfunction tokenCComment(stream, state) {\n  var maybeEnd = false, ch;\n  while ((ch = stream.next()) != null) {\n    if (maybeEnd && ch == \"/\") {\n      state.tokenize = null;\n      break;\n    }\n    maybeEnd = (ch == \"*\");\n  }\n  return [\"comment\", \"comment\"];\n}\n\n/**\n * Token string\n */\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, ch;\n    while ((ch = stream.next()) != null) {\n      if (ch == quote && !escaped) {\n        if (quote == \")\") stream.backUp(1);\n        break;\n      }\n      escaped = !escaped && ch == \"\\\\\";\n    }\n    if (ch == quote || !escaped && quote != \")\") state.tokenize = null;\n    return [\"string\", \"string\"];\n  };\n}\n\n/**\n * Token parenthesized\n */\nfunction tokenParenthesized(stream, state) {\n  stream.next(); // Must be \"(\"\n  if (!stream.match(/\\s*[\\\"\\')]/, false))\n    state.tokenize = tokenString(\")\");\n  else\n    state.tokenize = null;\n  return [null, \"(\"];\n}\n\n/**\n * Context management\n */\nfunction Context(type, indent, prev, line) {\n  this.type = type;\n  this.indent = indent;\n  this.prev = prev;\n  this.line = line || {firstWord: \"\", indent: 0};\n}\n\nfunction pushContext(state, stream, type, indent) {\n  indent = indent >= 0 ? indent : stream.indentUnit;\n  state.context = new Context(type, stream.indentation() + indent, state.context);\n  return type;\n}\n\nfunction popContext(state, stream, currentIndent) {\n  var contextIndent = state.context.indent - stream.indentUnit;\n  currentIndent = currentIndent || false;\n  state.context = state.context.prev;\n  if (currentIndent) state.context.indent = contextIndent;\n  return state.context.type;\n}\n\nfunction pass(type, stream, state) {\n  return states[state.context.type](type, stream, state);\n}\n\nfunction popAndPass(type, stream, state, n) {\n  for (var i = n || 1; i > 0; i--)\n    state.context = state.context.prev;\n  return pass(type, stream, state);\n}\n\n\n/**\n * Parser\n */\nfunction wordIsTag(word) {\n  return word.toLowerCase() in tagKeywords;\n}\n\nfunction wordIsProperty(word) {\n  word = word.toLowerCase();\n  return word in propertyKeywords || word in fontProperties;\n}\n\nfunction wordIsBlock(word) {\n  return word.toLowerCase() in blockKeywords;\n}\n\nfunction wordIsVendorPrefix(word) {\n  return word.toLowerCase().match(vendorPrefixesRegexp);\n}\n\nfunction wordAsValue(word) {\n  var wordLC = word.toLowerCase();\n  var override = \"variable\";\n  if (wordIsTag(word)) override = \"tag\";\n  else if (wordIsBlock(word)) override = \"block-keyword\";\n  else if (wordIsProperty(word)) override = \"property\";\n  else if (wordLC in valueKeywords || wordLC in commonAtoms) override = \"atom\";\n  else if (wordLC == \"return\" || wordLC in colorKeywords) override = \"keyword\";\n\n  // Font family\n  else if (word.match(/^[A-Z]/)) override = \"string\";\n  return override;\n}\n\nfunction typeIsBlock(type, stream) {\n  return ((endOfLine(stream) && (type == \"{\" || type == \"]\" || type == \"hash\" || type == \"qualifier\")) || type == \"block-mixin\");\n}\n\nfunction typeIsInterpolation(type, stream) {\n  return type == \"{\" && stream.match(/^\\s*\\$?[\\w-]+/i, false);\n}\n\nfunction typeIsPseudo(type, stream) {\n  return type == \":\" && stream.match(/^[a-z-]+/, false);\n}\n\nfunction startOfLine(stream) {\n  return stream.sol() || stream.string.match(new RegExp(\"^\\\\s*\" + escapeRegExp(stream.current())));\n}\n\nfunction endOfLine(stream) {\n  return stream.eol() || stream.match(/^\\s*$/, false);\n}\n\nfunction firstWordOfLine(line) {\n  var re = /^\\s*[-_]*[a-z0-9]+[\\w-]*/i;\n  var result = typeof line == \"string\" ? line.match(re) : line.string.match(re);\n  return result ? result[0].replace(/^\\s*/, \"\") : \"\";\n}\n\n\n/**\n * Block\n */\nstates.block = function(type, stream, state) {\n  if ((type == \"comment\" && startOfLine(stream)) ||\n      (type == \",\" && endOfLine(stream)) ||\n      type == \"mixin\") {\n    return pushContext(state, stream, \"block\", 0);\n  }\n  if (typeIsInterpolation(type, stream)) {\n    return pushContext(state, stream, \"interpolation\");\n  }\n  if (endOfLine(stream) && type == \"]\") {\n    if (!/^\\s*(\\.|#|:|\\[|\\*|&)/.test(stream.string) && !wordIsTag(firstWordOfLine(stream))) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n  }\n  if (typeIsBlock(type, stream)) {\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"}\" && endOfLine(stream)) {\n    return pushContext(state, stream, \"block\", 0);\n  }\n  if (type == \"variable-name\") {\n    if (stream.string.match(/^\\s?\\$[\\w-\\.\\[\\]\\'\\\"]+$/) || wordIsBlock(firstWordOfLine(stream))) {\n      return pushContext(state, stream, \"variableName\");\n    }\n    else {\n      return pushContext(state, stream, \"variableName\", 0);\n    }\n  }\n  if (type == \"=\") {\n    if (!endOfLine(stream) && !wordIsBlock(firstWordOfLine(stream))) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"*\") {\n    if (endOfLine(stream) || stream.match(/\\s*(,|\\.|#|\\[|:|{)/,false)) {\n      override = \"tag\";\n      return pushContext(state, stream, \"block\");\n    }\n  }\n  if (typeIsPseudo(type, stream)) {\n    return pushContext(state, stream, \"pseudo\");\n  }\n  if (/@(font-face|media|supports|(-moz-)?document)/.test(type)) {\n    return pushContext(state, stream, endOfLine(stream) ? \"block\" : \"atBlock\");\n  }\n  if (/@(-(moz|ms|o|webkit)-)?keyframes$/.test(type)) {\n    return pushContext(state, stream, \"keyframes\");\n  }\n  if (/@extends?/.test(type)) {\n    return pushContext(state, stream, \"extend\", 0);\n  }\n  if (type && type.charAt(0) == \"@\") {\n\n    // Property Lookup\n    if (stream.indentation() > 0 && wordIsProperty(stream.current().slice(1))) {\n      override = \"variable\";\n      return \"block\";\n    }\n    if (/(@import|@require|@charset)/.test(type)) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"reference\" && endOfLine(stream)) {\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"(\") {\n    return pushContext(state, stream, \"parens\");\n  }\n\n  if (type == \"vendor-prefixes\") {\n    return pushContext(state, stream, \"vendorPrefixes\");\n  }\n  if (type == \"word\") {\n    var word = stream.current();\n    override = wordAsValue(word);\n\n    if (override == \"property\") {\n      if (startOfLine(stream)) {\n        return pushContext(state, stream, \"block\", 0);\n      } else {\n        override = \"atom\";\n        return \"block\";\n      }\n    }\n\n    if (override == \"tag\") {\n\n      // tag is a css value\n      if (/embed|menu|pre|progress|sub|table/.test(word)) {\n        if (wordIsProperty(firstWordOfLine(stream))) {\n          override = \"atom\";\n          return \"block\";\n        }\n      }\n\n      // tag is an attribute\n      if (stream.string.match(new RegExp(\"\\\\[\\\\s*\" + word + \"|\" + word +\"\\\\s*\\\\]\"))) {\n        override = \"atom\";\n        return \"block\";\n      }\n\n      // tag is a variable\n      if (tagVariablesRegexp.test(word)) {\n        if ((startOfLine(stream) && stream.string.match(/=/)) ||\n            (!startOfLine(stream) &&\n             !stream.string.match(/^(\\s*\\.|#|\\&|\\[|\\/|>|\\*)/) &&\n             !wordIsTag(firstWordOfLine(stream)))) {\n          override = \"variable\";\n          if (wordIsBlock(firstWordOfLine(stream)))  return \"block\";\n          return pushContext(state, stream, \"block\", 0);\n        }\n      }\n\n      if (endOfLine(stream)) return pushContext(state, stream, \"block\");\n    }\n    if (override == \"block-keyword\") {\n      override = \"keyword\";\n\n      // Postfix conditionals\n      if (stream.current(/(if|unless)/) && !startOfLine(stream)) {\n        return \"block\";\n      }\n      return pushContext(state, stream, \"block\");\n    }\n    if (word == \"return\") return pushContext(state, stream, \"block\", 0);\n\n    // Placeholder selector\n    if (override == \"variable\" && stream.string.match(/^\\s?\\$[\\w-\\.\\[\\]\\'\\\"]+$/)) {\n      return pushContext(state, stream, \"block\");\n    }\n  }\n  return state.context.type;\n};\n\n\n/**\n * Parens\n */\nstates.parens = function(type, stream, state) {\n  if (type == \"(\") return pushContext(state, stream, \"parens\");\n  if (type == \")\") {\n    if (state.context.prev.type == \"parens\") {\n      return popContext(state, stream);\n    }\n    if ((stream.string.match(/^[a-z][\\w-]*\\(/i) && endOfLine(stream)) ||\n        wordIsBlock(firstWordOfLine(stream)) ||\n        /(\\.|#|:|\\[|\\*|&|>|~|\\+|\\/)/.test(firstWordOfLine(stream)) ||\n        (!stream.string.match(/^-?[a-z][\\w-\\.\\[\\]\\'\\\"]*\\s*=/) &&\n         wordIsTag(firstWordOfLine(stream)))) {\n      return pushContext(state, stream, \"block\");\n    }\n    if (stream.string.match(/^[\\$-]?[a-z][\\w-\\.\\[\\]\\'\\\"]*\\s*=/) ||\n        stream.string.match(/^\\s*(\\(|\\)|[0-9])/) ||\n        stream.string.match(/^\\s+[a-z][\\w-]*\\(/i) ||\n        stream.string.match(/^\\s+[\\$-]?[a-z]/i)) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n    if (endOfLine(stream)) return pushContext(state, stream, \"block\");\n    else return pushContext(state, stream, \"block\", 0);\n  }\n  if (type && type.charAt(0) == \"@\" && wordIsProperty(stream.current().slice(1))) {\n    override = \"variable\";\n  }\n  if (type == \"word\") {\n    var word = stream.current();\n    override = wordAsValue(word);\n    if (override == \"tag\" && tagVariablesRegexp.test(word)) {\n      override = \"variable\";\n    }\n    if (override == \"property\" || word == \"to\") override = \"atom\";\n  }\n  if (type == \"variable-name\") {\n    return pushContext(state, stream, \"variableName\");\n  }\n  if (typeIsPseudo(type, stream)) {\n    return pushContext(state, stream, \"pseudo\");\n  }\n  return state.context.type;\n};\n\n\n/**\n * Vendor prefixes\n */\nstates.vendorPrefixes = function(type, stream, state) {\n  if (type == \"word\") {\n    override = \"property\";\n    return pushContext(state, stream, \"block\", 0);\n  }\n  return popContext(state, stream);\n};\n\n\n/**\n * Pseudo\n */\nstates.pseudo = function(type, stream, state) {\n  if (!wordIsProperty(firstWordOfLine(stream.string))) {\n    stream.match(/^[a-z-]+/);\n    override = \"variableName.special\";\n    if (endOfLine(stream)) return pushContext(state, stream, \"block\");\n    return popContext(state, stream);\n  }\n  return popAndPass(type, stream, state);\n};\n\n\n/**\n * atBlock\n */\nstates.atBlock = function(type, stream, state) {\n  if (type == \"(\") return pushContext(state, stream, \"atBlock_parens\");\n  if (typeIsBlock(type, stream)) {\n    return pushContext(state, stream, \"block\");\n  }\n  if (typeIsInterpolation(type, stream)) {\n    return pushContext(state, stream, \"interpolation\");\n  }\n  if (type == \"word\") {\n    var word = stream.current().toLowerCase();\n    if (/^(only|not|and|or)$/.test(word))\n      override = \"keyword\";\n    else if (documentTypes.hasOwnProperty(word))\n      override = \"tag\";\n    else if (mediaTypes.hasOwnProperty(word))\n      override = \"attribute\";\n    else if (mediaFeatures.hasOwnProperty(word))\n      override = \"property\";\n    else if (nonStandardPropertyKeywords.hasOwnProperty(word))\n      override = \"string.special\";\n    else override = wordAsValue(stream.current());\n    if (override == \"tag\" && endOfLine(stream)) {\n      return pushContext(state, stream, \"block\");\n    }\n  }\n  if (type == \"operator\" && /^(not|and|or)$/.test(stream.current())) {\n    override = \"keyword\";\n  }\n  return state.context.type;\n};\n\nstates.atBlock_parens = function(type, stream, state) {\n  if (type == \"{\" || type == \"}\") return state.context.type;\n  if (type == \")\") {\n    if (endOfLine(stream)) return pushContext(state, stream, \"block\");\n    else return pushContext(state, stream, \"atBlock\");\n  }\n  if (type == \"word\") {\n    var word = stream.current().toLowerCase();\n    override = wordAsValue(word);\n    if (/^(max|min)/.test(word)) override = \"property\";\n    if (override == \"tag\") {\n      tagVariablesRegexp.test(word) ? override = \"variable\" : override = \"atom\";\n    }\n    return state.context.type;\n  }\n  return states.atBlock(type, stream, state);\n};\n\n\n/**\n * Keyframes\n */\nstates.keyframes = function(type, stream, state) {\n  if (stream.indentation() == \"0\" && ((type == \"}\" && startOfLine(stream)) || type == \"]\" || type == \"hash\"\n                                      || type == \"qualifier\" || wordIsTag(stream.current()))) {\n    return popAndPass(type, stream, state);\n  }\n  if (type == \"{\") return pushContext(state, stream, \"keyframes\");\n  if (type == \"}\") {\n    if (startOfLine(stream)) return popContext(state, stream, true);\n    else return pushContext(state, stream, \"keyframes\");\n  }\n  if (type == \"unit\" && /^[0-9]+\\%$/.test(stream.current())) {\n    return pushContext(state, stream, \"keyframes\");\n  }\n  if (type == \"word\") {\n    override = wordAsValue(stream.current());\n    if (override == \"block-keyword\") {\n      override = \"keyword\";\n      return pushContext(state, stream, \"keyframes\");\n    }\n  }\n  if (/@(font-face|media|supports|(-moz-)?document)/.test(type)) {\n    return pushContext(state, stream, endOfLine(stream) ? \"block\" : \"atBlock\");\n  }\n  if (type == \"mixin\") {\n    return pushContext(state, stream, \"block\", 0);\n  }\n  return state.context.type;\n};\n\n\n/**\n * Interpolation\n */\nstates.interpolation = function(type, stream, state) {\n  if (type == \"{\") popContext(state, stream) && pushContext(state, stream, \"block\");\n  if (type == \"}\") {\n    if (stream.string.match(/^\\s*(\\.|#|:|\\[|\\*|&|>|~|\\+|\\/)/i) ||\n        (stream.string.match(/^\\s*[a-z]/i) && wordIsTag(firstWordOfLine(stream)))) {\n      return pushContext(state, stream, \"block\");\n    }\n    if (!stream.string.match(/^(\\{|\\s*\\&)/) ||\n        stream.match(/\\s*[\\w-]/,false)) {\n      return pushContext(state, stream, \"block\", 0);\n    }\n    return pushContext(state, stream, \"block\");\n  }\n  if (type == \"variable-name\") {\n    return pushContext(state, stream, \"variableName\", 0);\n  }\n  if (type == \"word\") {\n    override = wordAsValue(stream.current());\n    if (override == \"tag\") override = \"atom\";\n  }\n  return state.context.type;\n};\n\n\n/**\n * Extend/s\n */\nstates.extend = function(type, stream, state) {\n  if (type == \"[\" || type == \"=\") return \"extend\";\n  if (type == \"]\") return popContext(state, stream);\n  if (type == \"word\") {\n    override = wordAsValue(stream.current());\n    return \"extend\";\n  }\n  return popContext(state, stream);\n};\n\n\n/**\n * Variable name\n */\nstates.variableName = function(type, stream, state) {\n  if (type == \"string\" || type == \"[\" || type == \"]\" || stream.current().match(/^(\\.|\\$)/)) {\n    if (stream.current().match(/^\\.[\\w-]+/i)) override = \"variable\";\n    return \"variableName\";\n  }\n  return popAndPass(type, stream, state);\n};\n\nexport const stylus = {\n  name: \"stylus\",\n  startState: function() {\n    return {\n      tokenize: null,\n      state: \"block\",\n      context: new Context(\"block\", 0, null)\n    };\n  },\n  token: function(stream, state) {\n    if (!state.tokenize && stream.eatSpace()) return null;\n    style = (state.tokenize || tokenBase)(stream, state);\n    if (style && typeof style == \"object\") {\n      type = style[1];\n      style = style[0];\n    }\n    override = style;\n    state.state = states[state.state](type, stream, state);\n    return override;\n  },\n  indent: function(state, textAfter, iCx) {\n    var cx = state.context,\n        ch = textAfter && textAfter.charAt(0),\n        indent = cx.indent,\n        lineFirstWord = firstWordOfLine(textAfter),\n        lineIndent = cx.line.indent,\n        prevLineFirstWord = state.context.prev ? state.context.prev.line.firstWord : \"\",\n        prevLineIndent = state.context.prev ? state.context.prev.line.indent : lineIndent;\n\n    if (cx.prev &&\n        (ch == \"}\" && (cx.type == \"block\" || cx.type == \"atBlock\" || cx.type == \"keyframes\") ||\n         ch == \")\" && (cx.type == \"parens\" || cx.type == \"atBlock_parens\") ||\n         ch == \"{\" && (cx.type == \"at\"))) {\n      indent = cx.indent - iCx.unit;\n    } else if (!(/(\\})/.test(ch))) {\n      if (/@|\\$|\\d/.test(ch) ||\n          /^\\{/.test(textAfter) ||\n/^\\s*\\/(\\/|\\*)/.test(textAfter) ||\n          /^\\s*\\/\\*/.test(prevLineFirstWord) ||\n          /^\\s*[\\w-\\.\\[\\]\\'\\\"]+\\s*(\\?|:|\\+)?=/i.test(textAfter) ||\n          /^(\\+|-)?[a-z][\\w-]*\\(/i.test(textAfter) ||\n          /^return/.test(textAfter) ||\n          wordIsBlock(lineFirstWord)) {\n        indent = lineIndent;\n      } else if (/(\\.|#|:|\\[|\\*|&|>|~|\\+|\\/)/.test(ch) || wordIsTag(lineFirstWord)) {\n        if (/\\,\\s*$/.test(prevLineFirstWord)) {\n          indent = prevLineIndent;\n        } else if (/(\\.|#|:|\\[|\\*|&|>|~|\\+|\\/)/.test(prevLineFirstWord) || wordIsTag(prevLineFirstWord)) {\n          indent = lineIndent <= prevLineIndent ? prevLineIndent : prevLineIndent + iCx.unit;\n        } else {\n          indent = lineIndent;\n        }\n      } else if (!/,\\s*$/.test(textAfter) && (wordIsVendorPrefix(lineFirstWord) || wordIsProperty(lineFirstWord))) {\n        if (wordIsBlock(prevLineFirstWord)) {\n          indent = lineIndent <= prevLineIndent ? prevLineIndent : prevLineIndent + iCx.unit;\n        } else if (/^\\{/.test(prevLineFirstWord)) {\n          indent = lineIndent <= prevLineIndent ? lineIndent : prevLineIndent + iCx.unit;\n        } else if (wordIsVendorPrefix(prevLineFirstWord) || wordIsProperty(prevLineFirstWord)) {\n          indent = lineIndent >= prevLineIndent ? prevLineIndent : lineIndent;\n        } else if (/^(\\.|#|:|\\[|\\*|&|@|\\+|\\-|>|~|\\/)/.test(prevLineFirstWord) ||\n                   /=\\s*$/.test(prevLineFirstWord) ||\n                   wordIsTag(prevLineFirstWord) ||\n                   /^\\$[\\w-\\.\\[\\]\\'\\\"]/.test(prevLineFirstWord)) {\n          indent = prevLineIndent + iCx.unit;\n        } else {\n          indent = lineIndent;\n        }\n      }\n    }\n    return indent;\n  },\n  languageData: {\n    indentOnInput: /^\\s*\\}$/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}},\n    autocomplete: hintWords\n  }\n};\n"], "names": [], "sourceRoot": ""}