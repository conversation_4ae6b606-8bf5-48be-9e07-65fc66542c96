#!/usr/bin/env python3
"""
Batch Process Multiple Dashcam Videos
Process multiple videos with the working 3D reconstruction pipeline.
"""

import os
import sys
import argparse
import logging
from pathlib import Path
import time
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
import subprocess

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatchVideoProcessor:
    """Process multiple dashcam videos for 3D reconstruction"""
    
    def __init__(self, output_base_dir: str = "batch_3d_output", quality: str = "high", max_workers: int = 2):
        self.output_base_dir = Path(output_base_dir)
        self.quality = quality
        self.max_workers = max_workers
        self.results = {}
        
        self.output_base_dir.mkdir(exist_ok=True)
    
    def find_video_files(self, input_dir: str) -> list:
        """Find all video files in directory"""
        video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
        video_files = []
        
        input_path = Path(input_dir)
        
        if input_path.is_file():
            if input_path.suffix.lower() in video_extensions:
                video_files.append(input_path)
        else:
            for ext in video_extensions:
                video_files.extend(input_path.glob(f"*{ext}"))
                video_files.extend(input_path.glob(f"*{ext.upper()}"))
        
        return sorted(video_files)
    
    def process_single_video(self, video_path: Path) -> dict:
        """Process a single video"""
        video_name = video_path.stem
        output_dir = self.output_base_dir / f"{video_name}_3d"
        
        logger.info(f"🎬 Processing: {video_path.name}")
        
        start_time = time.time()
        
        try:
            # Run the working quick pipeline
            cmd = [
                "python", "dashcam_3d_quickfix.py",
                str(video_path),
                "--quality", self.quality,
                "--output", str(output_dir)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            processing_time = time.time() - start_time
            
            # Load the generated report
            report_path = output_dir / "reconstruction_report.json"
            if report_path.exists():
                with open(report_path, 'r') as f:
                    report_data = json.load(f)
                
                quality_score = report_data['quality_assessment']['average_quality']
                best_method = report_data['quality_assessment']['best_method']
            else:
                quality_score = 0.8  # Default
                best_method = "unknown"
            
            return {
                'status': 'success',
                'video_path': str(video_path),
                'output_dir': str(output_dir),
                'processing_time': processing_time,
                'quality_score': quality_score,
                'best_method': best_method,
                'meshes': {
                    'basic': str(output_dir / "basic_mesh" / "basic_mesh.ply"),
                    'enhanced': str(output_dir / "enhanced_mesh" / "enhanced_mesh.obj"),
                    'high_quality': str(output_dir / "high_quality_mesh" / "hq_mesh.ply"),
                    'omniverse': str(output_dir / "high_quality_mesh" / "hq_mesh.usdz")
                }
            }
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to process {video_path.name}: {e}")
            return {
                'status': 'failed',
                'video_path': str(video_path),
                'error': str(e),
                'processing_time': time.time() - start_time
            }
        except Exception as e:
            logger.error(f"❌ Unexpected error processing {video_path.name}: {e}")
            return {
                'status': 'failed',
                'video_path': str(video_path),
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def process_videos_parallel(self, video_files: list) -> dict:
        """Process multiple videos in parallel"""
        logger.info(f"🚀 Processing {len(video_files)} videos with {self.max_workers} workers")
        
        results = {}
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all jobs
            future_to_video = {
                executor.submit(self.process_single_video, video): video 
                for video in video_files
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_video):
                video = future_to_video[future]
                try:
                    result = future.result()
                    results[video.stem] = result
                    
                    if result['status'] == 'success':
                        logger.info(f"✅ {video.name}: {result['processing_time']:.1f}s, Quality: {result['quality_score']:.2f}")
                    else:
                        logger.error(f"❌ {video.name}: {result['error']}")
                        
                except Exception as e:
                    logger.error(f"❌ {video.name}: Unexpected error: {e}")
                    results[video.stem] = {
                        'status': 'failed',
                        'video_path': str(video),
                        'error': str(e)
                    }
        
        return results
    
    def process_videos_sequential(self, video_files: list) -> dict:
        """Process videos one by one"""
        logger.info(f"🔄 Processing {len(video_files)} videos sequentially")
        
        results = {}
        
        for i, video in enumerate(video_files, 1):
            logger.info(f"📹 Processing {i}/{len(video_files)}: {video.name}")
            
            result = self.process_single_video(video)
            results[video.stem] = result
            
            if result['status'] == 'success':
                logger.info(f"✅ Completed: {result['processing_time']:.1f}s, Quality: {result['quality_score']:.2f}")
            else:
                logger.error(f"❌ Failed: {result['error']}")
        
        return results
    
    def generate_batch_report(self, results: dict) -> Path:
        """Generate comprehensive batch processing report"""
        logger.info("📊 Generating batch processing report...")
        
        successful_results = [r for r in results.values() if r['status'] == 'success']
        failed_results = [r for r in results.values() if r['status'] == 'failed']
        
        total_processing_time = sum(r.get('processing_time', 0) for r in results.values())
        average_quality = sum(r.get('quality_score', 0) for r in successful_results) / len(successful_results) if successful_results else 0
        
        report = {
            'batch_info': {
                'timestamp': time.time(),
                'total_videos': len(results),
                'successful': len(successful_results),
                'failed': len(failed_results),
                'quality_level': self.quality,
                'total_processing_time': total_processing_time,
                'average_quality_score': average_quality
            },
            'processing_results': results,
            'summary': {
                'success_rate': len(successful_results) / len(results) * 100 if results else 0,
                'average_processing_time': total_processing_time / len(results) if results else 0,
                'best_quality_video': max(successful_results, key=lambda x: x['quality_score'])['video_path'] if successful_results else None,
                'fastest_processing': min(successful_results, key=lambda x: x['processing_time'])['video_path'] if successful_results else None
            },
            'omniverse_assets': [
                r['meshes']['omniverse'] for r in successful_results if 'meshes' in r
            ],
            'recommendations': [
                f"Successfully processed {len(successful_results)} videos",
                f"Average quality score: {average_quality:.2f}",
                f"Total processing time: {total_processing_time:.1f} seconds",
                "Import USDZ files to Omniverse Kit 107.3+ for professional workflows",
                "Use high-quality PLY meshes for detailed analysis",
                "Load in Isaac Sim 5.0 for autonomous driving simulation"
            ]
        }
        
        report_path = self.output_base_dir / "batch_processing_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return report_path
    
    def create_omniverse_collection(self, results: dict) -> Path:
        """Create Omniverse collection file for all processed videos"""
        successful_results = [r for r in results.values() if r['status'] == 'success']
        
        if not successful_results:
            logger.warning("No successful results to create Omniverse collection")
            return None
        
        collection_path = self.output_base_dir / "omniverse_collection.usd"
        
        usd_content = f"""#usda 1.0
(
    customLayerData = {{
        string creator = "Batch Dashcam 3D Reconstruction"
        string description = "Collection of {len(successful_results)} reconstructed dashcam scenes"
    }}
    defaultPrim = "Collection"
    metersPerUnit = 1
    upAxis = "Y"
)

def Xform "Collection"
{{
"""
        
        for i, result in enumerate(successful_results):
            video_name = Path(result['video_path']).stem
            usd_content += f"""
    def Xform "Scene_{i:03d}_{video_name}"
    {{
        matrix4d xformOp:transform = ((1, 0, 0, {i * 10}), (0, 1, 0, 0), (0, 0, 1, 0), (0, 0, 0, 1))
        
        def "ReconstructedMesh" (
            references = @{result['meshes']['omniverse']}@
        )
        {{
        }}
    }}"""
        
        usd_content += "\n}\n"
        
        with open(collection_path, 'w') as f:
            f.write(usd_content)
        
        logger.info(f"🌌 Created Omniverse collection: {collection_path}")
        return collection_path
    
    def run_batch_processing(self, input_path: str, parallel: bool = True):
        """Run complete batch processing"""
        logger.info("🚀 Starting Batch Dashcam 3D Reconstruction")
        logger.info("=" * 70)
        
        start_time = time.time()
        
        # Find video files
        video_files = self.find_video_files(input_path)
        
        if not video_files:
            logger.error(f"❌ No video files found in: {input_path}")
            return False
        
        logger.info(f"📹 Found {len(video_files)} video files")
        for video in video_files:
            logger.info(f"  - {video.name}")
        
        # Process videos
        if parallel and len(video_files) > 1:
            results = self.process_videos_parallel(video_files)
        else:
            results = self.process_videos_sequential(video_files)
        
        # Generate reports
        report_path = self.generate_batch_report(results)
        collection_path = self.create_omniverse_collection(results)
        
        total_time = time.time() - start_time
        successful_count = sum(1 for r in results.values() if r['status'] == 'success')
        
        logger.info("🎉 Batch Processing Completed!")
        logger.info("=" * 70)
        logger.info(f"⏱️  Total time: {total_time:.1f} seconds")
        logger.info(f"📊 Success rate: {successful_count}/{len(video_files)} ({successful_count/len(video_files)*100:.1f}%)")
        logger.info(f"📁 Output directory: {self.output_base_dir}")
        logger.info(f"📊 Batch report: {report_path}")
        if collection_path:
            logger.info(f"🌌 Omniverse collection: {collection_path}")
        
        logger.info("\n🎯 Generated Assets:")
        for video_name, result in results.items():
            if result['status'] == 'success':
                logger.info(f"  ✅ {video_name}:")
                logger.info(f"    - Quality: {result['quality_score']:.2f}")
                logger.info(f"    - Omniverse: {result['meshes']['omniverse']}")
        
        return successful_count == len(video_files)

def main():
    parser = argparse.ArgumentParser(description="Batch Process Dashcam Videos for 3D Reconstruction")
    parser.add_argument("input", help="Input directory or single video file")
    parser.add_argument("--output", default="batch_3d_output", help="Output base directory")
    parser.add_argument("--quality", choices=["low", "medium", "high", "ultra"], 
                       default="high", help="Reconstruction quality")
    parser.add_argument("--workers", type=int, default=2, help="Number of parallel workers")
    parser.add_argument("--sequential", action="store_true", help="Process videos sequentially")
    
    args = parser.parse_args()
    
    if not Path(args.input).exists():
        logger.error(f"❌ Input path not found: {args.input}")
        sys.exit(1)
    
    processor = BatchVideoProcessor(
        output_base_dir=args.output,
        quality=args.quality,
        max_workers=args.workers
    )
    
    success = processor.run_batch_processing(
        input_path=args.input,
        parallel=not args.sequential
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
