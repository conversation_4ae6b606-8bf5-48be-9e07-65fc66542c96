#!/usr/bin/env python3
"""
NVIDIA Research Tools Environment Setup
Sets up cutting-edge NVIDIA research tools for high-quality 3D reconstruction.

This script installs and configures:
- 3DGRUT (SIGGRAPH Asia 2024)
- SCube (NeurIPS 2024) 
- XCube
- NKSR (CVPR 2023)
- FlexiCubes
- Omniverse Integration
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import logging
import json
import urllib.request
import zipfile
import tarfile

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NVIDIAResearchEnvironmentSetup:
    """Setup NVIDIA research tools environment"""
    
    def __init__(self, workspace_dir: str = "."):
        self.workspace = Path(workspace_dir).resolve()
        self.env_name = "nvidia_3d_research"
        self.python_version = "3.9"
        
        # Tool configurations
        self.tools_config = {
            '3dgrut': {
                'repo': 'https://github.com/nv-tlabs/3dgrut.git',
                'branch': 'main',
                'requirements': ['torch>=2.0.0', 'torchvision', 'cuda-toolkit=11.8'],
                'build_required': True
            },
            'scube': {
                'repo': 'https://github.com/nv-tlabs/SCube.git', 
                'branch': 'main',
                'requirements': ['torch>=1.12.0', 'mmcv>=2.0.0', 'mmsegmentation>=1.0.0'],
                'build_required': False
            },
            'xcube': {
                'repo': 'https://github.com/nv-tlabs/XCube.git',
                'branch': 'main', 
                'requirements': ['torch>=1.12.0', 'numpy', 'scipy'],
                'build_required': False
            },
            'nksr': {
                'repo': 'https://github.com/nv-tlabs/NKSR.git',
                'branch': 'main',
                'requirements': ['torch>=2.0.0', 'open3d', 'trimesh'],
                'build_required': False
            },
            'flexicubes': {
                'repo': 'https://github.com/nv-tlabs/FlexiCubes.git',
                'branch': 'main',
                'requirements': ['kaolin>=0.15.0', 'nvdiffrast'],
                'build_required': False
            }
        }
    
    def setup_conda_environment(self):
        """Create and setup conda environment"""
        logger.info(f"🐍 Creating conda environment: {self.env_name}")
        
        # Create conda environment
        cmd = [
            "conda", "create", "-n", self.env_name, 
            f"python={self.python_version}", "-y"
        ]
        subprocess.run(cmd, check=True)
        
        # Activate environment and install base packages
        self._run_in_conda_env([
            "conda", "install", "-y", 
            "pytorch", "torchvision", "torchaudio", 
            "pytorch-cuda=11.8", "-c", "pytorch", "-c", "nvidia"
        ])
        
        # Install additional packages
        self._run_in_conda_env([
            "pip", "install", 
            "opencv-python", "numpy", "scipy", "matplotlib",
            "trimesh", "open3d", "imageio", "tqdm",
            "wandb", "tensorboard", "rich"
        ])
        
        logger.info("✅ Conda environment created successfully")
    
    def clone_nvidia_repositories(self):
        """Clone all NVIDIA research repositories"""
        logger.info("📥 Cloning NVIDIA research repositories...")
        
        for tool_name, config in self.tools_config.items():
            tool_dir = self.workspace / f"{tool_name}-main"
            
            if tool_dir.exists():
                logger.info(f"⏭️  {tool_name} already exists, skipping...")
                continue
            
            logger.info(f"📥 Cloning {tool_name}...")
            cmd = [
                "git", "clone", "--recursive", 
                config['repo'], str(tool_dir)
            ]
            
            try:
                subprocess.run(cmd, check=True)
                logger.info(f"✅ {tool_name} cloned successfully")
            except subprocess.CalledProcessError as e:
                logger.error(f"❌ Failed to clone {tool_name}: {e}")
    
    def install_tool_dependencies(self):
        """Install dependencies for each tool"""
        logger.info("📦 Installing tool dependencies...")
        
        for tool_name, config in self.tools_config.items():
            tool_dir = self.workspace / f"{tool_name}-main"
            
            if not tool_dir.exists():
                logger.warning(f"⚠️  {tool_name} directory not found, skipping...")
                continue
            
            logger.info(f"📦 Installing {tool_name} dependencies...")
            
            # Install from requirements.txt if exists
            req_file = tool_dir / "requirements.txt"
            if req_file.exists():
                self._run_in_conda_env([
                    "pip", "install", "-r", str(req_file)
                ])
            
            # Install specific requirements
            for req in config['requirements']:
                try:
                    if 'cuda-toolkit' in req:
                        self._run_in_conda_env([
                            "conda", "install", "-y", req, "-c", "nvidia"
                        ])
                    elif 'kaolin' in req:
                        self._install_kaolin()
                    elif 'nvdiffrast' in req:
                        self._install_nvdiffrast()
                    else:
                        self._run_in_conda_env([
                            "pip", "install", req
                        ])
                except subprocess.CalledProcessError as e:
                    logger.warning(f"⚠️  Failed to install {req}: {e}")
    
    def build_tools(self):
        """Build tools that require compilation"""
        logger.info("🔨 Building tools that require compilation...")
        
        # Build 3DGRUT
        grut_dir = self.workspace / "3dgrut-main"
        if grut_dir.exists():
            logger.info("🔨 Building 3DGRUT...")
            self._build_3dgrut(grut_dir)
        
        # Build other tools as needed
        logger.info("✅ Tool building completed")
    
    def setup_omniverse_integration(self):
        """Setup Omniverse integration"""
        logger.info("🌌 Setting up Omniverse integration...")
        
        # Install USD Python bindings
        try:
            self._run_in_conda_env([
                "pip", "install", "usd-core"
            ])
            logger.info("✅ USD Python bindings installed")
        except subprocess.CalledProcessError:
            logger.warning("⚠️  USD Python bindings installation failed")
        
        # Create Omniverse configuration
        omni_config = {
            'omniverse_kit_version': '107.3+',
            'isaac_sim_version': '5.0+',
            'usd_version': '23.08',
            'features': {
                'gaussian_splatting': True,
                'ray_tracing': True,
                'physics_simulation': True
            }
        }
        
        config_path = self.workspace / 'omniverse_config.json'
        with open(config_path, 'w') as f:
            json.dump(omni_config, f, indent=2)
        
        logger.info("✅ Omniverse integration setup completed")
    
    def _run_in_conda_env(self, cmd):
        """Run command in conda environment"""
        if os.name == 'nt':  # Windows
            conda_cmd = ["conda", "run", "-n", self.env_name] + cmd
        else:  # Linux/Mac
            conda_cmd = ["conda", "run", "-n", self.env_name] + cmd
        
        subprocess.run(conda_cmd, check=True)
    
    def _install_kaolin(self):
        """Install Kaolin with CUDA support"""
        logger.info("📦 Installing Kaolin...")
        self._run_in_conda_env([
            "pip", "install", "kaolin==0.15.0", 
            "-f", "https://nvidia-kaolin.s3.us-east-2.amazonaws.com/torch-2.0.0_cu118.html"
        ])
    
    def _install_nvdiffrast(self):
        """Install nvdiffrast"""
        logger.info("📦 Installing nvdiffrast...")
        self._run_in_conda_env([
            "pip", "install", "git+https://github.com/NVlabs/nvdiffrast/"
        ])
    
    def _build_3dgrut(self, grut_dir: Path):
        """Build 3DGRUT with CUDA extensions"""
        logger.info("🔨 Building 3DGRUT CUDA extensions...")
        
        # Run the install script
        install_script = grut_dir / "install_env.sh"
        if install_script.exists():
            subprocess.run([
                "bash", str(install_script), self.env_name
            ], cwd=grut_dir, check=True)
        else:
            # Manual build
            self._run_in_conda_env([
                "pip", "install", "-e", "."
            ])
    
    def create_activation_script(self):
        """Create environment activation script"""
        logger.info("📝 Creating activation script...")
        
        if os.name == 'nt':  # Windows
            script_content = f"""@echo off
echo Activating NVIDIA 3D Research Environment...
call conda activate {self.env_name}
echo Environment activated! Ready for 3D reconstruction.
"""
            script_path = self.workspace / "activate_nvidia_env.bat"
        else:  # Linux/Mac
            script_content = f"""#!/bin/bash
echo "Activating NVIDIA 3D Research Environment..."
conda activate {self.env_name}
echo "Environment activated! Ready for 3D reconstruction."
"""
            script_path = self.workspace / "activate_nvidia_env.sh"
        
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        if os.name != 'nt':
            os.chmod(script_path, 0o755)
        
        logger.info(f"✅ Activation script created: {script_path}")
    
    def run_complete_setup(self):
        """Run complete environment setup"""
        logger.info("🚀 Starting NVIDIA Research Environment Setup")
        logger.info("=" * 60)
        
        try:
            self.setup_conda_environment()
            self.clone_nvidia_repositories()
            self.install_tool_dependencies()
            self.build_tools()
            self.setup_omniverse_integration()
            self.create_activation_script()
            
            logger.info("🎉 Setup completed successfully!")
            logger.info("=" * 60)
            logger.info("Next steps:")
            logger.info("1. Activate environment: conda activate nvidia_3d_research")
            logger.info("2. Test setup: python demo_test_setup.py")
            logger.info("3. Run reconstruction: python dashcam_to_3d_comprehensive.py your_video.mp4")
            
        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            sys.exit(1)

def main():
    import argparse
    parser = argparse.ArgumentParser(description="Setup NVIDIA Research Environment")
    parser.add_argument("--workspace", default=".", help="Workspace directory")
    args = parser.parse_args()
    
    setup = NVIDIAResearchEnvironmentSetup(args.workspace)
    setup.run_complete_setup()

if __name__ == "__main__":
    main()
