{"version": 3, "file": "3420.693f6432957cbf2699c5.js?v=693f6432957cbf2699c5", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,0CAA0C,EAAE;AAC5C,4DAA4D,EAAE;AAC9D;AACA,oCAAoC,EAAE;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uCAAuC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,YAAY,KAAK;;AAEjC;AACA;AACA;AACA;;AAEA;AACA,gDAAgD;AAChD;AACA;;AAEA,iDAAiD;AACjD,8BAA8B,KAAK;AACnC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA,MAAM;AACN;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA;AACA,QAAQ;AACR;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,mDAAmD,EAAE,iBAAiB,EAAE,GAAG,YAAY,IAAI,EAAE;AAC7F;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,qBAAqB;AACrB;AACA;;AAEA;AACA;AACA;;AAEA;AACA,iCAAiC;AACjC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;;AAEA,6BAA6B;AAC7B;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;;AAEA,4BAA4B;AAC5B,2CAA2C,KAAK;AAChD;AACA;;AAEA,qCAAqC;AACrC,2CAA2C,KAAK;AAChD;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;;AAEA,4BAA4B;AAC5B,2CAA2C,KAAK;AAChD;AACA;;AAEA,qCAAqC;AACrC,2CAA2C,KAAK;AAChD;AACA;;AAEA;AACA,QAAQ;AACR;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA,4CAA4C,gBAAgB;;AAE5D;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA,oBAAoB;AACpB;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/crystal.js"], "sourcesContent": ["function wordRegExp(words, end) {\n  return new RegExp((end ? \"\" : \"^\") + \"(?:\" + words.join(\"|\") + \")\" + (end ? \"$\" : \"\\\\b\"));\n}\n\nfunction chain(tokenize, stream, state) {\n  state.tokenize.push(tokenize);\n  return tokenize(stream, state);\n}\n\nvar operators = /^(?:[-+/%|&^]|\\*\\*?|[<>]{2})/;\nvar conditionalOperators = /^(?:[=!]~|===|<=>|[<>=!]=?|[|&]{2}|~)/;\nvar indexingOperators = /^(?:\\[\\][?=]?)/;\nvar anotherOperators = /^(?:\\.(?:\\.{2})?|->|[?:])/;\nvar idents = /^[a-z_\\u009F-\\uFFFF][a-zA-Z0-9_\\u009F-\\uFFFF]*/;\nvar types = /^[A-Z_\\u009F-\\uFFFF][a-zA-Z0-9_\\u009F-\\uFFFF]*/;\nvar keywords = wordRegExp([\n  \"abstract\", \"alias\", \"as\", \"asm\", \"begin\", \"break\", \"case\", \"class\", \"def\", \"do\",\n  \"else\", \"elsif\", \"end\", \"ensure\", \"enum\", \"extend\", \"for\", \"fun\", \"if\",\n  \"include\", \"instance_sizeof\", \"lib\", \"macro\", \"module\", \"next\", \"of\", \"out\", \"pointerof\",\n  \"private\", \"protected\", \"rescue\", \"return\", \"require\", \"select\", \"sizeof\", \"struct\",\n  \"super\", \"then\", \"type\", \"typeof\", \"uninitialized\", \"union\", \"unless\", \"until\", \"when\", \"while\", \"with\",\n  \"yield\", \"__DIR__\", \"__END_LINE__\", \"__FILE__\", \"__LINE__\"\n]);\nvar atomWords = wordRegExp([\"true\", \"false\", \"nil\", \"self\"]);\nvar indentKeywordsArray = [\n  \"def\", \"fun\", \"macro\",\n  \"class\", \"module\", \"struct\", \"lib\", \"enum\", \"union\",\n  \"do\", \"for\"\n];\nvar indentKeywords = wordRegExp(indentKeywordsArray);\nvar indentExpressionKeywordsArray = [\"if\", \"unless\", \"case\", \"while\", \"until\", \"begin\", \"then\"];\nvar indentExpressionKeywords = wordRegExp(indentExpressionKeywordsArray);\nvar dedentKeywordsArray = [\"end\", \"else\", \"elsif\", \"rescue\", \"ensure\"];\nvar dedentKeywords = wordRegExp(dedentKeywordsArray);\nvar dedentPunctualsArray = [\"\\\\)\", \"\\\\}\", \"\\\\]\"];\nvar dedentPunctuals = new RegExp(\"^(?:\" + dedentPunctualsArray.join(\"|\") + \")$\");\nvar nextTokenizer = {\n  \"def\": tokenFollowIdent, \"fun\": tokenFollowIdent, \"macro\": tokenMacroDef,\n  \"class\": tokenFollowType, \"module\": tokenFollowType, \"struct\": tokenFollowType,\n  \"lib\": tokenFollowType, \"enum\": tokenFollowType, \"union\": tokenFollowType\n};\nvar matching = {\"[\": \"]\", \"{\": \"}\", \"(\": \")\", \"<\": \">\"};\n\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  // Macros\n  if (state.lastToken != \"\\\\\" && stream.match(\"{%\", false)) {\n    return chain(tokenMacro(\"%\", \"%\"), stream, state);\n  }\n\n  if (state.lastToken != \"\\\\\" && stream.match(\"{{\", false)) {\n    return chain(tokenMacro(\"{\", \"}\"), stream, state);\n  }\n\n  // Comments\n  if (stream.peek() == \"#\") {\n    stream.skipToEnd();\n    return \"comment\";\n  }\n\n  // Variables and keywords\n  var matched;\n  if (stream.match(idents)) {\n    stream.eat(/[?!]/);\n\n    matched = stream.current();\n    if (stream.eat(\":\")) {\n      return \"atom\";\n    } else if (state.lastToken == \".\") {\n      return \"property\";\n    } else if (keywords.test(matched)) {\n      if (indentKeywords.test(matched)) {\n        if (!(matched == \"fun\" && state.blocks.indexOf(\"lib\") >= 0) && !(matched == \"def\" && state.lastToken == \"abstract\")) {\n          state.blocks.push(matched);\n          state.currentIndent += 1;\n        }\n      } else if ((state.lastStyle == \"operator\" || !state.lastStyle) && indentExpressionKeywords.test(matched)) {\n        state.blocks.push(matched);\n        state.currentIndent += 1;\n      } else if (matched == \"end\") {\n        state.blocks.pop();\n        state.currentIndent -= 1;\n      }\n\n      if (nextTokenizer.hasOwnProperty(matched)) {\n        state.tokenize.push(nextTokenizer[matched]);\n      }\n\n      return \"keyword\";\n    } else if (atomWords.test(matched)) {\n      return \"atom\";\n    }\n\n    return \"variable\";\n  }\n\n  // Class variables and instance variables\n  // or attributes\n  if (stream.eat(\"@\")) {\n    if (stream.peek() == \"[\") {\n      return chain(tokenNest(\"[\", \"]\", \"meta\"), stream, state);\n    }\n\n    stream.eat(\"@\");\n    stream.match(idents) || stream.match(types);\n    return \"propertyName\";\n  }\n\n  // Constants and types\n  if (stream.match(types)) {\n    return \"tag\";\n  }\n\n  // Symbols or ':' operator\n  if (stream.eat(\":\")) {\n    if (stream.eat(\"\\\"\")) {\n      return chain(tokenQuote(\"\\\"\", \"atom\", false), stream, state);\n    } else if (stream.match(idents) || stream.match(types) ||\n               stream.match(operators) || stream.match(conditionalOperators) || stream.match(indexingOperators)) {\n      return \"atom\";\n    }\n    stream.eat(\":\");\n    return \"operator\";\n  }\n\n  // Strings\n  if (stream.eat(\"\\\"\")) {\n    return chain(tokenQuote(\"\\\"\", \"string\", true), stream, state);\n  }\n\n  // Strings or regexps or macro variables or '%' operator\n  if (stream.peek() == \"%\") {\n    var style = \"string\";\n    var embed = true;\n    var delim;\n\n    if (stream.match(\"%r\")) {\n      // Regexps\n      style = \"string.special\";\n      delim = stream.next();\n    } else if (stream.match(\"%w\")) {\n      embed = false;\n      delim = stream.next();\n    } else if (stream.match(\"%q\")) {\n      embed = false;\n      delim = stream.next();\n    } else {\n      if(delim = stream.match(/^%([^\\w\\s=])/)) {\n        delim = delim[1];\n      } else if (stream.match(/^%[a-zA-Z_\\u009F-\\uFFFF][\\w\\u009F-\\uFFFF]*/)) {\n        // Macro variables\n        return \"meta\";\n      } else if (stream.eat('%')) {\n        // '%' operator\n        return \"operator\";\n      }\n    }\n\n    if (matching.hasOwnProperty(delim)) {\n      delim = matching[delim];\n    }\n    return chain(tokenQuote(delim, style, embed), stream, state);\n  }\n\n  // Here Docs\n  if (matched = stream.match(/^<<-('?)([A-Z]\\w*)\\1/)) {\n    return chain(tokenHereDoc(matched[2], !matched[1]), stream, state)\n  }\n\n  // Characters\n  if (stream.eat(\"'\")) {\n    stream.match(/^(?:[^']|\\\\(?:[befnrtv0'\"]|[0-7]{3}|u(?:[0-9a-fA-F]{4}|\\{[0-9a-fA-F]{1,6}\\})))/);\n    stream.eat(\"'\");\n    return \"atom\";\n  }\n\n  // Numbers\n  if (stream.eat(\"0\")) {\n    if (stream.eat(\"x\")) {\n      stream.match(/^[0-9a-fA-F_]+/);\n    } else if (stream.eat(\"o\")) {\n      stream.match(/^[0-7_]+/);\n    } else if (stream.eat(\"b\")) {\n      stream.match(/^[01_]+/);\n    }\n    return \"number\";\n  }\n\n  if (stream.eat(/^\\d/)) {\n    stream.match(/^[\\d_]*(?:\\.[\\d_]+)?(?:[eE][+-]?\\d+)?/);\n    return \"number\";\n  }\n\n  // Operators\n  if (stream.match(operators)) {\n    stream.eat(\"=\"); // Operators can follow assign symbol.\n    return \"operator\";\n  }\n\n  if (stream.match(conditionalOperators) || stream.match(anotherOperators)) {\n    return \"operator\";\n  }\n\n  // Parens and braces\n  if (matched = stream.match(/[({[]/, false)) {\n    matched = matched[0];\n    return chain(tokenNest(matched, matching[matched], null), stream, state);\n  }\n\n  // Escapes\n  if (stream.eat(\"\\\\\")) {\n    stream.next();\n    return \"meta\";\n  }\n\n  stream.next();\n  return null;\n}\n\nfunction tokenNest(begin, end, style, started) {\n  return function (stream, state) {\n    if (!started && stream.match(begin)) {\n      state.tokenize[state.tokenize.length - 1] = tokenNest(begin, end, style, true);\n      state.currentIndent += 1;\n      return style;\n    }\n\n    var nextStyle = tokenBase(stream, state);\n    if (stream.current() === end) {\n      state.tokenize.pop();\n      state.currentIndent -= 1;\n      nextStyle = style;\n    }\n\n    return nextStyle;\n  };\n}\n\nfunction tokenMacro(begin, end, started) {\n  return function (stream, state) {\n    if (!started && stream.match(\"{\" + begin)) {\n      state.currentIndent += 1;\n      state.tokenize[state.tokenize.length - 1] = tokenMacro(begin, end, true);\n      return \"meta\";\n    }\n\n    if (stream.match(end + \"}\")) {\n      state.currentIndent -= 1;\n      state.tokenize.pop();\n      return \"meta\";\n    }\n\n    return tokenBase(stream, state);\n  };\n}\n\nfunction tokenMacroDef(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  var matched;\n  if (matched = stream.match(idents)) {\n    if (matched == \"def\") {\n      return \"keyword\";\n    }\n    stream.eat(/[?!]/);\n  }\n\n  state.tokenize.pop();\n  return \"def\";\n}\n\nfunction tokenFollowIdent(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  if (stream.match(idents)) {\n    stream.eat(/[!?]/);\n  } else {\n    stream.match(operators) || stream.match(conditionalOperators) || stream.match(indexingOperators);\n  }\n  state.tokenize.pop();\n  return \"def\";\n}\n\nfunction tokenFollowType(stream, state) {\n  if (stream.eatSpace()) {\n    return null;\n  }\n\n  stream.match(types);\n  state.tokenize.pop();\n  return \"def\";\n}\n\nfunction tokenQuote(end, style, embed) {\n  return function (stream, state) {\n    var escaped = false;\n\n    while (stream.peek()) {\n      if (!escaped) {\n        if (stream.match(\"{%\", false)) {\n          state.tokenize.push(tokenMacro(\"%\", \"%\"));\n          return style;\n        }\n\n        if (stream.match(\"{{\", false)) {\n          state.tokenize.push(tokenMacro(\"{\", \"}\"));\n          return style;\n        }\n\n        if (embed && stream.match(\"#{\", false)) {\n          state.tokenize.push(tokenNest(\"#{\", \"}\", \"meta\"));\n          return style;\n        }\n\n        var ch = stream.next();\n\n        if (ch == end) {\n          state.tokenize.pop();\n          return style;\n        }\n\n        escaped = embed && ch == \"\\\\\";\n      } else {\n        stream.next();\n        escaped = false;\n      }\n    }\n\n    return style;\n  };\n}\n\nfunction tokenHereDoc(phrase, embed) {\n  return function (stream, state) {\n    if (stream.sol()) {\n      stream.eatSpace()\n      if (stream.match(phrase)) {\n        state.tokenize.pop();\n        return \"string\";\n      }\n    }\n\n    var escaped = false;\n    while (stream.peek()) {\n      if (!escaped) {\n        if (stream.match(\"{%\", false)) {\n          state.tokenize.push(tokenMacro(\"%\", \"%\"));\n          return \"string\";\n        }\n\n        if (stream.match(\"{{\", false)) {\n          state.tokenize.push(tokenMacro(\"{\", \"}\"));\n          return \"string\";\n        }\n\n        if (embed && stream.match(\"#{\", false)) {\n          state.tokenize.push(tokenNest(\"#{\", \"}\", \"meta\"));\n          return \"string\";\n        }\n\n        escaped = stream.next() == \"\\\\\" && embed;\n      } else {\n        stream.next();\n        escaped = false;\n      }\n    }\n\n    return \"string\";\n  }\n}\n\nexport const crystal = {\n  name: \"crystal\",\n  startState: function () {\n    return {\n      tokenize: [tokenBase],\n      currentIndent: 0,\n      lastToken: null,\n      lastStyle: null,\n      blocks: []\n    };\n  },\n\n  token: function (stream, state) {\n    var style = state.tokenize[state.tokenize.length - 1](stream, state);\n    var token = stream.current();\n\n    if (style && style != \"comment\") {\n      state.lastToken = token;\n      state.lastStyle = style;\n    }\n\n    return style;\n  },\n\n  indent: function (state, textAfter, cx) {\n    textAfter = textAfter.replace(/^\\s*(?:\\{%)?\\s*|\\s*(?:%\\})?\\s*$/g, \"\");\n\n    if (dedentKeywords.test(textAfter) || dedentPunctuals.test(textAfter)) {\n      return cx.unit * (state.currentIndent - 1);\n    }\n\n    return cx.unit * state.currentIndent;\n  },\n\n  languageData: {\n    indentOnInput: wordRegExp(dedentPunctualsArray.concat(dedentKeywordsArray), true),\n    commentTokens: {line: \"#\"}\n  }\n};\n"], "names": [], "sourceRoot": ""}