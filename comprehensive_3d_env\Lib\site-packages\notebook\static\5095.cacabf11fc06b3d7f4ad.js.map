{"version": 3, "file": "5095.cacabf11fc06b3d7f4ad.js?v=cacabf11fc06b3d7f4ad", "mappings": ";;;;;;;;;AAAA;AACA,4DAA4D,2BAA2B;;AAIrF;;;;;;;;;;;;;ACH2D;;AAE7D;AACA,6BAA6B,mGAAM,mBAAmB,6KAA8D;AACpH;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@mermaid-js/layout-elk/dist/chunks/mermaid-layout-elk.core/chunk-ZW26E7AF.mjs", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@mermaid-js/layout-elk/dist/mermaid-layout-elk.core.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __name = (target, value) => __defProp(target, \"name\", { value, configurable: true });\n\nexport {\n  __name\n};\n", "import {\n  __name\n} from \"./chunks/mermaid-layout-elk.core/chunk-ZW26E7AF.mjs\";\n\n// src/layouts.ts\nvar loader = /* @__PURE__ */ __name(async () => await import(\"./chunks/mermaid-layout-elk.core/render-SL7ONYGO.mjs\"), \"loader\");\nvar algos = [\"elk.stress\", \"elk.force\", \"elk.mrtree\", \"elk.sporeOverlap\"];\nvar layouts = [\n  {\n    name: \"elk\",\n    loader,\n    algorithm: \"elk.layered\"\n  },\n  ...algos.map((algo) => ({\n    name: algo,\n    loader,\n    algorithm: algo\n  }))\n];\nvar layouts_default = layouts;\nexport {\n  layouts_default as default\n};\n"], "names": [], "sourceRoot": ""}