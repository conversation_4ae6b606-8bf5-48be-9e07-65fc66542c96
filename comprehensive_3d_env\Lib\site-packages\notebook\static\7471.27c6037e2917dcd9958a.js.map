{"version": 3, "file": "7471.27c6037e2917dcd9958a.js?v=27c6037e2917dcd9958a", "mappings": ";;;;;;AAAa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB,wBAAwB,mBAAO,CAAC,KAAmB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB;;;;;;;AClDa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB;AACxB,mBAAmB,mBAAO,CAAC,IAAoB;AAC/C,wBAAwB,mBAAO,CAAC,KAAyB;AACzD;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,wFAAwF;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,wBAAwB;AACxB;;;;;;;ACxDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,4BAA4B,GAAG,uBAAuB,GAAG,oBAAoB,GAAG,kBAAkB;AAClG,mBAAmB,mBAAO,CAAC,IAAoB;AAC/C,oBAAoB,mBAAO,CAAC,KAAe;AAC3C,qBAAqB,mBAAO,CAAC,KAAgB;AAC7C,oBAAoB,mBAAO,CAAC,KAAe;AAC3C,oBAAoB,mBAAO,CAAC,KAAe;AAC3C,sBAAsB,mBAAO,CAAC,KAA+B;AAC7D,oBAAoB,mBAAO,CAAC,KAAqB;AACjD,2BAA2B,mBAAO,CAAC,KAA4B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0EAA0E,UAAU;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB,wEAAwE;AAC1F;AACA;AACA,kCAAkC;AAClC;AACA,kCAAkC,gCAAgC,eAAe;AACjF,wCAAwC,oCAAoC;AAC5E;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA,gEAAgE,UAAU;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA,gEAAgE,UAAU;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA,gEAAgE,UAAU;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,gEAAgE,UAAU;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,wFAAwF;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,iCAAiC;AAC5E;AACA,2CAA2C,6CAA6C;AACxF;AACA,2CAA2C,0BAA0B;AACrE;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC;AAChC;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,8DAA8D,6CAA6C;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,UAAU;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA,gGAAgG,qBAAqB;AACrH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,mDAAmD;AAClG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mEAAmE,UAAU;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8EAA8E,UAAU;AACxF;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA,+DAA+D,UAAU;AACzE;AACA;AACA;AACA;AACA,yBAAyB,SAAS;AAClC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC,YAAY;AAC9C,gEAAgE;AAChE;AACA;AACA,mEAAmE,kFAAkF;AACrJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sEAAsE,UAAU;AAChF;AACA;AACA,yHAAyH,sBAAsB;AAC/I;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,SAAS;AAC1C;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA,yBAAyB,SAAS;AAClC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,CAAC;AACD,4BAA4B;AAC5B;;;;;;;AC7lBa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,wBAAwB;AACxB,sBAAsB,mBAAO,CAAC,IAAuB;AACrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,wBAAwB;AACxB;;;;;;;AC9Ba;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,yBAAyB;AACzB,mBAAmB,mBAAO,CAAC,IAAoB;AAC/C,wBAAwB,mBAAO,CAAC,KAAyB;AACzD;AACA;AACA,kCAAkC;AAClC;AACA;AACA,wFAAwF;AACxF;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,yBAAyB;AACzB;;;;;;;ACjDa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA,iDAAiD,OAAO;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,wBAAwB,mBAAO,CAAC,KAA4B;AAC5D,mBAAmB,mBAAO,CAAC,IAAuB;AAClD,wBAAwB,mBAAO,CAAC,KAAmB;AACnD,wBAAwB,mBAAO,CAAC,KAAmB;AACnD,0BAA0B,mBAAO,CAAC,KAAqB;AACvD,oBAAoB,mBAAO,CAAC,KAAwB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8DAA8D,UAAU;AACxE;AACA;AACA;AACA,6BAA6B;AAC7B;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sDAAsD,uEAAuE;AAC7H;AACA,sHAAsH,UAAU;AAChI;AACA;AACA;AACA,+FAA+F,UAAU;AACzG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mHAAmH,UAAU;AAC7H;AACA;AACA;AACA;AACA,gDAAgD,QAAQ;AACxD;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA,qHAAqH,UAAU;AAC/H;AACA;AACA;AACA;AACA;AACA,gDAAgD,QAAQ;AACxD;AACA;AACA;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA,oCAAoC,QAAQ;AAC5C;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0FAA0F,UAAU;AACpG;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA,mEAAmE,UAAU;AAC7E;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B,QAAQ;AACpC;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+CAA+C,qDAAqD,gEAAgE,mEAAmE,2EAA2E,0GAA0G;AAC5Z;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;ACtQa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,sBAAsB;AACtB,mBAAmB,mBAAO,CAAC,IAAuB;AAClD;AACA;AACA,kCAAkC;AAClC;AACA,wFAAwF;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,mCAAmC;AAC9D;AACA;AACA;AACA;AACA,CAAC;AACD,sBAAsB;AACtB;;;;;;;AClIa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,mBAAmB;AACnB,mBAAmB,mBAAO,CAAC,KAAuB;AAClD,wBAAwB,mBAAO,CAAC,KAAmB;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,mBAAmB;AACnB;;;;;;;AC1Da;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,oBAAoB,mBAAO,CAAC,KAAwB;AACpD;AACA;AACA;AACA,kCAAkC;AAClC,gCAAgC,UAAU;AAC1C,8BAA8B,QAAQ;AACtC;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;ACpGa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,oBAAoB,mBAAO,CAAC,KAAwB;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;AC3Ba;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,qBAAqB,GAAG,gBAAgB;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA,qBAAqB,uBAAuB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,qBAAqB;AACrB;;;;;;;ACjIa;AACb;AACA;AACA;AACA,eAAe,gBAAgB,sCAAsC,kBAAkB;AACvF,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,oBAAoB;AACpB,2BAA2B,mBAAO,CAAC,KAAsB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,0DAA0D,UAAU;AACpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oEAAoE,mBAAmB;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa;AACb,SAAS;AACT;AACA;AACA,CAAC;AACD,oBAAoB;AACpB;;;;;;;AChHa;AACb;AACA,cAAc,6BAA6B,0BAA0B,cAAc,qBAAqB;AACxG,iBAAiB,oDAAoD,qEAAqE,cAAc;AACxJ,uBAAuB,sBAAsB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC;AACxC,mCAAmC,SAAS;AAC5C,mCAAmC,WAAW,UAAU;AACxD,0CAA0C,cAAc;AACxD;AACA,8GAA8G,OAAO;AACrH,iFAAiF,iBAAiB;AAClG,yDAAyD,gBAAgB,QAAQ;AACjF,+CAA+C,gBAAgB,gBAAgB;AAC/E;AACA,kCAAkC;AAClC;AACA;AACA,UAAU,YAAY,aAAa,SAAS,UAAU;AACtD,oCAAoC,SAAS;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,MAAM;AAC1B;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA;AACA,6EAA6E,OAAO;AACpF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qBAAqB;AACrB;AACA;AACA;AACA;AACA,8CAA6C,EAAE,aAAa,EAAC;AAC7D,kBAAkB,GAAG,gBAAgB,GAAG,WAAW;AACnD,WAAW;AACX;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,gBAAgB;AAChB;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,wEAAwE,gBAAgB;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA,6EAA6E,UAAU;AACvF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,uBAAuB;AAChD;AACA;AACA;AACA;AACA,4EAA4E,iBAAiB;AAC7F;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA,0DAA0D,UAAU;AACpE;AACA;AACA;AACA;AACA,wBAAwB,QAAQ;AAChC;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,kBAAkB;AAClB;;;;;;;ACvTa;AACb,8CAA6C,EAAE,aAAa,EAAC;AAC7D,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA,UAAU;AACV,sCAAsC,gCAAgC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,uBAAuB;AACvB", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/Handler.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/InputJax.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MathDocument.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/MathList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/core/OutputJax.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/handlers/html/HTMLDocument.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/handlers/html/HTMLDomStrings.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/handlers/html/HTMLHandler.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/handlers/html/HTMLMathItem.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/handlers/html/HTMLMathList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/BitField.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/FunctionList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/LinkedList.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mathjax-full/js/util/PrioritizedList.js"], "sourcesContent": ["\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractHandler = void 0;\nvar MathDocument_js_1 = require(\"./MathDocument.js\");\nvar DefaultMathDocument = (function (_super) {\n    __extends(DefaultMathDocument, _super);\n    function DefaultMathDocument() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return DefaultMathDocument;\n}(MathDocument_js_1.AbstractMathDocument));\nvar AbstractHandler = (function () {\n    function AbstractHandler(adaptor, priority) {\n        if (priority === void 0) { priority = 5; }\n        this.documentClass = DefaultMathDocument;\n        this.adaptor = adaptor;\n        this.priority = priority;\n    }\n    Object.defineProperty(AbstractHandler.prototype, \"name\", {\n        get: function () {\n            return this.constructor.NAME;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractHandler.prototype.handlesDocument = function (_document) {\n        return false;\n    };\n    AbstractHandler.prototype.create = function (document, options) {\n        return new this.documentClass(document, this.adaptor, options);\n    };\n    AbstractHandler.NAME = 'generic';\n    return AbstractHandler;\n}());\nexports.AbstractHandler = AbstractHandler;\n//# sourceMappingURL=Handler.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractInputJax = void 0;\nvar Options_js_1 = require(\"../util/Options.js\");\nvar FunctionList_js_1 = require(\"../util/FunctionList.js\");\nvar AbstractInputJax = (function () {\n    function AbstractInputJax(options) {\n        if (options === void 0) { options = {}; }\n        this.adaptor = null;\n        this.mmlFactory = null;\n        var CLASS = this.constructor;\n        this.options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, CLASS.OPTIONS), options);\n        this.preFilters = new FunctionList_js_1.FunctionList();\n        this.postFilters = new FunctionList_js_1.FunctionList();\n    }\n    Object.defineProperty(AbstractInputJax.prototype, \"name\", {\n        get: function () {\n            return this.constructor.NAME;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractInputJax.prototype.setAdaptor = function (adaptor) {\n        this.adaptor = adaptor;\n    };\n    AbstractInputJax.prototype.setMmlFactory = function (mmlFactory) {\n        this.mmlFactory = mmlFactory;\n    };\n    AbstractInputJax.prototype.initialize = function () {\n    };\n    AbstractInputJax.prototype.reset = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    Object.defineProperty(AbstractInputJax.prototype, \"processStrings\", {\n        get: function () {\n            return true;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractInputJax.prototype.findMath = function (_node, _options) {\n        return [];\n    };\n    AbstractInputJax.prototype.executeFilters = function (filters, math, document, data) {\n        var args = { math: math, document: document, data: data };\n        filters.execute(args);\n        return args.data;\n    };\n    AbstractInputJax.NAME = 'generic';\n    AbstractInputJax.OPTIONS = {};\n    return AbstractInputJax;\n}());\nexports.AbstractInputJax = AbstractInputJax;\n//# sourceMappingURL=InputJax.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractMathDocument = exports.resetAllOptions = exports.resetOptions = exports.RenderList = void 0;\nvar Options_js_1 = require(\"../util/Options.js\");\nvar InputJax_js_1 = require(\"./InputJax.js\");\nvar OutputJax_js_1 = require(\"./OutputJax.js\");\nvar MathList_js_1 = require(\"./MathList.js\");\nvar MathItem_js_1 = require(\"./MathItem.js\");\nvar MmlFactory_js_1 = require(\"../core/MmlTree/MmlFactory.js\");\nvar BitField_js_1 = require(\"../util/BitField.js\");\nvar PrioritizedList_js_1 = require(\"../util/PrioritizedList.js\");\nvar RenderList = (function (_super) {\n    __extends(RenderList, _super);\n    function RenderList() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    RenderList.create = function (actions) {\n        var e_1, _a;\n        var list = new this();\n        try {\n            for (var _b = __values(Object.keys(actions)), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var id = _c.value;\n                var _d = __read(this.action(id, actions[id]), 2), action = _d[0], priority = _d[1];\n                if (priority) {\n                    list.add(action, priority);\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return list;\n    };\n    RenderList.action = function (id, action) {\n        var _a, _b, _c, _d;\n        var renderDoc, renderMath;\n        var convert = true;\n        var priority = action[0];\n        if (action.length === 1 || typeof action[1] === 'boolean') {\n            action.length === 2 && (convert = action[1]);\n            _a = __read(this.methodActions(id), 2), renderDoc = _a[0], renderMath = _a[1];\n        }\n        else if (typeof action[1] === 'string') {\n            if (typeof action[2] === 'string') {\n                action.length === 4 && (convert = action[3]);\n                var _e = __read(action.slice(1), 2), method1 = _e[0], method2 = _e[1];\n                _b = __read(this.methodActions(method1, method2), 2), renderDoc = _b[0], renderMath = _b[1];\n            }\n            else {\n                action.length === 3 && (convert = action[2]);\n                _c = __read(this.methodActions(action[1]), 2), renderDoc = _c[0], renderMath = _c[1];\n            }\n        }\n        else {\n            action.length === 4 && (convert = action[3]);\n            _d = __read(action.slice(1), 2), renderDoc = _d[0], renderMath = _d[1];\n        }\n        return [{ id: id, renderDoc: renderDoc, renderMath: renderMath, convert: convert }, priority];\n    };\n    RenderList.methodActions = function (method1, method2) {\n        if (method2 === void 0) { method2 = method1; }\n        return [\n            function (document) { method1 && document[method1](); return false; },\n            function (math, document) { method2 && math[method2](document); return false; }\n        ];\n    };\n    RenderList.prototype.renderDoc = function (document, start) {\n        var e_2, _a;\n        if (start === void 0) { start = MathItem_js_1.STATE.UNPROCESSED; }\n        try {\n            for (var _b = __values(this.items), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                if (item.priority >= start) {\n                    if (item.item.renderDoc(document))\n                        return;\n                }\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n    };\n    RenderList.prototype.renderMath = function (math, document, start) {\n        var e_3, _a;\n        if (start === void 0) { start = MathItem_js_1.STATE.UNPROCESSED; }\n        try {\n            for (var _b = __values(this.items), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                if (item.priority >= start) {\n                    if (item.item.renderMath(math, document))\n                        return;\n                }\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n    };\n    RenderList.prototype.renderConvert = function (math, document, end) {\n        var e_4, _a;\n        if (end === void 0) { end = MathItem_js_1.STATE.LAST; }\n        try {\n            for (var _b = __values(this.items), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                if (item.priority > end)\n                    return;\n                if (item.item.convert) {\n                    if (item.item.renderMath(math, document))\n                        return;\n                }\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n    };\n    RenderList.prototype.findID = function (id) {\n        var e_5, _a;\n        try {\n            for (var _b = __values(this.items), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                if (item.item.id === id) {\n                    return item.item;\n                }\n            }\n        }\n        catch (e_5_1) { e_5 = { error: e_5_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_5) throw e_5.error; }\n        }\n        return null;\n    };\n    return RenderList;\n}(PrioritizedList_js_1.PrioritizedList));\nexports.RenderList = RenderList;\nexports.resetOptions = {\n    all: false,\n    processed: false,\n    inputJax: null,\n    outputJax: null\n};\nexports.resetAllOptions = {\n    all: true,\n    processed: true,\n    inputJax: [],\n    outputJax: []\n};\nvar DefaultInputJax = (function (_super) {\n    __extends(DefaultInputJax, _super);\n    function DefaultInputJax() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    DefaultInputJax.prototype.compile = function (_math) {\n        return null;\n    };\n    return DefaultInputJax;\n}(InputJax_js_1.AbstractInputJax));\nvar DefaultOutputJax = (function (_super) {\n    __extends(DefaultOutputJax, _super);\n    function DefaultOutputJax() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    DefaultOutputJax.prototype.typeset = function (_math, _document) {\n        if (_document === void 0) { _document = null; }\n        return null;\n    };\n    DefaultOutputJax.prototype.escaped = function (_math, _document) {\n        return null;\n    };\n    return DefaultOutputJax;\n}(OutputJax_js_1.AbstractOutputJax));\nvar DefaultMathList = (function (_super) {\n    __extends(DefaultMathList, _super);\n    function DefaultMathList() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return DefaultMathList;\n}(MathList_js_1.AbstractMathList));\nvar DefaultMathItem = (function (_super) {\n    __extends(DefaultMathItem, _super);\n    function DefaultMathItem() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return DefaultMathItem;\n}(MathItem_js_1.AbstractMathItem));\nvar AbstractMathDocument = (function () {\n    function AbstractMathDocument(document, adaptor, options) {\n        var _this = this;\n        var CLASS = this.constructor;\n        this.document = document;\n        this.options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, CLASS.OPTIONS), options);\n        this.math = new (this.options['MathList'] || DefaultMathList)();\n        this.renderActions = RenderList.create(this.options['renderActions']);\n        this.processed = new AbstractMathDocument.ProcessBits();\n        this.outputJax = this.options['OutputJax'] || new DefaultOutputJax();\n        var inputJax = this.options['InputJax'] || [new DefaultInputJax()];\n        if (!Array.isArray(inputJax)) {\n            inputJax = [inputJax];\n        }\n        this.inputJax = inputJax;\n        this.adaptor = adaptor;\n        this.outputJax.setAdaptor(adaptor);\n        this.inputJax.map(function (jax) { return jax.setAdaptor(adaptor); });\n        this.mmlFactory = this.options['MmlFactory'] || new MmlFactory_js_1.MmlFactory();\n        this.inputJax.map(function (jax) { return jax.setMmlFactory(_this.mmlFactory); });\n        this.outputJax.initialize();\n        this.inputJax.map(function (jax) { return jax.initialize(); });\n    }\n    Object.defineProperty(AbstractMathDocument.prototype, \"kind\", {\n        get: function () {\n            return this.constructor.KIND;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractMathDocument.prototype.addRenderAction = function (id) {\n        var action = [];\n        for (var _i = 1; _i < arguments.length; _i++) {\n            action[_i - 1] = arguments[_i];\n        }\n        var _a = __read(RenderList.action(id, action), 2), fn = _a[0], p = _a[1];\n        this.renderActions.add(fn, p);\n    };\n    AbstractMathDocument.prototype.removeRenderAction = function (id) {\n        var action = this.renderActions.findID(id);\n        if (action) {\n            this.renderActions.remove(action);\n        }\n    };\n    AbstractMathDocument.prototype.render = function () {\n        this.renderActions.renderDoc(this);\n        return this;\n    };\n    AbstractMathDocument.prototype.rerender = function (start) {\n        if (start === void 0) { start = MathItem_js_1.STATE.RERENDER; }\n        this.state(start - 1);\n        this.render();\n        return this;\n    };\n    AbstractMathDocument.prototype.convert = function (math, options) {\n        if (options === void 0) { options = {}; }\n        var _a = (0, Options_js_1.userOptions)({\n            format: this.inputJax[0].name, display: true, end: MathItem_js_1.STATE.LAST,\n            em: 16, ex: 8, containerWidth: null, lineWidth: 1000000, scale: 1, family: ''\n        }, options), format = _a.format, display = _a.display, end = _a.end, ex = _a.ex, em = _a.em, containerWidth = _a.containerWidth, lineWidth = _a.lineWidth, scale = _a.scale, family = _a.family;\n        if (containerWidth === null) {\n            containerWidth = 80 * ex;\n        }\n        var jax = this.inputJax.reduce(function (jax, ijax) { return (ijax.name === format ? ijax : jax); }, null);\n        var mitem = new this.options.MathItem(math, jax, display);\n        mitem.start.node = this.adaptor.body(this.document);\n        mitem.setMetrics(em, ex, containerWidth, lineWidth, scale);\n        if (this.outputJax.options.mtextInheritFont) {\n            mitem.outputData.mtextFamily = family;\n        }\n        if (this.outputJax.options.merrorInheritFont) {\n            mitem.outputData.merrorFamily = family;\n        }\n        mitem.convert(this, end);\n        return (mitem.typesetRoot || mitem.root);\n    };\n    AbstractMathDocument.prototype.findMath = function (_options) {\n        if (_options === void 0) { _options = null; }\n        this.processed.set('findMath');\n        return this;\n    };\n    AbstractMathDocument.prototype.compile = function () {\n        var e_6, _a, e_7, _b;\n        if (!this.processed.isSet('compile')) {\n            var recompile = [];\n            try {\n                for (var _c = __values(this.math), _d = _c.next(); !_d.done; _d = _c.next()) {\n                    var math = _d.value;\n                    this.compileMath(math);\n                    if (math.inputData.recompile !== undefined) {\n                        recompile.push(math);\n                    }\n                }\n            }\n            catch (e_6_1) { e_6 = { error: e_6_1 }; }\n            finally {\n                try {\n                    if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n                }\n                finally { if (e_6) throw e_6.error; }\n            }\n            try {\n                for (var recompile_1 = __values(recompile), recompile_1_1 = recompile_1.next(); !recompile_1_1.done; recompile_1_1 = recompile_1.next()) {\n                    var math = recompile_1_1.value;\n                    var data = math.inputData.recompile;\n                    math.state(data.state);\n                    math.inputData.recompile = data;\n                    this.compileMath(math);\n                }\n            }\n            catch (e_7_1) { e_7 = { error: e_7_1 }; }\n            finally {\n                try {\n                    if (recompile_1_1 && !recompile_1_1.done && (_b = recompile_1.return)) _b.call(recompile_1);\n                }\n                finally { if (e_7) throw e_7.error; }\n            }\n            this.processed.set('compile');\n        }\n        return this;\n    };\n    AbstractMathDocument.prototype.compileMath = function (math) {\n        try {\n            math.compile(this);\n        }\n        catch (err) {\n            if (err.retry || err.restart) {\n                throw err;\n            }\n            this.options['compileError'](this, math, err);\n            math.inputData['error'] = err;\n        }\n    };\n    AbstractMathDocument.prototype.compileError = function (math, err) {\n        math.root = this.mmlFactory.create('math', null, [\n            this.mmlFactory.create('merror', { 'data-mjx-error': err.message, title: err.message }, [\n                this.mmlFactory.create('mtext', null, [\n                    this.mmlFactory.create('text').setText('Math input error')\n                ])\n            ])\n        ]);\n        if (math.display) {\n            math.root.attributes.set('display', 'block');\n        }\n        math.inputData.error = err.message;\n    };\n    AbstractMathDocument.prototype.typeset = function () {\n        var e_8, _a;\n        if (!this.processed.isSet('typeset')) {\n            try {\n                for (var _b = __values(this.math), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var math = _c.value;\n                    try {\n                        math.typeset(this);\n                    }\n                    catch (err) {\n                        if (err.retry || err.restart) {\n                            throw err;\n                        }\n                        this.options['typesetError'](this, math, err);\n                        math.outputData['error'] = err;\n                    }\n                }\n            }\n            catch (e_8_1) { e_8 = { error: e_8_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_8) throw e_8.error; }\n            }\n            this.processed.set('typeset');\n        }\n        return this;\n    };\n    AbstractMathDocument.prototype.typesetError = function (math, err) {\n        math.typesetRoot = this.adaptor.node('mjx-container', {\n            class: 'MathJax mjx-output-error',\n            jax: this.outputJax.name,\n        }, [\n            this.adaptor.node('span', {\n                'data-mjx-error': err.message,\n                title: err.message,\n                style: {\n                    color: 'red',\n                    'background-color': 'yellow',\n                    'line-height': 'normal'\n                }\n            }, [\n                this.adaptor.text('Math output error')\n            ])\n        ]);\n        if (math.display) {\n            this.adaptor.setAttributes(math.typesetRoot, {\n                style: {\n                    display: 'block',\n                    margin: '1em 0',\n                    'text-align': 'center'\n                }\n            });\n        }\n        math.outputData.error = err.message;\n    };\n    AbstractMathDocument.prototype.getMetrics = function () {\n        if (!this.processed.isSet('getMetrics')) {\n            this.outputJax.getMetrics(this);\n            this.processed.set('getMetrics');\n        }\n        return this;\n    };\n    AbstractMathDocument.prototype.updateDocument = function () {\n        var e_9, _a;\n        if (!this.processed.isSet('updateDocument')) {\n            try {\n                for (var _b = __values(this.math.reversed()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var math = _c.value;\n                    math.updateDocument(this);\n                }\n            }\n            catch (e_9_1) { e_9 = { error: e_9_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_9) throw e_9.error; }\n            }\n            this.processed.set('updateDocument');\n        }\n        return this;\n    };\n    AbstractMathDocument.prototype.removeFromDocument = function (_restore) {\n        if (_restore === void 0) { _restore = false; }\n        return this;\n    };\n    AbstractMathDocument.prototype.state = function (state, restore) {\n        var e_10, _a;\n        if (restore === void 0) { restore = false; }\n        try {\n            for (var _b = __values(this.math), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var math = _c.value;\n                math.state(state, restore);\n            }\n        }\n        catch (e_10_1) { e_10 = { error: e_10_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_10) throw e_10.error; }\n        }\n        if (state < MathItem_js_1.STATE.INSERTED) {\n            this.processed.clear('updateDocument');\n        }\n        if (state < MathItem_js_1.STATE.TYPESET) {\n            this.processed.clear('typeset');\n            this.processed.clear('getMetrics');\n        }\n        if (state < MathItem_js_1.STATE.COMPILED) {\n            this.processed.clear('compile');\n        }\n        return this;\n    };\n    AbstractMathDocument.prototype.reset = function (options) {\n        var _a;\n        if (options === void 0) { options = { processed: true }; }\n        options = (0, Options_js_1.userOptions)(Object.assign({}, exports.resetOptions), options);\n        options.all && Object.assign(options, exports.resetAllOptions);\n        options.processed && this.processed.reset();\n        options.inputJax && this.inputJax.forEach(function (jax) { return jax.reset.apply(jax, __spreadArray([], __read(options.inputJax), false)); });\n        options.outputJax && (_a = this.outputJax).reset.apply(_a, __spreadArray([], __read(options.outputJax), false));\n        return this;\n    };\n    AbstractMathDocument.prototype.clear = function () {\n        this.reset();\n        this.math.clear();\n        return this;\n    };\n    AbstractMathDocument.prototype.concat = function (list) {\n        this.math.merge(list);\n        return this;\n    };\n    AbstractMathDocument.prototype.clearMathItemsWithin = function (containers) {\n        var _a;\n        var items = this.getMathItemsWithin(containers);\n        (_a = this.math).remove.apply(_a, __spreadArray([], __read(items), false));\n        return items;\n    };\n    AbstractMathDocument.prototype.getMathItemsWithin = function (elements) {\n        var e_11, _a, e_12, _b;\n        if (!Array.isArray(elements)) {\n            elements = [elements];\n        }\n        var adaptor = this.adaptor;\n        var items = [];\n        var containers = adaptor.getElements(elements, this.document);\n        try {\n            ITEMS: for (var _c = __values(this.math), _d = _c.next(); !_d.done; _d = _c.next()) {\n                var item = _d.value;\n                try {\n                    for (var containers_1 = (e_12 = void 0, __values(containers)), containers_1_1 = containers_1.next(); !containers_1_1.done; containers_1_1 = containers_1.next()) {\n                        var container = containers_1_1.value;\n                        if (item.start.node && adaptor.contains(container, item.start.node)) {\n                            items.push(item);\n                            continue ITEMS;\n                        }\n                    }\n                }\n                catch (e_12_1) { e_12 = { error: e_12_1 }; }\n                finally {\n                    try {\n                        if (containers_1_1 && !containers_1_1.done && (_b = containers_1.return)) _b.call(containers_1);\n                    }\n                    finally { if (e_12) throw e_12.error; }\n                }\n            }\n        }\n        catch (e_11_1) { e_11 = { error: e_11_1 }; }\n        finally {\n            try {\n                if (_d && !_d.done && (_a = _c.return)) _a.call(_c);\n            }\n            finally { if (e_11) throw e_11.error; }\n        }\n        return items;\n    };\n    AbstractMathDocument.KIND = 'MathDocument';\n    AbstractMathDocument.OPTIONS = {\n        OutputJax: null,\n        InputJax: null,\n        MmlFactory: null,\n        MathList: DefaultMathList,\n        MathItem: DefaultMathItem,\n        compileError: function (doc, math, err) {\n            doc.compileError(math, err);\n        },\n        typesetError: function (doc, math, err) {\n            doc.typesetError(math, err);\n        },\n        renderActions: (0, Options_js_1.expandable)({\n            find: [MathItem_js_1.STATE.FINDMATH, 'findMath', '', false],\n            compile: [MathItem_js_1.STATE.COMPILED],\n            metrics: [MathItem_js_1.STATE.METRICS, 'getMetrics', '', false],\n            typeset: [MathItem_js_1.STATE.TYPESET],\n            update: [MathItem_js_1.STATE.INSERTED, 'updateDocument', false]\n        })\n    };\n    AbstractMathDocument.ProcessBits = (0, BitField_js_1.BitFieldClass)('findMath', 'compile', 'getMetrics', 'typeset', 'updateDocument');\n    return AbstractMathDocument;\n}());\nexports.AbstractMathDocument = AbstractMathDocument;\n//# sourceMappingURL=MathDocument.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractMathList = void 0;\nvar LinkedList_js_1 = require(\"../util/LinkedList.js\");\nvar AbstractMathList = (function (_super) {\n    __extends(AbstractMathList, _super);\n    function AbstractMathList() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    AbstractMathList.prototype.isBefore = function (a, b) {\n        return (a.start.i < b.start.i || (a.start.i === b.start.i && a.start.n < b.start.n));\n    };\n    return AbstractMathList;\n}(LinkedList_js_1.LinkedList));\nexports.AbstractMathList = AbstractMathList;\n//# sourceMappingURL=MathList.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.AbstractOutputJax = void 0;\nvar Options_js_1 = require(\"../util/Options.js\");\nvar FunctionList_js_1 = require(\"../util/FunctionList.js\");\nvar AbstractOutputJax = (function () {\n    function AbstractOutputJax(options) {\n        if (options === void 0) { options = {}; }\n        this.adaptor = null;\n        var CLASS = this.constructor;\n        this.options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, CLASS.OPTIONS), options);\n        this.postFilters = new FunctionList_js_1.FunctionList();\n    }\n    Object.defineProperty(AbstractOutputJax.prototype, \"name\", {\n        get: function () {\n            return this.constructor.NAME;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    AbstractOutputJax.prototype.setAdaptor = function (adaptor) {\n        this.adaptor = adaptor;\n    };\n    AbstractOutputJax.prototype.initialize = function () {\n    };\n    AbstractOutputJax.prototype.reset = function () {\n        var _args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            _args[_i] = arguments[_i];\n        }\n    };\n    AbstractOutputJax.prototype.getMetrics = function (_document) {\n    };\n    AbstractOutputJax.prototype.styleSheet = function (_document) {\n        return null;\n    };\n    AbstractOutputJax.prototype.pageElements = function (_document) {\n        return null;\n    };\n    AbstractOutputJax.prototype.executeFilters = function (filters, math, document, data) {\n        var args = { math: math, document: document, data: data };\n        filters.execute(args);\n        return args.data;\n    };\n    AbstractOutputJax.NAME = 'generic';\n    AbstractOutputJax.OPTIONS = {};\n    return AbstractOutputJax;\n}());\nexports.AbstractOutputJax = AbstractOutputJax;\n//# sourceMappingURL=OutputJax.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HTMLDocument = void 0;\nvar MathDocument_js_1 = require(\"../../core/MathDocument.js\");\nvar Options_js_1 = require(\"../../util/Options.js\");\nvar HTMLMathItem_js_1 = require(\"./HTMLMathItem.js\");\nvar HTMLMathList_js_1 = require(\"./HTMLMathList.js\");\nvar HTMLDomStrings_js_1 = require(\"./HTMLDomStrings.js\");\nvar MathItem_js_1 = require(\"../../core/MathItem.js\");\nvar HTMLDocument = (function (_super) {\n    __extends(HTMLDocument, _super);\n    function HTMLDocument(document, adaptor, options) {\n        var _this = this;\n        var _a = __read((0, Options_js_1.separateOptions)(options, HTMLDomStrings_js_1.HTMLDomStrings.OPTIONS), 2), html = _a[0], dom = _a[1];\n        _this = _super.call(this, document, adaptor, html) || this;\n        _this.domStrings = _this.options['DomStrings'] || new HTMLDomStrings_js_1.HTMLDomStrings(dom);\n        _this.domStrings.adaptor = adaptor;\n        _this.styles = [];\n        return _this;\n    }\n    HTMLDocument.prototype.findPosition = function (N, index, delim, nodes) {\n        var e_1, _a;\n        var adaptor = this.adaptor;\n        try {\n            for (var _b = __values(nodes[N]), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var list = _c.value;\n                var _d = __read(list, 2), node = _d[0], n = _d[1];\n                if (index <= n && adaptor.kind(node) === '#text') {\n                    return { node: node, n: Math.max(index, 0), delim: delim };\n                }\n                index -= n;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return { node: null, n: 0, delim: delim };\n    };\n    HTMLDocument.prototype.mathItem = function (item, jax, nodes) {\n        var math = item.math;\n        var start = this.findPosition(item.n, item.start.n, item.open, nodes);\n        var end = this.findPosition(item.n, item.end.n, item.close, nodes);\n        return new this.options.MathItem(math, jax, item.display, start, end);\n    };\n    HTMLDocument.prototype.findMath = function (options) {\n        var e_2, _a, e_3, _b, _c, e_4, _d, e_5, _e;\n        if (!this.processed.isSet('findMath')) {\n            this.adaptor.document = this.document;\n            options = (0, Options_js_1.userOptions)({ elements: this.options.elements || [this.adaptor.body(this.document)] }, options);\n            try {\n                for (var _f = __values(this.adaptor.getElements(options['elements'], this.document)), _g = _f.next(); !_g.done; _g = _f.next()) {\n                    var container = _g.value;\n                    var _h = __read([null, null], 2), strings = _h[0], nodes = _h[1];\n                    try {\n                        for (var _j = (e_3 = void 0, __values(this.inputJax)), _k = _j.next(); !_k.done; _k = _j.next()) {\n                            var jax = _k.value;\n                            var list = new (this.options['MathList'])();\n                            if (jax.processStrings) {\n                                if (strings === null) {\n                                    _c = __read(this.domStrings.find(container), 2), strings = _c[0], nodes = _c[1];\n                                }\n                                try {\n                                    for (var _l = (e_4 = void 0, __values(jax.findMath(strings))), _m = _l.next(); !_m.done; _m = _l.next()) {\n                                        var math = _m.value;\n                                        list.push(this.mathItem(math, jax, nodes));\n                                    }\n                                }\n                                catch (e_4_1) { e_4 = { error: e_4_1 }; }\n                                finally {\n                                    try {\n                                        if (_m && !_m.done && (_d = _l.return)) _d.call(_l);\n                                    }\n                                    finally { if (e_4) throw e_4.error; }\n                                }\n                            }\n                            else {\n                                try {\n                                    for (var _o = (e_5 = void 0, __values(jax.findMath(container))), _p = _o.next(); !_p.done; _p = _o.next()) {\n                                        var math = _p.value;\n                                        var item = new this.options.MathItem(math.math, jax, math.display, math.start, math.end);\n                                        list.push(item);\n                                    }\n                                }\n                                catch (e_5_1) { e_5 = { error: e_5_1 }; }\n                                finally {\n                                    try {\n                                        if (_p && !_p.done && (_e = _o.return)) _e.call(_o);\n                                    }\n                                    finally { if (e_5) throw e_5.error; }\n                                }\n                            }\n                            this.math.merge(list);\n                        }\n                    }\n                    catch (e_3_1) { e_3 = { error: e_3_1 }; }\n                    finally {\n                        try {\n                            if (_k && !_k.done && (_b = _j.return)) _b.call(_j);\n                        }\n                        finally { if (e_3) throw e_3.error; }\n                    }\n                }\n            }\n            catch (e_2_1) { e_2 = { error: e_2_1 }; }\n            finally {\n                try {\n                    if (_g && !_g.done && (_a = _f.return)) _a.call(_f);\n                }\n                finally { if (e_2) throw e_2.error; }\n            }\n            this.processed.set('findMath');\n        }\n        return this;\n    };\n    HTMLDocument.prototype.updateDocument = function () {\n        if (!this.processed.isSet('updateDocument')) {\n            this.addPageElements();\n            this.addStyleSheet();\n            _super.prototype.updateDocument.call(this);\n            this.processed.set('updateDocument');\n        }\n        return this;\n    };\n    HTMLDocument.prototype.addPageElements = function () {\n        var body = this.adaptor.body(this.document);\n        var node = this.documentPageElements();\n        if (node) {\n            this.adaptor.append(body, node);\n        }\n    };\n    HTMLDocument.prototype.addStyleSheet = function () {\n        var sheet = this.documentStyleSheet();\n        var adaptor = this.adaptor;\n        if (sheet && !adaptor.parent(sheet)) {\n            var head = adaptor.head(this.document);\n            var styles = this.findSheet(head, adaptor.getAttribute(sheet, 'id'));\n            if (styles) {\n                adaptor.replace(sheet, styles);\n            }\n            else {\n                adaptor.append(head, sheet);\n            }\n        }\n    };\n    HTMLDocument.prototype.findSheet = function (head, id) {\n        var e_6, _a;\n        if (id) {\n            try {\n                for (var _b = __values(this.adaptor.tags(head, 'style')), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var sheet = _c.value;\n                    if (this.adaptor.getAttribute(sheet, 'id') === id) {\n                        return sheet;\n                    }\n                }\n            }\n            catch (e_6_1) { e_6 = { error: e_6_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_6) throw e_6.error; }\n            }\n        }\n        return null;\n    };\n    HTMLDocument.prototype.removeFromDocument = function (restore) {\n        var e_7, _a;\n        if (restore === void 0) { restore = false; }\n        if (this.processed.isSet('updateDocument')) {\n            try {\n                for (var _b = __values(this.math), _c = _b.next(); !_c.done; _c = _b.next()) {\n                    var math = _c.value;\n                    if (math.state() >= MathItem_js_1.STATE.INSERTED) {\n                        math.state(MathItem_js_1.STATE.TYPESET, restore);\n                    }\n                }\n            }\n            catch (e_7_1) { e_7 = { error: e_7_1 }; }\n            finally {\n                try {\n                    if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n                }\n                finally { if (e_7) throw e_7.error; }\n            }\n        }\n        this.processed.clear('updateDocument');\n        return this;\n    };\n    HTMLDocument.prototype.documentStyleSheet = function () {\n        return this.outputJax.styleSheet(this);\n    };\n    HTMLDocument.prototype.documentPageElements = function () {\n        return this.outputJax.pageElements(this);\n    };\n    HTMLDocument.prototype.addStyles = function (styles) {\n        this.styles.push(styles);\n    };\n    HTMLDocument.prototype.getStyles = function () {\n        return this.styles;\n    };\n    HTMLDocument.KIND = 'HTML';\n    HTMLDocument.OPTIONS = __assign(__assign({}, MathDocument_js_1.AbstractMathDocument.OPTIONS), { renderActions: (0, Options_js_1.expandable)(__assign(__assign({}, MathDocument_js_1.AbstractMathDocument.OPTIONS.renderActions), { styles: [MathItem_js_1.STATE.INSERTED + 1, '', 'updateStyleSheet', false] })), MathList: HTMLMathList_js_1.HTMLMathList, MathItem: HTMLMathItem_js_1.HTMLMathItem, DomStrings: null });\n    return HTMLDocument;\n}(MathDocument_js_1.AbstractMathDocument));\nexports.HTMLDocument = HTMLDocument;\n//# sourceMappingURL=HTMLDocument.js.map", "\"use strict\";\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HTMLDomStrings = void 0;\nvar Options_js_1 = require(\"../../util/Options.js\");\nvar HTMLDomStrings = (function () {\n    function HTMLDomStrings(options) {\n        if (options === void 0) { options = null; }\n        var CLASS = this.constructor;\n        this.options = (0, Options_js_1.userOptions)((0, Options_js_1.defaultOptions)({}, CLASS.OPTIONS), options);\n        this.init();\n        this.getPatterns();\n    }\n    HTMLDomStrings.prototype.init = function () {\n        this.strings = [];\n        this.string = '';\n        this.snodes = [];\n        this.nodes = [];\n        this.stack = [];\n    };\n    HTMLDomStrings.prototype.getPatterns = function () {\n        var skip = (0, Options_js_1.makeArray)(this.options['skipHtmlTags']);\n        var ignore = (0, Options_js_1.makeArray)(this.options['ignoreHtmlClass']);\n        var process = (0, Options_js_1.makeArray)(this.options['processHtmlClass']);\n        this.skipHtmlTags = new RegExp('^(?:' + skip.join('|') + ')$', 'i');\n        this.ignoreHtmlClass = new RegExp('(?:^| )(?:' + ignore.join('|') + ')(?: |$)');\n        this.processHtmlClass = new RegExp('(?:^| )(?:' + process + ')(?: |$)');\n    };\n    HTMLDomStrings.prototype.pushString = function () {\n        if (this.string.match(/\\S/)) {\n            this.strings.push(this.string);\n            this.nodes.push(this.snodes);\n        }\n        this.string = '';\n        this.snodes = [];\n    };\n    HTMLDomStrings.prototype.extendString = function (node, text) {\n        this.snodes.push([node, text.length]);\n        this.string += text;\n    };\n    HTMLDomStrings.prototype.handleText = function (node, ignore) {\n        if (!ignore) {\n            this.extendString(node, this.adaptor.value(node));\n        }\n        return this.adaptor.next(node);\n    };\n    HTMLDomStrings.prototype.handleTag = function (node, ignore) {\n        if (!ignore) {\n            var text = this.options['includeHtmlTags'][this.adaptor.kind(node)];\n            this.extendString(node, text);\n        }\n        return this.adaptor.next(node);\n    };\n    HTMLDomStrings.prototype.handleContainer = function (node, ignore) {\n        this.pushString();\n        var cname = this.adaptor.getAttribute(node, 'class') || '';\n        var tname = this.adaptor.kind(node) || '';\n        var process = this.processHtmlClass.exec(cname);\n        var next = node;\n        if (this.adaptor.firstChild(node) && !this.adaptor.getAttribute(node, 'data-MJX') &&\n            (process || !this.skipHtmlTags.exec(tname))) {\n            if (this.adaptor.next(node)) {\n                this.stack.push([this.adaptor.next(node), ignore]);\n            }\n            next = this.adaptor.firstChild(node);\n            ignore = (ignore || this.ignoreHtmlClass.exec(cname)) && !process;\n        }\n        else {\n            next = this.adaptor.next(node);\n        }\n        return [next, ignore];\n    };\n    HTMLDomStrings.prototype.handleOther = function (node, _ignore) {\n        this.pushString();\n        return this.adaptor.next(node);\n    };\n    HTMLDomStrings.prototype.find = function (node) {\n        var _a, _b;\n        this.init();\n        var stop = this.adaptor.next(node);\n        var ignore = false;\n        var include = this.options['includeHtmlTags'];\n        while (node && node !== stop) {\n            var kind = this.adaptor.kind(node);\n            if (kind === '#text') {\n                node = this.handleText(node, ignore);\n            }\n            else if (include.hasOwnProperty(kind)) {\n                node = this.handleTag(node, ignore);\n            }\n            else if (kind) {\n                _a = __read(this.handleContainer(node, ignore), 2), node = _a[0], ignore = _a[1];\n            }\n            else {\n                node = this.handleOther(node, ignore);\n            }\n            if (!node && this.stack.length) {\n                this.pushString();\n                _b = __read(this.stack.pop(), 2), node = _b[0], ignore = _b[1];\n            }\n        }\n        this.pushString();\n        var result = [this.strings, this.nodes];\n        this.init();\n        return result;\n    };\n    HTMLDomStrings.OPTIONS = {\n        skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code', 'annotation', 'annotation-xml'],\n        includeHtmlTags: { br: '\\n', wbr: '', '#comment': '' },\n        ignoreHtmlClass: 'mathjax_ignore',\n        processHtmlClass: 'mathjax_process'\n    };\n    return HTMLDomStrings;\n}());\nexports.HTMLDomStrings = HTMLDomStrings;\n//# sourceMappingURL=HTMLDomStrings.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HTMLHandler = void 0;\nvar Handler_js_1 = require(\"../../core/Handler.js\");\nvar HTMLDocument_js_1 = require(\"./HTMLDocument.js\");\nvar HTMLHandler = (function (_super) {\n    __extends(HTMLHandler, _super);\n    function HTMLHandler() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.documentClass = HTMLDocument_js_1.HTMLDocument;\n        return _this;\n    }\n    HTMLHandler.prototype.handlesDocument = function (document) {\n        var adaptor = this.adaptor;\n        if (typeof (document) === 'string') {\n            try {\n                document = adaptor.parse(document, 'text/html');\n            }\n            catch (err) { }\n        }\n        if (document instanceof adaptor.window.Document ||\n            document instanceof adaptor.window.HTMLElement ||\n            document instanceof adaptor.window.DocumentFragment) {\n            return true;\n        }\n        return false;\n    };\n    HTMLHandler.prototype.create = function (document, options) {\n        var adaptor = this.adaptor;\n        if (typeof (document) === 'string') {\n            document = adaptor.parse(document, 'text/html');\n        }\n        else if (document instanceof adaptor.window.HTMLElement ||\n            document instanceof adaptor.window.DocumentFragment) {\n            var child = document;\n            document = adaptor.parse('', 'text/html');\n            adaptor.append(adaptor.body(document), child);\n        }\n        return _super.prototype.create.call(this, document, options);\n    };\n    return HTMLHandler;\n}(Handler_js_1.AbstractHandler));\nexports.HTMLHandler = HTMLHandler;\n//# sourceMappingURL=HTMLHandler.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HTMLMathItem = void 0;\nvar MathItem_js_1 = require(\"../../core/MathItem.js\");\nvar HTMLMathItem = (function (_super) {\n    __extends(HTMLMathItem, _super);\n    function HTMLMathItem(math, jax, display, start, end) {\n        if (display === void 0) { display = true; }\n        if (start === void 0) { start = { node: null, n: 0, delim: '' }; }\n        if (end === void 0) { end = { node: null, n: 0, delim: '' }; }\n        return _super.call(this, math, jax, display, start, end) || this;\n    }\n    Object.defineProperty(HTMLMathItem.prototype, \"adaptor\", {\n        get: function () {\n            return this.inputJax.adaptor;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    HTMLMathItem.prototype.updateDocument = function (_html) {\n        if (this.state() < MathItem_js_1.STATE.INSERTED) {\n            if (this.inputJax.processStrings) {\n                var node = this.start.node;\n                if (node === this.end.node) {\n                    if (this.end.n && this.end.n < this.adaptor.value(this.end.node).length) {\n                        this.adaptor.split(this.end.node, this.end.n);\n                    }\n                    if (this.start.n) {\n                        node = this.adaptor.split(this.start.node, this.start.n);\n                    }\n                    this.adaptor.replace(this.typesetRoot, node);\n                }\n                else {\n                    if (this.start.n) {\n                        node = this.adaptor.split(node, this.start.n);\n                    }\n                    while (node !== this.end.node) {\n                        var next = this.adaptor.next(node);\n                        this.adaptor.remove(node);\n                        node = next;\n                    }\n                    this.adaptor.insert(this.typesetRoot, node);\n                    if (this.end.n < this.adaptor.value(node).length) {\n                        this.adaptor.split(node, this.end.n);\n                    }\n                    this.adaptor.remove(node);\n                }\n            }\n            else {\n                this.adaptor.replace(this.typesetRoot, this.start.node);\n            }\n            this.start.node = this.end.node = this.typesetRoot;\n            this.start.n = this.end.n = 0;\n            this.state(MathItem_js_1.STATE.INSERTED);\n        }\n    };\n    HTMLMathItem.prototype.updateStyleSheet = function (document) {\n        document.addStyleSheet();\n    };\n    HTMLMathItem.prototype.removeFromDocument = function (restore) {\n        if (restore === void 0) { restore = false; }\n        if (this.state() >= MathItem_js_1.STATE.TYPESET) {\n            var adaptor = this.adaptor;\n            var node = this.start.node;\n            var math = adaptor.text('');\n            if (restore) {\n                var text = this.start.delim + this.math + this.end.delim;\n                if (this.inputJax.processStrings) {\n                    math = adaptor.text(text);\n                }\n                else {\n                    var doc = adaptor.parse(text, 'text/html');\n                    math = adaptor.firstChild(adaptor.body(doc));\n                }\n            }\n            if (adaptor.parent(node)) {\n                adaptor.replace(math, node);\n            }\n            this.start.node = this.end.node = math;\n            this.start.n = this.end.n = 0;\n        }\n    };\n    return HTMLMathItem;\n}(MathItem_js_1.AbstractMathItem));\nexports.HTMLMathItem = HTMLMathItem;\n//# sourceMappingURL=HTMLMathItem.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.HTMLMathList = void 0;\nvar MathList_js_1 = require(\"../../core/MathList.js\");\nvar HTMLMathList = (function (_super) {\n    __extends(HTMLMathList, _super);\n    function HTMLMathList() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return HTMLMathList;\n}(MathList_js_1.AbstractMathList));\nexports.HTMLMathList = HTMLMathList;\n//# sourceMappingURL=HTMLMathList.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.BitFieldClass = exports.BitField = void 0;\nvar BitField = (function () {\n    function BitField() {\n        this.bits = 0;\n    }\n    BitField.allocate = function () {\n        var e_1, _a;\n        var names = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            names[_i] = arguments[_i];\n        }\n        try {\n            for (var names_1 = __values(names), names_1_1 = names_1.next(); !names_1_1.done; names_1_1 = names_1.next()) {\n                var name_1 = names_1_1.value;\n                if (this.has(name_1)) {\n                    throw new Error('Bit already allocated for ' + name_1);\n                }\n                if (this.next === BitField.MAXBIT) {\n                    throw new Error('Maximum number of bits already allocated');\n                }\n                this.names.set(name_1, this.next);\n                this.next <<= 1;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (names_1_1 && !names_1_1.done && (_a = names_1.return)) _a.call(names_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n    };\n    BitField.has = function (name) {\n        return this.names.has(name);\n    };\n    BitField.prototype.set = function (name) {\n        this.bits |= this.getBit(name);\n    };\n    BitField.prototype.clear = function (name) {\n        this.bits &= ~this.getBit(name);\n    };\n    BitField.prototype.isSet = function (name) {\n        return !!(this.bits & this.getBit(name));\n    };\n    BitField.prototype.reset = function () {\n        this.bits = 0;\n    };\n    BitField.prototype.getBit = function (name) {\n        var bit = this.constructor.names.get(name);\n        if (!bit) {\n            throw new Error('Unknown bit-field name: ' + name);\n        }\n        return bit;\n    };\n    BitField.MAXBIT = 1 << 31;\n    BitField.next = 1;\n    BitField.names = new Map();\n    return BitField;\n}());\nexports.BitField = BitField;\nfunction BitFieldClass() {\n    var names = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        names[_i] = arguments[_i];\n    }\n    var Bits = (function (_super) {\n        __extends(Bits, _super);\n        function Bits() {\n            return _super !== null && _super.apply(this, arguments) || this;\n        }\n        return Bits;\n    }(BitField));\n    Bits.allocate.apply(Bits, __spreadArray([], __read(names), false));\n    return Bits;\n}\nexports.BitFieldClass = BitFieldClass;\n//# sourceMappingURL=BitField.js.map", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.FunctionList = void 0;\nvar PrioritizedList_js_1 = require(\"./PrioritizedList.js\");\nvar FunctionList = (function (_super) {\n    __extends(FunctionList, _super);\n    function FunctionList() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    FunctionList.prototype.execute = function () {\n        var e_1, _a;\n        var data = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            data[_i] = arguments[_i];\n        }\n        try {\n            for (var _b = __values(this), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                var result = item.item.apply(item, __spreadArray([], __read(data), false));\n                if (result === false) {\n                    return false;\n                }\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return true;\n    };\n    FunctionList.prototype.asyncExecute = function () {\n        var data = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            data[_i] = arguments[_i];\n        }\n        var i = -1;\n        var items = this.items;\n        return new Promise(function (ok, fail) {\n            (function execute() {\n                var _a;\n                while (++i < items.length) {\n                    var result = (_a = items[i]).item.apply(_a, __spreadArray([], __read(data), false));\n                    if (result instanceof Promise) {\n                        result.then(execute).catch(function (err) { return fail(err); });\n                        return;\n                    }\n                    if (result === false) {\n                        ok(false);\n                        return;\n                    }\n                }\n                ok(true);\n            })();\n        });\n    };\n    return FunctionList;\n}(PrioritizedList_js_1.PrioritizedList));\nexports.FunctionList = FunctionList;\n//# sourceMappingURL=FunctionList.js.map", "\"use strict\";\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar __read = (this && this.__read) || function (o, n) {\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n    if (!m) return o;\n    var i = m.call(o), r, ar = [], e;\n    try {\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n    }\n    catch (error) { e = { error: error }; }\n    finally {\n        try {\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\n        }\n        finally { if (e) throw e.error; }\n    }\n    return ar;\n};\nvar __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nvar __values = (this && this.__values) || function(o) {\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n    if (m) return m.call(o);\n    if (o && typeof o.length === \"number\") return {\n        next: function () {\n            if (o && i >= o.length) o = void 0;\n            return { value: o && o[i++], done: !o };\n        }\n    };\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.LinkedList = exports.ListItem = exports.END = void 0;\nexports.END = Symbol();\nvar ListItem = (function () {\n    function ListItem(data) {\n        if (data === void 0) { data = null; }\n        this.next = null;\n        this.prev = null;\n        this.data = data;\n    }\n    return ListItem;\n}());\nexports.ListItem = ListItem;\nvar LinkedList = (function () {\n    function LinkedList() {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        this.list = new ListItem(exports.END);\n        this.list.next = this.list.prev = this.list;\n        this.push.apply(this, __spreadArray([], __read(args), false));\n    }\n    LinkedList.prototype.isBefore = function (a, b) {\n        return a < b;\n    };\n    LinkedList.prototype.push = function () {\n        var e_1, _a;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        try {\n            for (var args_1 = __values(args), args_1_1 = args_1.next(); !args_1_1.done; args_1_1 = args_1.next()) {\n                var data = args_1_1.value;\n                var item = new ListItem(data);\n                item.next = this.list;\n                item.prev = this.list.prev;\n                this.list.prev = item;\n                item.prev.next = item;\n            }\n        }\n        catch (e_1_1) { e_1 = { error: e_1_1 }; }\n        finally {\n            try {\n                if (args_1_1 && !args_1_1.done && (_a = args_1.return)) _a.call(args_1);\n            }\n            finally { if (e_1) throw e_1.error; }\n        }\n        return this;\n    };\n    LinkedList.prototype.pop = function () {\n        var item = this.list.prev;\n        if (item.data === exports.END) {\n            return null;\n        }\n        this.list.prev = item.prev;\n        item.prev.next = this.list;\n        item.next = item.prev = null;\n        return item.data;\n    };\n    LinkedList.prototype.unshift = function () {\n        var e_2, _a;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        try {\n            for (var _b = __values(args.slice(0).reverse()), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var data = _c.value;\n                var item = new ListItem(data);\n                item.next = this.list.next;\n                item.prev = this.list;\n                this.list.next = item;\n                item.next.prev = item;\n            }\n        }\n        catch (e_2_1) { e_2 = { error: e_2_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_2) throw e_2.error; }\n        }\n        return this;\n    };\n    LinkedList.prototype.shift = function () {\n        var item = this.list.next;\n        if (item.data === exports.END) {\n            return null;\n        }\n        this.list.next = item.next;\n        item.next.prev = this.list;\n        item.next = item.prev = null;\n        return item.data;\n    };\n    LinkedList.prototype.remove = function () {\n        var e_3, _a;\n        var items = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            items[_i] = arguments[_i];\n        }\n        var map = new Map();\n        try {\n            for (var items_1 = __values(items), items_1_1 = items_1.next(); !items_1_1.done; items_1_1 = items_1.next()) {\n                var item_1 = items_1_1.value;\n                map.set(item_1, true);\n            }\n        }\n        catch (e_3_1) { e_3 = { error: e_3_1 }; }\n        finally {\n            try {\n                if (items_1_1 && !items_1_1.done && (_a = items_1.return)) _a.call(items_1);\n            }\n            finally { if (e_3) throw e_3.error; }\n        }\n        var item = this.list.next;\n        while (item.data !== exports.END) {\n            var next = item.next;\n            if (map.has(item.data)) {\n                item.prev.next = item.next;\n                item.next.prev = item.prev;\n                item.next = item.prev = null;\n            }\n            item = next;\n        }\n    };\n    LinkedList.prototype.clear = function () {\n        this.list.next.prev = this.list.prev.next = null;\n        this.list.next = this.list.prev = this.list;\n        return this;\n    };\n    LinkedList.prototype[Symbol.iterator] = function () {\n        var current;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    current = this.list.next;\n                    _a.label = 1;\n                case 1:\n                    if (!(current.data !== exports.END)) return [3, 3];\n                    return [4, current.data];\n                case 2:\n                    _a.sent();\n                    current = current.next;\n                    return [3, 1];\n                case 3: return [2];\n            }\n        });\n    };\n    LinkedList.prototype.reversed = function () {\n        var current;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    current = this.list.prev;\n                    _a.label = 1;\n                case 1:\n                    if (!(current.data !== exports.END)) return [3, 3];\n                    return [4, current.data];\n                case 2:\n                    _a.sent();\n                    current = current.prev;\n                    return [3, 1];\n                case 3: return [2];\n            }\n        });\n    };\n    LinkedList.prototype.insert = function (data, isBefore) {\n        if (isBefore === void 0) { isBefore = null; }\n        if (isBefore === null) {\n            isBefore = this.isBefore.bind(this);\n        }\n        var item = new ListItem(data);\n        var cur = this.list.next;\n        while (cur.data !== exports.END && isBefore(cur.data, item.data)) {\n            cur = cur.next;\n        }\n        item.prev = cur.prev;\n        item.next = cur;\n        cur.prev.next = cur.prev = item;\n        return this;\n    };\n    LinkedList.prototype.sort = function (isBefore) {\n        var e_4, _a;\n        if (isBefore === void 0) { isBefore = null; }\n        if (isBefore === null) {\n            isBefore = this.isBefore.bind(this);\n        }\n        var lists = [];\n        try {\n            for (var _b = __values(this), _c = _b.next(); !_c.done; _c = _b.next()) {\n                var item = _c.value;\n                lists.push(new LinkedList(item));\n            }\n        }\n        catch (e_4_1) { e_4 = { error: e_4_1 }; }\n        finally {\n            try {\n                if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n            }\n            finally { if (e_4) throw e_4.error; }\n        }\n        this.list.next = this.list.prev = this.list;\n        while (lists.length > 1) {\n            var l1 = lists.shift();\n            var l2 = lists.shift();\n            l1.merge(l2, isBefore);\n            lists.push(l1);\n        }\n        if (lists.length) {\n            this.list = lists[0].list;\n        }\n        return this;\n    };\n    LinkedList.prototype.merge = function (list, isBefore) {\n        var _a, _b, _c, _d, _e;\n        if (isBefore === void 0) { isBefore = null; }\n        if (isBefore === null) {\n            isBefore = this.isBefore.bind(this);\n        }\n        var lcur = this.list.next;\n        var mcur = list.list.next;\n        while (lcur.data !== exports.END && mcur.data !== exports.END) {\n            if (isBefore(mcur.data, lcur.data)) {\n                _a = __read([lcur, mcur], 2), mcur.prev.next = _a[0], lcur.prev.next = _a[1];\n                _b = __read([lcur.prev, mcur.prev], 2), mcur.prev = _b[0], lcur.prev = _b[1];\n                _c = __read([list.list, this.list], 2), this.list.prev.next = _c[0], list.list.prev.next = _c[1];\n                _d = __read([list.list.prev, this.list.prev], 2), this.list.prev = _d[0], list.list.prev = _d[1];\n                _e = __read([mcur.next, lcur], 2), lcur = _e[0], mcur = _e[1];\n            }\n            else {\n                lcur = lcur.next;\n            }\n        }\n        if (mcur.data !== exports.END) {\n            this.list.prev.next = list.list.next;\n            list.list.next.prev = this.list.prev;\n            list.list.prev.next = this.list;\n            this.list.prev = list.list.prev;\n            list.list.next = list.list.prev = list.list;\n        }\n        return this;\n    };\n    return LinkedList;\n}());\nexports.LinkedList = LinkedList;\n//# sourceMappingURL=LinkedList.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.PrioritizedList = void 0;\nvar PrioritizedList = (function () {\n    function PrioritizedList() {\n        this.items = [];\n        this.items = [];\n    }\n    PrioritizedList.prototype[Symbol.iterator] = function () {\n        var i = 0;\n        var items = this.items;\n        return {\n            next: function () {\n                return { value: items[i++], done: (i > items.length) };\n            }\n        };\n    };\n    PrioritizedList.prototype.add = function (item, priority) {\n        if (priority === void 0) { priority = PrioritizedList.DEFAULTPRIORITY; }\n        var i = this.items.length;\n        do {\n            i--;\n        } while (i >= 0 && priority < this.items[i].priority);\n        this.items.splice(i + 1, 0, { item: item, priority: priority });\n        return item;\n    };\n    PrioritizedList.prototype.remove = function (item) {\n        var i = this.items.length;\n        do {\n            i--;\n        } while (i >= 0 && this.items[i].item !== item);\n        if (i >= 0) {\n            this.items.splice(i, 1);\n        }\n    };\n    PrioritizedList.DEFAULTPRIORITY = 5;\n    return PrioritizedList;\n}());\nexports.PrioritizedList = PrioritizedList;\n//# sourceMappingURL=PrioritizedList.js.map"], "names": [], "sourceRoot": ""}