{"version": 3, "file": "8381.0291906ada65d4e5df4e.js?v=0291906ada65d4e5df4e", "mappings": ";;;;;;;;;;AAAA;AACA,cAAc;AACd,kBAAkB,kBAAkB;AACpC;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA,kBAAkB,MAAM;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/pig.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\n// builtin funcs taken from trunk revision 1303237\nvar pBuiltins = \"ABS ACOS ARITY ASIN ATAN AVG BAGSIZE BINSTORAGE BLOOM BUILDBLOOM CBRT CEIL \"\n    + \"CONCAT COR COS COSH COUNT COUNT_STAR COV CONSTANTSIZE CUBEDIMENSIONS DIFF DISTINCT DOUBLEABS \"\n    + \"DOUBLEAVG DOUBLEBASE DOUBLEMAX DOUBLEMIN DOUBLEROUND DOUBLESUM EXP FLOOR FLOATABS FLOATAVG \"\n    + \"FLOATMAX FLOATMIN FLOATROUND FLOATSUM GENERICINVOKER INDEXOF INTABS INTAVG INTMAX INTMIN \"\n    + \"INTSUM INVOKEFORDOUBLE INVOKEFORFLOAT INVOKEFORINT INVOKEFORLONG INVOKEFORSTRING INVOKER \"\n    + \"ISEMPTY JSONLOADER JSONMETADATA JSONSTORAGE LAST_INDEX_OF LCFIRST LOG LOG10 LOWER LONGABS \"\n    + \"LONGAVG LONGMAX LONGMIN LONGSUM MAX MIN MAPSIZE MONITOREDUDF NONDETERMINISTIC OUTPUTSCHEMA  \"\n    + \"PIGSTORAGE PIGSTREAMING RANDOM REGEX_EXTRACT REGEX_EXTRACT_ALL REPLACE ROUND SIN SINH SIZE \"\n    + \"SQRT STRSPLIT SUBSTRING SUM STRINGCONCAT STRINGMAX STRINGMIN STRINGSIZE TAN TANH TOBAG \"\n    + \"TOKENIZE TOMAP TOP TOTUPLE TRIM TEXTLOADER TUPLESIZE UCFIRST UPPER UTF8STORAGECONVERTER \";\n\n// taken from QueryLexer.g\nvar pKeywords = \"VOID IMPORT RETURNS DEFINE LOAD FILTER FOREACH ORDER CUBE DISTINCT COGROUP \"\n    + \"JOIN CROSS UNION SPLIT INTO IF OTHERWISE ALL AS BY USING INNER OUTER ONSCHEMA PARALLEL \"\n    + \"PARTITION GROUP AND OR NOT GENERATE FLATTEN ASC DESC IS STREAM THROUGH STORE MAPREDUCE \"\n    + \"SHIP CACHE INPUT OUTPUT STDERROR STDIN STDOUT LIMIT SAMPLE LEFT RIGHT FULL EQ GT LT GTE LTE \"\n    + \"NEQ MATCHES TRUE FALSE DUMP\";\n\n// data types\nvar pTypes = \"BOOLEAN INT LONG FLOAT DOUBLE CHARARRAY BYTEARRAY BAG TUPLE MAP \";\n\nvar builtins = words(pBuiltins), keywords = words(pKeywords), types = words(pTypes)\n\nvar isOperatorChar = /[*+\\-%<>=&?:\\/!|]/;\n\nfunction chain(stream, state, f) {\n  state.tokenize = f;\n  return f(stream, state);\n}\n\nfunction tokenComment(stream, state) {\n  var isEnd = false;\n  var ch;\n  while(ch = stream.next()) {\n    if(ch == \"/\" && isEnd) {\n      state.tokenize = tokenBase;\n      break;\n    }\n    isEnd = (ch == \"*\");\n  }\n  return \"comment\";\n}\n\nfunction tokenString(quote) {\n  return function(stream, state) {\n    var escaped = false, next, end = false;\n    while((next = stream.next()) != null) {\n      if (next == quote && !escaped) {\n        end = true; break;\n      }\n      escaped = !escaped && next == \"\\\\\";\n    }\n    if (end || !escaped)\n      state.tokenize = tokenBase;\n    return \"error\";\n  };\n}\n\n\nfunction tokenBase(stream, state) {\n  var ch = stream.next();\n\n  // is a start of string?\n  if (ch == '\"' || ch == \"'\")\n    return chain(stream, state, tokenString(ch));\n  // is it one of the special chars\n  else if(/[\\[\\]{}\\(\\),;\\.]/.test(ch))\n    return null;\n  // is it a number?\n  else if(/\\d/.test(ch)) {\n    stream.eatWhile(/[\\w\\.]/);\n    return \"number\";\n  }\n  // multi line comment or operator\n  else if (ch == \"/\") {\n    if (stream.eat(\"*\")) {\n      return chain(stream, state, tokenComment);\n    }\n    else {\n      stream.eatWhile(isOperatorChar);\n      return \"operator\";\n    }\n  }\n  // single line comment or operator\n  else if (ch==\"-\") {\n    if(stream.eat(\"-\")){\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    else {\n      stream.eatWhile(isOperatorChar);\n      return \"operator\";\n    }\n  }\n  // is it an operator\n  else if (isOperatorChar.test(ch)) {\n    stream.eatWhile(isOperatorChar);\n    return \"operator\";\n  }\n  else {\n    // get the while word\n    stream.eatWhile(/[\\w\\$_]/);\n    // is it one of the listed keywords?\n    if (keywords && keywords.propertyIsEnumerable(stream.current().toUpperCase())) {\n      //keywords can be used as variables like flatten(group), group.$0 etc..\n      if (!stream.eat(\")\") && !stream.eat(\".\"))\n        return \"keyword\";\n    }\n    // is it one of the builtin functions?\n    if (builtins && builtins.propertyIsEnumerable(stream.current().toUpperCase()))\n      return \"builtin\";\n    // is it one of the listed types?\n    if (types && types.propertyIsEnumerable(stream.current().toUpperCase()))\n      return \"type\";\n    // default is a 'variable'\n    return \"variable\";\n  }\n}\n\n// Interface\nexport const pig = {\n  name: \"pig\",\n\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      startOfLine: true\n    };\n  },\n\n  token: function(stream, state) {\n    if(stream.eatSpace()) return null;\n    var style = state.tokenize(stream, state);\n    return style;\n  },\n\n  languageData: {\n    autocomplete: (pBuiltins + pTypes + pKeywords).split(\" \")\n  }\n};\n"], "names": [], "sourceRoot": ""}