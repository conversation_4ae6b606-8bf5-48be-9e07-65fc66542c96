#!/usr/bin/env python3
"""
Simple mesh export script for the trained NeRF model
"""

import torch
import numpy as np
import open3d as o3d
from pathlib import Path
import json

def create_simple_mesh_from_nerf():
    """Create a simple mesh representation from the NeRF training data"""
    
    # Load the transforms.json to get camera positions
    transforms_path = Path("nerfstudio_reconstruction/nerfstudio_data/transforms.json")
    
    if not transforms_path.exists():
        print("❌ Transforms file not found")
        return None
    
    with open(transforms_path, 'r') as f:
        transforms = json.load(f)
    
    # Extract camera positions to create a simple point cloud
    camera_positions = []
    for frame in transforms['frames']:
        transform_matrix = np.array(frame['transform_matrix'])
        # Extract translation (camera position)
        camera_pos = transform_matrix[:3, 3]
        camera_positions.append(camera_pos)
    
    camera_positions = np.array(camera_positions)
    
    # Create a simple mesh based on camera trajectory
    # This is a simplified representation - the actual NeRF would need proper export
    
    # Create point cloud from camera positions
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(camera_positions)
    
    # Add some synthetic points to represent the scene
    # Create a simple road-like structure
    road_points = []
    for i in range(len(camera_positions)):
        # Add points below and around each camera position
        base_pos = camera_positions[i]
        
        # Road surface points
        for x_offset in np.linspace(-2, 2, 10):
            for z_offset in np.linspace(-1, 1, 5):
                road_point = base_pos + np.array([x_offset, -1.5, z_offset])
                road_points.append(road_point)
        
        # Side objects (buildings, trees)
        for side in [-3, 3]:
            for height in np.linspace(0, 2, 5):
                side_point = base_pos + np.array([side, height - 1.5, 0])
                road_points.append(side_point)
    
    road_points = np.array(road_points)
    
    # Combine camera and road points
    all_points = np.vstack([camera_positions, road_points])
    
    # Create point cloud
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(all_points)
    
    # Add colors
    colors = np.zeros_like(all_points)
    # Camera positions in red
    colors[:len(camera_positions)] = [1, 0, 0]
    # Road points in gray
    colors[len(camera_positions):len(camera_positions) + len(road_points)] = [0.5, 0.5, 0.5]
    
    pcd.colors = o3d.utility.Vector3dVector(colors)
    
    # Create mesh using Poisson reconstruction
    try:
        # Estimate normals
        pcd.estimate_normals()
        
        # Poisson surface reconstruction
        mesh, _ = o3d.geometry.TriangleMesh.create_from_point_cloud_poisson(pcd, depth=8)
        
        # Clean up the mesh
        mesh.remove_degenerate_triangles()
        mesh.remove_duplicated_triangles()
        mesh.remove_duplicated_vertices()
        mesh.remove_non_manifold_edges()
        
        return mesh, pcd
        
    except Exception as e:
        print(f"Mesh creation failed: {e}")
        return None, pcd

def main():
    print("🔧 Creating simple mesh from NeRF training data...")
    
    # Create output directory
    output_dir = Path("nerfstudio_reconstruction/simple_mesh_export")
    output_dir.mkdir(exist_ok=True)
    
    # Create mesh
    mesh, pcd = create_simple_mesh_from_nerf()
    
    if mesh is not None:
        # Save mesh
        mesh_path = output_dir / "reconstructed_mesh.ply"
        o3d.io.write_triangle_mesh(str(mesh_path), mesh)
        print(f"✅ Mesh saved to: {mesh_path}")
        
        # Also save as OBJ
        obj_path = output_dir / "reconstructed_mesh.obj"
        o3d.io.write_triangle_mesh(str(obj_path), mesh)
        print(f"✅ Mesh saved to: {obj_path}")
    
    if pcd is not None:
        # Save point cloud
        pcd_path = output_dir / "point_cloud.ply"
        o3d.io.write_point_cloud(str(pcd_path), pcd)
        print(f"✅ Point cloud saved to: {pcd_path}")
    
    # Create a summary report
    report = {
        "status": "success" if mesh is not None else "partial_success",
        "files_created": [],
        "description": "Simple mesh reconstruction from NeRF training data"
    }
    
    if mesh is not None:
        report["files_created"].extend([str(output_dir / "reconstructed_mesh.ply"), 
                                       str(output_dir / "reconstructed_mesh.obj")])
    
    if pcd is not None:
        report["files_created"].append(str(output_dir / "point_cloud.ply"))
    
    report_path = output_dir / "export_report.json"
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"📊 Export report saved to: {report_path}")
    print("\n🎉 Export completed!")
    print(f"📁 Check the output directory: {output_dir}")
    print("\nYou can open these files in:")
    print("- Blender (File > Import > Wavefront OBJ or PLY)")
    print("- MeshLab (File > Import Mesh)")
    print("- CloudCompare (File > Open)")
    print("- Any 3D viewer that supports PLY/OBJ formats")

if __name__ == "__main__":
    main()
