(self["webpackChunk_JUPYTERLAB_CORE_OUTPUT"] = self["webpackChunk_JUPYTERLAB_CORE_OUTPUT"] || []).push([[7995],{

/***/ 57995:
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXPORTS
__webpack_require__.d(__webpack_exports__, {
  ADDITIONAL_PROPERTIES_KEY: () => (/* reexport */ ADDITIONAL_PROPERTIES_KEY),
  ADDITIONAL_PROPERTY_FLAG: () => (/* reexport */ ADDITIONAL_PROPERTY_FLAG),
  ALL_OF_KEY: () => (/* reexport */ ALL_OF_KEY),
  ANY_OF_KEY: () => (/* reexport */ ANY_OF_KEY),
  CONST_KEY: () => (/* reexport */ CONST_KEY),
  DEFAULT_KEY: () => (/* reexport */ DEFAULT_KEY),
  DEFINITIONS_KEY: () => (/* reexport */ DEFINITIONS_KEY),
  DEPENDENCIES_KEY: () => (/* reexport */ DEPENDENCIES_KEY),
  ENUM_KEY: () => (/* reexport */ ENUM_KEY),
  ERRORS_KEY: () => (/* reexport */ ERRORS_KEY),
  ErrorSchemaBuilder: () => (/* reexport */ ErrorSchemaBuilder),
  ID_KEY: () => (/* reexport */ ID_KEY),
  IF_KEY: () => (/* reexport */ IF_KEY),
  ITEMS_KEY: () => (/* reexport */ ITEMS_KEY),
  JUNK_OPTION_ID: () => (/* reexport */ JUNK_OPTION_ID),
  NAME_KEY: () => (/* reexport */ NAME_KEY),
  ONE_OF_KEY: () => (/* reexport */ ONE_OF_KEY),
  PROPERTIES_KEY: () => (/* reexport */ PROPERTIES_KEY),
  REF_KEY: () => (/* reexport */ REF_KEY),
  REQUIRED_KEY: () => (/* reexport */ REQUIRED_KEY),
  RJSF_ADDITONAL_PROPERTIES_FLAG: () => (/* reexport */ RJSF_ADDITONAL_PROPERTIES_FLAG),
  ROOT_SCHEMA_PREFIX: () => (/* reexport */ ROOT_SCHEMA_PREFIX),
  SUBMIT_BTN_OPTIONS_KEY: () => (/* reexport */ SUBMIT_BTN_OPTIONS_KEY),
  TranslatableString: () => (/* reexport */ TranslatableString),
  UI_FIELD_KEY: () => (/* reexport */ UI_FIELD_KEY),
  UI_GLOBAL_OPTIONS_KEY: () => (/* reexport */ UI_GLOBAL_OPTIONS_KEY),
  UI_OPTIONS_KEY: () => (/* reexport */ UI_OPTIONS_KEY),
  UI_WIDGET_KEY: () => (/* reexport */ UI_WIDGET_KEY),
  allowAdditionalItems: () => (/* reexport */ allowAdditionalItems),
  ariaDescribedByIds: () => (/* reexport */ ariaDescribedByIds),
  asNumber: () => (/* reexport */ asNumber),
  canExpand: () => (/* reexport */ canExpand),
  createErrorHandler: () => (/* reexport */ createErrorHandler),
  createSchemaUtils: () => (/* reexport */ createSchemaUtils),
  dataURItoBlob: () => (/* reexport */ dataURItoBlob),
  deepEquals: () => (/* reexport */ deepEquals),
  descriptionId: () => (/* reexport */ descriptionId),
  englishStringTranslator: () => (/* reexport */ englishStringTranslator),
  enumOptionsDeselectValue: () => (/* reexport */ enumOptionsDeselectValue),
  enumOptionsIndexForValue: () => (/* reexport */ enumOptionsIndexForValue),
  enumOptionsIsSelected: () => (/* reexport */ enumOptionsIsSelected),
  enumOptionsSelectValue: () => (/* reexport */ enumOptionsSelectValue),
  enumOptionsValueForIndex: () => (/* reexport */ enumOptionsValueForIndex),
  errorId: () => (/* reexport */ errorId),
  examplesId: () => (/* reexport */ examplesId),
  findSchemaDefinition: () => (/* reexport */ findSchemaDefinition),
  getClosestMatchingOption: () => (/* reexport */ getClosestMatchingOption),
  getDateElementProps: () => (/* reexport */ getDateElementProps),
  getDefaultFormState: () => (/* reexport */ getDefaultFormState),
  getDiscriminatorFieldFromSchema: () => (/* reexport */ getDiscriminatorFieldFromSchema),
  getDisplayLabel: () => (/* reexport */ getDisplayLabel),
  getFirstMatchingOption: () => (/* reexport */ getFirstMatchingOption),
  getInputProps: () => (/* reexport */ getInputProps),
  getMatchingOption: () => (/* reexport */ getMatchingOption),
  getOptionMatchingSimpleDiscriminator: () => (/* reexport */ getOptionMatchingSimpleDiscriminator),
  getSchemaType: () => (/* reexport */ getSchemaType),
  getSubmitButtonOptions: () => (/* reexport */ getSubmitButtonOptions),
  getTemplate: () => (/* reexport */ getTemplate),
  getUiOptions: () => (/* reexport */ getUiOptions),
  getWidget: () => (/* reexport */ getWidget),
  guessType: () => (/* reexport */ guessType),
  hasWidget: () => (/* reexport */ hasWidget),
  hashForSchema: () => (/* reexport */ hashForSchema),
  helpId: () => (/* reexport */ helpId),
  isConstant: () => (/* reexport */ isConstant),
  isCustomWidget: () => (/* reexport */ isCustomWidget),
  isFilesArray: () => (/* reexport */ isFilesArray),
  isFixedItems: () => (/* reexport */ isFixedItems),
  isMultiSelect: () => (/* reexport */ isMultiSelect),
  isObject: () => (/* reexport */ isObject),
  isSelect: () => (/* reexport */ isSelect),
  labelValue: () => (/* reexport */ labelValue),
  localToUTC: () => (/* reexport */ localToUTC),
  mergeDefaultsWithFormData: () => (/* reexport */ mergeDefaultsWithFormData),
  mergeObjects: () => (/* reexport */ mergeObjects),
  mergeSchemas: () => (/* reexport */ mergeSchemas),
  mergeValidationData: () => (/* reexport */ mergeValidationData),
  optionId: () => (/* reexport */ optionId),
  optionsList: () => (/* reexport */ optionsList),
  orderProperties: () => (/* reexport */ orderProperties),
  pad: () => (/* reexport */ pad),
  parseDateString: () => (/* reexport */ parseDateString),
  rangeSpec: () => (/* reexport */ rangeSpec),
  replaceStringParameters: () => (/* reexport */ replaceStringParameters),
  retrieveSchema: () => (/* reexport */ retrieveSchema),
  sanitizeDataForNewSchema: () => (/* reexport */ sanitizeDataForNewSchema),
  schemaParser: () => (/* reexport */ schemaParser),
  schemaRequiresTrueValue: () => (/* reexport */ schemaRequiresTrueValue),
  shouldRender: () => (/* reexport */ shouldRender),
  titleId: () => (/* reexport */ titleId),
  toConstant: () => (/* reexport */ toConstant),
  toDateString: () => (/* reexport */ toDateString),
  toErrorList: () => (/* reexport */ toErrorList),
  toErrorSchema: () => (/* reexport */ toErrorSchema),
  toIdSchema: () => (/* reexport */ toIdSchema),
  toPathSchema: () => (/* reexport */ toPathSchema),
  unwrapErrorHandler: () => (/* reexport */ unwrapErrorHandler),
  utcToLocal: () => (/* reexport */ utcToLocal),
  validationDataMerge: () => (/* reexport */ validationDataMerge),
  withIdRefPrefix: () => (/* reexport */ withIdRefPrefix)
});

;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/isObject.js
/** Determines whether a `thing` is an object for the purposes of RSJF. In this case, `thing` is an object if it has
 * the type `object` but is NOT null, an array or a File.
 *
 * @param thing - The thing to check to see whether it is an object
 * @returns - True if it is a non-null, non-array, non-File object
 */
function isObject(thing) {
    if (typeof File !== 'undefined' && thing instanceof File) {
        return false;
    }
    if (typeof Date !== 'undefined' && thing instanceof Date) {
        return false;
    }
    return typeof thing === 'object' && thing !== null && !Array.isArray(thing);
}
//# sourceMappingURL=isObject.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/allowAdditionalItems.js

/** Checks the schema to see if it is allowing additional items, by verifying that `schema.additionalItems` is an
 * object. The user is warned in the console if `schema.additionalItems` has the value `true`.
 *
 * @param schema - The schema object to check
 * @returns - True if additional items is allowed, otherwise false
 */
function allowAdditionalItems(schema) {
    if (schema.additionalItems === true) {
        console.warn('additionalItems=true is currently not supported');
    }
    return isObject(schema.additionalItems);
}
//# sourceMappingURL=allowAdditionalItems.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/asNumber.js
/** Attempts to convert the string into a number. If an empty string is provided, then `undefined` is returned. If a
 * `null` is provided, it is returned. If the string ends in a `.` then the string is returned because the user may be
 * in the middle of typing a float number. If a number ends in a pattern like `.0`, `.20`, `.030`, string is returned
 * because the user may be typing number that will end in a non-zero digit. Otherwise, the string is wrapped by
 * `Number()` and if that result is not `NaN`, that number will be returned, otherwise the string `value` will be.
 *
 * @param value - The string or null value to convert to a number
 * @returns - The `value` converted to a number when appropriate, otherwise the `value`
 */
function asNumber(value) {
    if (value === '') {
        return undefined;
    }
    if (value === null) {
        return null;
    }
    if (/\.$/.test(value)) {
        // '3.' can't really be considered a number even if it parses in js. The
        // user is most likely entering a float.
        return value;
    }
    if (/\.0$/.test(value)) {
        // we need to return this as a string here, to allow for input like 3.07
        return value;
    }
    if (/\.\d*0$/.test(value)) {
        // It's a number, that's cool - but we need it as a string so it doesn't screw
        // with the user when entering dollar amounts or other values (such as those with
        // specific precision or number of significant digits)
        return value;
    }
    const n = Number(value);
    const valid = typeof n === 'number' && !Number.isNaN(n);
    return valid ? n : value;
}
//# sourceMappingURL=asNumber.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/constants.js
/** Below are the list of all the keys into various elements of a RJSFSchema or UiSchema that are used by the various
 * utility functions. In addition to those keys, there are the special `ADDITIONAL_PROPERTY_FLAG` and
 * `RJSF_ADDITONAL_PROPERTIES_FLAG` flags that is added to a schema under certain conditions by the `retrieveSchema()`
 * utility.
 */
const ADDITIONAL_PROPERTY_FLAG = '__additional_property';
const ADDITIONAL_PROPERTIES_KEY = 'additionalProperties';
const ALL_OF_KEY = 'allOf';
const ANY_OF_KEY = 'anyOf';
const CONST_KEY = 'const';
const DEFAULT_KEY = 'default';
const DEFINITIONS_KEY = 'definitions';
const DEPENDENCIES_KEY = 'dependencies';
const ENUM_KEY = 'enum';
const ERRORS_KEY = '__errors';
const ID_KEY = '$id';
const IF_KEY = 'if';
const ITEMS_KEY = 'items';
const JUNK_OPTION_ID = '_$junk_option_schema_id$_';
const NAME_KEY = '$name';
const ONE_OF_KEY = 'oneOf';
const PROPERTIES_KEY = 'properties';
const REQUIRED_KEY = 'required';
const SUBMIT_BTN_OPTIONS_KEY = 'submitButtonOptions';
const REF_KEY = '$ref';
const RJSF_ADDITONAL_PROPERTIES_FLAG = '__rjsf_additionalProperties';
const ROOT_SCHEMA_PREFIX = '__rjsf_rootSchema';
const UI_FIELD_KEY = 'ui:field';
const UI_WIDGET_KEY = 'ui:widget';
const UI_OPTIONS_KEY = 'ui:options';
const UI_GLOBAL_OPTIONS_KEY = 'ui:globalOptions';
//# sourceMappingURL=constants.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/getUiOptions.js


/** Get all passed options from ui:options, and ui:<optionName>, returning them in an object with the `ui:`
 * stripped off. Any `globalOptions` will always be returned, unless they are overridden by options in the `uiSchema`.
 *
 * @param [uiSchema={}] - The UI Schema from which to get any `ui:xxx` options
 * @param [globalOptions={}] - The optional Global UI Schema from which to get any fallback `xxx` options
 * @returns - An object containing all the `ui:xxx` options with the `ui:` stripped off along with all `globalOptions`
 */
function getUiOptions(uiSchema = {}, globalOptions = {}) {
    return Object.keys(uiSchema)
        .filter((key) => key.indexOf('ui:') === 0)
        .reduce((options, key) => {
        const value = uiSchema[key];
        if (key === UI_WIDGET_KEY && isObject(value)) {
            console.error('Setting options via ui:widget object is no longer supported, use ui:options instead');
            return options;
        }
        if (key === UI_OPTIONS_KEY && isObject(value)) {
            return { ...options, ...value };
        }
        return { ...options, [key.substring(3)]: value };
    }, { ...globalOptions });
}
//# sourceMappingURL=getUiOptions.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/canExpand.js

/** Checks whether the field described by `schema`, having the `uiSchema` and `formData` supports expanding. The UI for
 * the field can expand if it has additional properties, is not forced as non-expandable by the `uiSchema` and the
 * `formData` object doesn't already have `schema.maxProperties` elements.
 *
 * @param schema - The schema for the field that is being checked
 * @param [uiSchema={}] - The uiSchema for the field
 * @param [formData] - The formData for the field
 * @returns - True if the schema element has additionalProperties, is expandable, and not at the maxProperties limit
 */
function canExpand(schema, uiSchema = {}, formData) {
    if (!schema.additionalProperties) {
        return false;
    }
    const { expandable = true } = getUiOptions(uiSchema);
    if (expandable === false) {
        return expandable;
    }
    // if ui:options.expandable was not explicitly set to false, we can add
    // another property if we have not exceeded maxProperties yet
    if (schema.maxProperties !== undefined && formData) {
        return Object.keys(formData).length < schema.maxProperties;
    }
    return true;
}
//# sourceMappingURL=canExpand.js.map
// EXTERNAL MODULE: ../node_modules/lodash/isPlainObject.js
var isPlainObject = __webpack_require__(40861);
var isPlainObject_default = /*#__PURE__*/__webpack_require__.n(isPlainObject);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/createErrorHandler.js


/** Given a `formData` object, recursively creates a `FormValidation` error handling structure around it
 *
 * @param formData - The form data around which the error handler is created
 * @returns - A `FormValidation` object based on the `formData` structure
 */
function createErrorHandler(formData) {
    const handler = {
        // We store the list of errors for this node in a property named __errors
        // to avoid name collision with a possible sub schema field named
        // 'errors' (see `utils.toErrorSchema`).
        [ERRORS_KEY]: [],
        addError(message) {
            this[ERRORS_KEY].push(message);
        },
    };
    if (Array.isArray(formData)) {
        return formData.reduce((acc, value, key) => {
            return { ...acc, [key]: createErrorHandler(value) };
        }, handler);
    }
    if (isPlainObject_default()(formData)) {
        const formObject = formData;
        return Object.keys(formObject).reduce((acc, key) => {
            return { ...acc, [key]: createErrorHandler(formObject[key]) };
        }, handler);
    }
    return handler;
}
//# sourceMappingURL=createErrorHandler.js.map
// EXTERNAL MODULE: ../node_modules/lodash/isEqualWith.js
var isEqualWith = __webpack_require__(65191);
var isEqualWith_default = /*#__PURE__*/__webpack_require__.n(isEqualWith);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/deepEquals.js

/** Implements a deep equals using the `lodash.isEqualWith` function, that provides a customized comparator that
 * assumes all functions are equivalent.
 *
 * @param a - The first element to compare
 * @param b - The second element to compare
 * @returns - True if the `a` and `b` are deeply equal, false otherwise
 */
function deepEquals(a, b) {
    return isEqualWith_default()(a, b, (obj, other) => {
        if (typeof obj === 'function' && typeof other === 'function') {
            // Assume all functions are equivalent
            // see https://github.com/rjsf-team/react-jsonschema-form/issues/255
            return true;
        }
        return undefined; // fallback to default isEquals behavior
    });
}
//# sourceMappingURL=deepEquals.js.map
// EXTERNAL MODULE: ../node_modules/lodash/get.js
var get = __webpack_require__(99729);
var get_default = /*#__PURE__*/__webpack_require__.n(get);
// EXTERNAL MODULE: ../node_modules/lodash/isEmpty.js
var isEmpty = __webpack_require__(90104);
var isEmpty_default = /*#__PURE__*/__webpack_require__.n(isEmpty);
// EXTERNAL MODULE: ../node_modules/jsonpointer/jsonpointer.js
var jsonpointer = __webpack_require__(88208);
// EXTERNAL MODULE: ../node_modules/lodash/omit.js
var omit = __webpack_require__(48159);
var omit_default = /*#__PURE__*/__webpack_require__.n(omit);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/findSchemaDefinition.js



/** Splits out the value at the `key` in `object` from the `object`, returning an array that contains in the first
 * location, the `object` minus the `key: value` and in the second location the `value`.
 *
 * @param key - The key from the object to extract
 * @param object - The object from which to extract the element
 * @returns - An array with the first value being the object minus the `key` element and the second element being the
 *      value from `object[key]`
 */
function splitKeyElementFromObject(key, object) {
    const value = object[key];
    const remaining = omit_default()(object, [key]);
    return [remaining, value];
}
/** Given the name of a `$ref` from within a schema, using the `rootSchema`, look up and return the sub-schema using the
 * path provided by that reference. If `#` is not the first character of the reference, or the path does not exist in
 * the schema, then throw an Error. Otherwise return the sub-schema. Also deals with nested `$ref`s in the sub-schema.
 *
 * @param $ref - The ref string for which the schema definition is desired
 * @param [rootSchema={}] - The root schema in which to search for the definition
 * @returns - The sub-schema within the `rootSchema` which matches the `$ref` if it exists
 * @throws - Error indicating that no schema for that reference exists
 */
function findSchemaDefinition($ref, rootSchema = {}) {
    let ref = $ref || '';
    if (ref.startsWith('#')) {
        // Decode URI fragment representation.
        ref = decodeURIComponent(ref.substring(1));
    }
    else {
        throw new Error(`Could not find a definition for ${$ref}.`);
    }
    const current = jsonpointer.get(rootSchema, ref);
    if (current === undefined) {
        throw new Error(`Could not find a definition for ${$ref}.`);
    }
    if (current[REF_KEY]) {
        const [remaining, theRef] = splitKeyElementFromObject(REF_KEY, current);
        const subSchema = findSchemaDefinition(theRef, rootSchema);
        if (Object.keys(remaining).length > 0) {
            return { ...remaining, ...subSchema };
        }
        return subSchema;
    }
    return current;
}
//# sourceMappingURL=findSchemaDefinition.js.map
// EXTERNAL MODULE: ../node_modules/lodash/has.js
var has = __webpack_require__(73915);
var has_default = /*#__PURE__*/__webpack_require__.n(has);
// EXTERNAL MODULE: ../node_modules/lodash/isNumber.js
var isNumber = __webpack_require__(78581);
var isNumber_default = /*#__PURE__*/__webpack_require__.n(isNumber);
// EXTERNAL MODULE: ../node_modules/lodash/isObject.js
var lodash_isObject = __webpack_require__(11611);
var isObject_default = /*#__PURE__*/__webpack_require__.n(lodash_isObject);
// EXTERNAL MODULE: ../node_modules/lodash/isString.js
var isString = __webpack_require__(47206);
var isString_default = /*#__PURE__*/__webpack_require__.n(isString);
// EXTERNAL MODULE: ../node_modules/lodash/reduce.js
var reduce = __webpack_require__(34172);
var reduce_default = /*#__PURE__*/__webpack_require__.n(reduce);
// EXTERNAL MODULE: ../node_modules/lodash/times.js
var times = __webpack_require__(41571);
var times_default = /*#__PURE__*/__webpack_require__.n(times);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/getOptionMatchingSimpleDiscriminator.js


/** Compares the value of `discriminatorField` within `formData` against the value of `discriminatorField` within schema for each `option`.
 * Returns index of first `option` whose discriminator matches formData. Returns `undefined` if there is no match.
 * This function does not work with discriminators of `"type": "object"` and `"type": "array"`
 *
 * @param formData - The current formData, if any, used to figure out a match
 * @param options - The list of options to find a matching options from
 * @param [discriminatorField] - The optional name of the field within the options object whose value is used to
 *          determine which option is selected
 * @returns - The index of the matched option or undefined if there is no match
 */
function getOptionMatchingSimpleDiscriminator(formData, options, discriminatorField) {
    var _a;
    if (formData && discriminatorField) {
        const value = get_default()(formData, discriminatorField);
        if (value === undefined) {
            return;
        }
        for (let i = 0; i < options.length; i++) {
            const option = options[i];
            const discriminator = get_default()(option, [PROPERTIES_KEY, discriminatorField], {});
            if (discriminator.type === 'object' || discriminator.type === 'array') {
                continue;
            }
            if (discriminator.const === value) {
                return i;
            }
            if ((_a = discriminator.enum) === null || _a === void 0 ? void 0 : _a.includes(value)) {
                return i;
            }
        }
    }
    return;
}
//# sourceMappingURL=getOptionMatchingSimpleDiscriminator.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/getMatchingOption.js





/** Given the `formData` and list of `options`, attempts to find the index of the option that best matches the data.
 * Deprecated, use `getFirstMatchingOption()` instead.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param formData - The current formData, if any, used to figure out a match
 * @param options - The list of options to find a matching options from
 * @param rootSchema - The root schema, used to primarily to look up `$ref`s
 * @param [discriminatorField] - The optional name of the field within the options object whose value is used to
 *          determine which option is selected
 * @returns - The index of the matched option or 0 if none is available
 * @deprecated
 */
function getMatchingOption(validator, formData, options, rootSchema, discriminatorField) {
    // For performance, skip validating subschemas if formData is undefined. We just
    // want to get the first option in that case.
    if (formData === undefined) {
        return 0;
    }
    const simpleDiscriminatorMatch = getOptionMatchingSimpleDiscriminator(formData, options, discriminatorField);
    if (isNumber_default()(simpleDiscriminatorMatch)) {
        return simpleDiscriminatorMatch;
    }
    for (let i = 0; i < options.length; i++) {
        const option = options[i];
        // If we have a discriminator field, then we will use this to make the determination
        if (discriminatorField && has_default()(option, [PROPERTIES_KEY, discriminatorField])) {
            const value = get_default()(formData, discriminatorField);
            const discriminator = get_default()(option, [PROPERTIES_KEY, discriminatorField], {});
            if (validator.isValid(discriminator, value, rootSchema)) {
                return i;
            }
        }
        else if (option[PROPERTIES_KEY]) {
            // If the schema describes an object then we need to add slightly more
            // strict matching to the schema, because unless the schema uses the
            // "requires" keyword, an object will match the schema as long as it
            // doesn't have matching keys with a conflicting type. To do this we use an
            // "anyOf" with an array of requires. This augmentation expresses that the
            // schema should match if any of the keys in the schema are present on the
            // object and pass validation.
            //
            // Create an "anyOf" schema that requires at least one of the keys in the
            // "properties" object
            const requiresAnyOf = {
                anyOf: Object.keys(option[PROPERTIES_KEY]).map((key) => ({
                    required: [key],
                })),
            };
            let augmentedSchema;
            // If the "anyOf" keyword already exists, wrap the augmentation in an "allOf"
            if (option.anyOf) {
                // Create a shallow clone of the option
                const { ...shallowClone } = option;
                if (!shallowClone.allOf) {
                    shallowClone.allOf = [];
                }
                else {
                    // If "allOf" already exists, shallow clone the array
                    shallowClone.allOf = shallowClone.allOf.slice();
                }
                shallowClone.allOf.push(requiresAnyOf);
                augmentedSchema = shallowClone;
            }
            else {
                augmentedSchema = Object.assign({}, option, requiresAnyOf);
            }
            // Remove the "required" field as it's likely that not all fields have
            // been filled in yet, which will mean that the schema is not valid
            delete augmentedSchema.required;
            if (validator.isValid(augmentedSchema, formData, rootSchema)) {
                return i;
            }
        }
        else if (validator.isValid(option, formData, rootSchema)) {
            return i;
        }
    }
    return 0;
}
//# sourceMappingURL=getMatchingOption.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/getFirstMatchingOption.js

/** Given the `formData` and list of `options`, attempts to find the index of the first option that matches the data.
 * Always returns the first option if there is nothing that matches.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param formData - The current formData, if any, used to figure out a match
 * @param options - The list of options to find a matching options from
 * @param rootSchema - The root schema, used to primarily to look up `$ref`s
 * @param [discriminatorField] - The optional name of the field within the options object whose value is used to
 *          determine which option is selected
 * @returns - The index of the first matched option or 0 if none is available
 */
function getFirstMatchingOption(validator, formData, options, rootSchema, discriminatorField) {
    return getMatchingOption(validator, formData, options, rootSchema, discriminatorField);
}
//# sourceMappingURL=getFirstMatchingOption.js.map
// EXTERNAL MODULE: ../node_modules/lodash/isEqual.js
var isEqual = __webpack_require__(56141);
var isEqual_default = /*#__PURE__*/__webpack_require__.n(isEqual);
// EXTERNAL MODULE: ../node_modules/lodash/set.js
var set = __webpack_require__(47215);
var set_default = /*#__PURE__*/__webpack_require__.n(set);
// EXTERNAL MODULE: ../node_modules/lodash/transform.js
var transform = __webpack_require__(42673);
var transform_default = /*#__PURE__*/__webpack_require__.n(transform);
// EXTERNAL MODULE: ../node_modules/lodash/merge.js
var merge = __webpack_require__(15409);
var merge_default = /*#__PURE__*/__webpack_require__.n(merge);
// EXTERNAL MODULE: ../node_modules/lodash/flattenDeep.js
var flattenDeep = __webpack_require__(84812);
var flattenDeep_default = /*#__PURE__*/__webpack_require__.n(flattenDeep);
// EXTERNAL MODULE: ../node_modules/lodash/uniq.js
var uniq = __webpack_require__(83766);
var uniq_default = /*#__PURE__*/__webpack_require__.n(uniq);
// EXTERNAL MODULE: ../node_modules/json-schema-merge-allof/src/index.js
var src = __webpack_require__(64717);
var src_default = /*#__PURE__*/__webpack_require__.n(src);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/getDiscriminatorFieldFromSchema.js


/** Returns the `discriminator.propertyName` when defined in the `schema` if it is a string. A warning is generated when
 * it is not a string. Returns `undefined` when a valid discriminator is not present.
 *
 * @param schema - The schema from which the discriminator is potentially obtained
 * @returns - The `discriminator.propertyName` if it exists in the schema, otherwise `undefined`
 */
function getDiscriminatorFieldFromSchema(schema) {
    let discriminator;
    const maybeString = get_default()(schema, 'discriminator.propertyName', undefined);
    if (isString_default()(maybeString)) {
        discriminator = maybeString;
    }
    else if (maybeString !== undefined) {
        console.warn(`Expecting discriminator to be a string, got "${typeof maybeString}" instead`);
    }
    return discriminator;
}
//# sourceMappingURL=getDiscriminatorFieldFromSchema.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/guessType.js
/** Given a specific `value` attempts to guess the type of a schema element. In the case where we have to implicitly
 *  create a schema, it is useful to know what type to use based on the data we are defining.
 *
 * @param value - The value from which to guess the type
 * @returns - The best guess for the object type
 */
function guessType(value) {
    if (Array.isArray(value)) {
        return 'array';
    }
    if (typeof value === 'string') {
        return 'string';
    }
    if (value == null) {
        return 'null';
    }
    if (typeof value === 'boolean') {
        return 'boolean';
    }
    if (!isNaN(value)) {
        return 'number';
    }
    if (typeof value === 'object') {
        return 'object';
    }
    // Default to string if we can't figure it out
    return 'string';
}
//# sourceMappingURL=guessType.js.map
// EXTERNAL MODULE: ../node_modules/lodash/union.js
var union = __webpack_require__(68946);
var union_default = /*#__PURE__*/__webpack_require__.n(union);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/getSchemaType.js

/** Gets the type of a given `schema`. If the type is not explicitly defined, then an attempt is made to infer it from
 * other elements of the schema as follows:
 * - schema.const: Returns the `guessType()` of that value
 * - schema.enum: Returns `string`
 * - schema.properties: Returns `object`
 * - schema.additionalProperties: Returns `object`
 * - type is an array with a length of 2 and one type is 'null': Returns the other type
 *
 * @param schema - The schema for which to get the type
 * @returns - The type of the schema
 */
function getSchemaType(schema) {
    let { type } = schema;
    if (!type && schema.const) {
        return guessType(schema.const);
    }
    if (!type && schema.enum) {
        return 'string';
    }
    if (!type && (schema.properties || schema.additionalProperties)) {
        return 'object';
    }
    if (Array.isArray(type)) {
        if (type.length === 2 && type.includes('null')) {
            type = type.find((type) => type !== 'null');
        }
        else {
            type = type[0];
        }
    }
    return type;
}
//# sourceMappingURL=getSchemaType.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/mergeSchemas.js




/** Recursively merge deeply nested schemas. The difference between `mergeSchemas` and `mergeObjects` is that
 * `mergeSchemas` only concats arrays for values under the 'required' keyword, and when it does, it doesn't include
 * duplicate values.
 *
 * @param obj1 - The first schema object to merge
 * @param obj2 - The second schema object to merge
 * @returns - The merged schema object
 */
function mergeSchemas(obj1, obj2) {
    const acc = Object.assign({}, obj1); // Prevent mutation of source object.
    return Object.keys(obj2).reduce((acc, key) => {
        const left = obj1 ? obj1[key] : {}, right = obj2[key];
        if (obj1 && key in obj1 && isObject(right)) {
            acc[key] = mergeSchemas(left, right);
        }
        else if (obj1 &&
            obj2 &&
            (getSchemaType(obj1) === 'object' || getSchemaType(obj2) === 'object') &&
            key === REQUIRED_KEY &&
            Array.isArray(left) &&
            Array.isArray(right)) {
            // Don't include duplicate values when merging 'required' fields.
            acc[key] = union_default()(left, right);
        }
        else {
            acc[key] = right;
        }
        return acc;
    }, acc);
}
//# sourceMappingURL=mergeSchemas.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/retrieveSchema.js
















/** Retrieves an expanded schema that has had all of its conditions, additional properties, references and dependencies
 * resolved and merged into the `schema` given a `validator`, `rootSchema` and `rawFormData` that is used to do the
 * potentially recursive resolution.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be forwarded to all the APIs
 * @param schema - The schema for which retrieving a schema is desired
 * @param [rootSchema={}] - The root schema that will be forwarded to all the APIs
 * @param [rawFormData] - The current formData, if any, to assist retrieving a schema
 * @returns - The schema having its conditions, additional properties, references and dependencies resolved
 */
function retrieveSchema(validator, schema, rootSchema = {}, rawFormData) {
    return retrieveSchemaInternal(validator, schema, rootSchema, rawFormData)[0];
}
/** Resolves a conditional block (if/else/then) by removing the condition and merging the appropriate conditional branch
 * with the rest of the schema. If `expandAllBranches` is true, then the `retrieveSchemaInteral()` results for both
 * conditions will be returned.
 *
 * @param validator - An implementation of the `ValidatorType` interface that is used to detect valid schema conditions
 * @param schema - The schema for which resolving a condition is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param expandAllBranches - Flag, if true, will return all possible branches of conditions, any/oneOf and
 *          dependencies as a list of schemas
 * @param recurseList - The list of recursive references already processed
 * @param [formData] - The current formData to assist retrieving a schema
 * @returns - A list of schemas with the appropriate conditions resolved, possibly with all branches expanded
 */
function resolveCondition(validator, schema, rootSchema, expandAllBranches, recurseList, formData) {
    const { if: expression, then, else: otherwise, ...resolvedSchemaLessConditional } = schema;
    const conditionValue = validator.isValid(expression, formData || {}, rootSchema);
    let resolvedSchemas = [resolvedSchemaLessConditional];
    let schemas = [];
    if (expandAllBranches) {
        if (then && typeof then !== 'boolean') {
            schemas = schemas.concat(retrieveSchemaInternal(validator, then, rootSchema, formData, expandAllBranches, recurseList));
        }
        if (otherwise && typeof otherwise !== 'boolean') {
            schemas = schemas.concat(retrieveSchemaInternal(validator, otherwise, rootSchema, formData, expandAllBranches, recurseList));
        }
    }
    else {
        const conditionalSchema = conditionValue ? then : otherwise;
        if (conditionalSchema && typeof conditionalSchema !== 'boolean') {
            schemas = schemas.concat(retrieveSchemaInternal(validator, conditionalSchema, rootSchema, formData, expandAllBranches, recurseList));
        }
    }
    if (schemas.length) {
        resolvedSchemas = schemas.map((s) => mergeSchemas(resolvedSchemaLessConditional, s));
    }
    return resolvedSchemas.flatMap((s) => retrieveSchemaInternal(validator, s, rootSchema, formData, expandAllBranches, recurseList));
}
/** Given a list of lists of allOf, anyOf or oneOf values, create a list of lists of all permutations of the values. The
 * `listOfLists` is expected to be all resolved values of the 1st...nth schemas within an `allOf`, `anyOf` or `oneOf`.
 * From those lists, build a matrix for each `xxxOf` where there is more than one schema for a row in the list of lists.
 *
 * For example:
 * - If there are three xxxOf rows (A, B, C) and they have been resolved such that there is only one A, two B and three
 *   C schemas then:
 *   - The permutation for the first row is `[[A]]`
 *   - The permutations for the second row are `[[A,B1], [A,B2]]`
 *   - The permutations for the third row are `[[A,B1,C1], [A,B1,C2], [A,B1,C3], [A,B2,C1], [A,B2,C2], [A,B2,C3]]`
 *
 * @param listOfLists - The list of lists of elements that represent the allOf, anyOf or oneOf resolved values in order
 * @returns - The list of all permutations of schemas for a set of `xxxOf`s
 */
function getAllPermutationsOfXxxOf(listOfLists) {
    const allPermutations = listOfLists.reduce((permutations, list) => {
        // When there are more than one set of schemas for a row, duplicate the set of permutations and add in the values
        if (list.length > 1) {
            return list.flatMap((element) => times_default()(permutations.length, (i) => [...permutations[i]].concat(element)));
        }
        // Otherwise just push in the single value into the current set of permutations
        permutations.forEach((permutation) => permutation.push(list[0]));
        return permutations;
    }, [[]] // Start with an empty list
    );
    return allPermutations;
}
/** Resolves references and dependencies within a schema and its 'allOf' children. Passes the `expandAllBranches` flag
 * down to the `retrieveSchemaInternal()`, `resolveReference()` and `resolveDependencies()` helper calls. If
 * `expandAllBranches` is true, then all possible dependencies and/or allOf branches are returned.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be forwarded to all the APIs
 * @param schema - The schema for which resolving a schema is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param expandAllBranches - Flag, if true, will return all possible branches of conditions, any/oneOf and dependencies
 *          as a list of schemas
 * @param recurseList - The list of recursive references already processed
 * @param [formData] - The current formData, if any, to assist retrieving a schema
 * @returns - The list of schemas having its references, dependencies and allOf schemas resolved
 */
function resolveSchema(validator, schema, rootSchema, expandAllBranches, recurseList, formData) {
    const updatedSchemas = resolveReference(validator, schema, rootSchema, expandAllBranches, recurseList, formData);
    if (updatedSchemas.length > 1 || updatedSchemas[0] !== schema) {
        // return the updatedSchemas array if it has either multiple schemas within it
        // OR the first schema is not the same as the original schema
        return updatedSchemas;
    }
    if (DEPENDENCIES_KEY in schema) {
        const resolvedSchemas = resolveDependencies(validator, schema, rootSchema, expandAllBranches, recurseList, formData);
        return resolvedSchemas.flatMap((s) => {
            return retrieveSchemaInternal(validator, s, rootSchema, formData, expandAllBranches, recurseList);
        });
    }
    if (ALL_OF_KEY in schema && Array.isArray(schema.allOf)) {
        const allOfSchemaElements = schema.allOf.map((allOfSubschema) => retrieveSchemaInternal(validator, allOfSubschema, rootSchema, formData, expandAllBranches, recurseList));
        const allPermutations = getAllPermutationsOfXxxOf(allOfSchemaElements);
        return allPermutations.map((permutation) => ({ ...schema, allOf: permutation }));
    }
    // No $ref or dependencies or allOf attribute was found, returning the original schema.
    return [schema];
}
/** Resolves all references within a schema and then returns the `retrieveSchemaInternal()` if the resolved schema is
 * actually different than the original. Passes the `expandAllBranches` flag down to the `retrieveSchemaInternal()`
 * helper call.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be forwarded to all the APIs
 * @param schema - The schema for which resolving a reference is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param expandAllBranches - Flag, if true, will return all possible branches of conditions, any/oneOf and dependencies
 *          as a list of schemas
 * @param recurseList - The list of recursive references already processed
 * @param [formData] - The current formData, if any, to assist retrieving a schema
 * @returns - The list schemas retrieved after having all references resolved
 */
function resolveReference(validator, schema, rootSchema, expandAllBranches, recurseList, formData) {
    const updatedSchema = resolveAllReferences(schema, rootSchema, recurseList);
    if (updatedSchema !== schema) {
        // Only call this if the schema was actually changed by the `resolveAllReferences()` function
        return retrieveSchemaInternal(validator, updatedSchema, rootSchema, formData, expandAllBranches, recurseList);
    }
    return [schema];
}
/** Resolves all references within the schema itself as well as any of its properties and array items.
 *
 * @param schema - The schema for which resolving all references is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param recurseList - List of $refs already resolved to prevent recursion
 * @returns - given schema will all references resolved or the original schema if no internal `$refs` were resolved
 */
function resolveAllReferences(schema, rootSchema, recurseList) {
    if (!isObject(schema)) {
        return schema;
    }
    let resolvedSchema = schema;
    // resolve top level ref
    if (REF_KEY in resolvedSchema) {
        const { $ref, ...localSchema } = resolvedSchema;
        // Check for a recursive reference and stop the loop
        if (recurseList.includes($ref)) {
            return resolvedSchema;
        }
        recurseList.push($ref);
        // Retrieve the referenced schema definition.
        const refSchema = findSchemaDefinition($ref, rootSchema);
        resolvedSchema = { ...refSchema, ...localSchema };
    }
    if (PROPERTIES_KEY in resolvedSchema) {
        const childrenLists = [];
        const updatedProps = transform_default()(resolvedSchema[PROPERTIES_KEY], (result, value, key) => {
            const childList = [...recurseList];
            result[key] = resolveAllReferences(value, rootSchema, childList);
            childrenLists.push(childList);
        }, {});
        merge_default()(recurseList, uniq_default()(flattenDeep_default()(childrenLists)));
        resolvedSchema = { ...resolvedSchema, [PROPERTIES_KEY]: updatedProps };
    }
    if (ITEMS_KEY in resolvedSchema &&
        !Array.isArray(resolvedSchema.items) &&
        typeof resolvedSchema.items !== 'boolean') {
        resolvedSchema = {
            ...resolvedSchema,
            items: resolveAllReferences(resolvedSchema.items, rootSchema, recurseList),
        };
    }
    return isEqual_default()(schema, resolvedSchema) ? schema : resolvedSchema;
}
/** Creates new 'properties' items for each key in the `formData`
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param theSchema - The schema for which the existing additional properties is desired
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s * @param validator
 * @param [aFormData] - The current formData, if any, to assist retrieving a schema
 * @returns - The updated schema with additional properties stubbed
 */
function stubExistingAdditionalProperties(validator, theSchema, rootSchema, aFormData) {
    // Clone the schema so that we don't ruin the consumer's original
    const schema = {
        ...theSchema,
        properties: { ...theSchema.properties },
    };
    // make sure formData is an object
    const formData = aFormData && isObject(aFormData) ? aFormData : {};
    Object.keys(formData).forEach((key) => {
        if (key in schema.properties) {
            // No need to stub, our schema already has the property
            return;
        }
        let additionalProperties = {};
        if (typeof schema.additionalProperties !== 'boolean') {
            if (REF_KEY in schema.additionalProperties) {
                additionalProperties = retrieveSchema(validator, { $ref: get_default()(schema.additionalProperties, [REF_KEY]) }, rootSchema, formData);
            }
            else if ('type' in schema.additionalProperties) {
                additionalProperties = { ...schema.additionalProperties };
            }
            else if (ANY_OF_KEY in schema.additionalProperties || ONE_OF_KEY in schema.additionalProperties) {
                additionalProperties = {
                    type: 'object',
                    ...schema.additionalProperties,
                };
            }
            else {
                additionalProperties = { type: guessType(get_default()(formData, [key])) };
            }
        }
        else {
            additionalProperties = { type: guessType(get_default()(formData, [key])) };
        }
        // The type of our new key should match the additionalProperties value;
        schema.properties[key] = additionalProperties;
        // Set our additional property flag so we know it was dynamically added
        set_default()(schema.properties, [key, ADDITIONAL_PROPERTY_FLAG], true);
    });
    return schema;
}
/** Internal handler that retrieves an expanded schema that has had all of its conditions, additional properties,
 * references and dependencies resolved and merged into the `schema` given a `validator`, `rootSchema` and `rawFormData`
 * that is used to do the potentially recursive resolution. If `expandAllBranches` is true, then all possible branches
 * of the schema and its references, conditions and dependencies are returned.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be forwarded to all the APIs
 * @param schema - The schema for which retrieving a schema is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param [rawFormData] - The current formData, if any, to assist retrieving a schema
 * @param [expandAllBranches=false] - Flag, if true, will return all possible branches of conditions, any/oneOf and
 *          dependencies as a list of schemas
 * @param [recurseList=[]] - The optional, list of recursive references already processed
 * @returns - The schema(s) resulting from having its conditions, additional properties, references and dependencies
 *          resolved. Multiple schemas may be returned if `expandAllBranches` is true.
 */
function retrieveSchemaInternal(validator, schema, rootSchema, rawFormData, expandAllBranches = false, recurseList = []) {
    if (!isObject(schema)) {
        return [{}];
    }
    const resolvedSchemas = resolveSchema(validator, schema, rootSchema, expandAllBranches, recurseList, rawFormData);
    return resolvedSchemas.flatMap((s) => {
        let resolvedSchema = s;
        if (IF_KEY in resolvedSchema) {
            return resolveCondition(validator, resolvedSchema, rootSchema, expandAllBranches, recurseList, rawFormData);
        }
        if (ALL_OF_KEY in resolvedSchema) {
            // resolve allOf schemas
            if (expandAllBranches) {
                const { allOf, ...restOfSchema } = resolvedSchema;
                return [...allOf, restOfSchema];
            }
            try {
                resolvedSchema = src_default()(resolvedSchema, {
                    deep: false,
                });
            }
            catch (e) {
                console.warn('could not merge subschemas in allOf:\n', e);
                const { allOf, ...resolvedSchemaWithoutAllOf } = resolvedSchema;
                return resolvedSchemaWithoutAllOf;
            }
        }
        const hasAdditionalProperties = ADDITIONAL_PROPERTIES_KEY in resolvedSchema && resolvedSchema.additionalProperties !== false;
        if (hasAdditionalProperties) {
            return stubExistingAdditionalProperties(validator, resolvedSchema, rootSchema, rawFormData);
        }
        return resolvedSchema;
    });
}
/** Resolves an `anyOf` or `oneOf` within a schema (if present) to the list of schemas returned from
 * `retrieveSchemaInternal()` for the best matching option. If `expandAllBranches` is true, then a list of schemas for ALL
 * options are retrieved and returned.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be forwarded to all the APIs
 * @param schema - The schema for which retrieving a schema is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param expandAllBranches - Flag, if true, will return all possible branches of conditions, any/oneOf and dependencies
 *          as a list of schemas
 * @param [rawFormData] - The current formData, if any, to assist retrieving a schema, defaults to an empty object
 * @returns - Either an array containing the best matching option or all options if `expandAllBranches` is true
 */
function resolveAnyOrOneOfSchemas(validator, schema, rootSchema, expandAllBranches, rawFormData) {
    let anyOrOneOf;
    const { oneOf, anyOf, ...remaining } = schema;
    if (Array.isArray(oneOf)) {
        anyOrOneOf = oneOf;
    }
    else if (Array.isArray(anyOf)) {
        anyOrOneOf = anyOf;
    }
    if (anyOrOneOf) {
        // Ensure that during expand all branches we pass an object rather than undefined so that all options are interrogated
        const formData = rawFormData === undefined && expandAllBranches ? {} : rawFormData;
        const discriminator = getDiscriminatorFieldFromSchema(schema);
        anyOrOneOf = anyOrOneOf.map((s) => {
            // Due to anyOf/oneOf possibly using the same $ref we always pass a fresh recurse list array so that each option
            // can resolve recursive references independently
            return resolveAllReferences(s, rootSchema, []);
        });
        // Call this to trigger the set of isValid() calls that the schema parser will need
        const option = getFirstMatchingOption(validator, formData, anyOrOneOf, rootSchema, discriminator);
        if (expandAllBranches) {
            return anyOrOneOf.map((item) => mergeSchemas(remaining, item));
        }
        schema = mergeSchemas(remaining, anyOrOneOf[option]);
    }
    return [schema];
}
/** Resolves dependencies within a schema and its 'anyOf/oneOf' children. Passes the `expandAllBranches` flag down to
 * the `resolveAnyOrOneOfSchema()` and `processDependencies()` helper calls.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be forwarded to all the APIs
 * @param schema - The schema for which resolving a dependency is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param expandAllBranches - Flag, if true, will return all possible branches of conditions, any/oneOf and dependencies
 *          as a list of schemas
 * @param recurseList - The list of recursive references already processed
 * @param [formData] - The current formData, if any, to assist retrieving a schema
 * @returns - The list of schemas with their dependencies resolved
 */
function resolveDependencies(validator, schema, rootSchema, expandAllBranches, recurseList, formData) {
    // Drop the dependencies from the source schema.
    const { dependencies, ...remainingSchema } = schema;
    const resolvedSchemas = resolveAnyOrOneOfSchemas(validator, remainingSchema, rootSchema, expandAllBranches, formData);
    return resolvedSchemas.flatMap((resolvedSchema) => processDependencies(validator, dependencies, resolvedSchema, rootSchema, expandAllBranches, recurseList, formData));
}
/** Processes all the `dependencies` recursively into the list of `resolvedSchema`s as needed. Passes the
 * `expandAllBranches` flag down to the `withDependentSchema()` and the recursive `processDependencies()` helper calls.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be forwarded to all the APIs
 * @param dependencies - The set of dependencies that needs to be processed
 * @param resolvedSchema - The schema for which processing dependencies is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param expandAllBranches - Flag, if true, will return all possible branches of conditions, any/oneOf and dependencies
 *          as a list of schemas
 * @param recurseList - The list of recursive references already processed
 * @param [formData] - The current formData, if any, to assist retrieving a schema
 * @returns - The schema with the `dependencies` resolved into it
 */
function processDependencies(validator, dependencies, resolvedSchema, rootSchema, expandAllBranches, recurseList, formData) {
    let schemas = [resolvedSchema];
    // Process dependencies updating the local schema properties as appropriate.
    for (const dependencyKey in dependencies) {
        // Skip this dependency if its trigger property is not present.
        if (!expandAllBranches && get_default()(formData, [dependencyKey]) === undefined) {
            continue;
        }
        // Skip this dependency if it is not included in the schema (such as when dependencyKey is itself a hidden dependency.)
        if (resolvedSchema.properties && !(dependencyKey in resolvedSchema.properties)) {
            continue;
        }
        const [remainingDependencies, dependencyValue] = splitKeyElementFromObject(dependencyKey, dependencies);
        if (Array.isArray(dependencyValue)) {
            schemas[0] = withDependentProperties(resolvedSchema, dependencyValue);
        }
        else if (isObject(dependencyValue)) {
            schemas = withDependentSchema(validator, resolvedSchema, rootSchema, dependencyKey, dependencyValue, expandAllBranches, recurseList, formData);
        }
        return schemas.flatMap((schema) => processDependencies(validator, remainingDependencies, schema, rootSchema, expandAllBranches, recurseList, formData));
    }
    return schemas;
}
/** Updates a schema with additionally required properties added
 *
 * @param schema - The schema for which resolving a dependent properties is desired
 * @param [additionallyRequired] - An optional array of additionally required names
 * @returns - The schema with the additional required values merged in
 */
function withDependentProperties(schema, additionallyRequired) {
    if (!additionallyRequired) {
        return schema;
    }
    const required = Array.isArray(schema.required)
        ? Array.from(new Set([...schema.required, ...additionallyRequired]))
        : additionallyRequired;
    return { ...schema, required: required };
}
/** Merges a dependent schema into the `schema` dealing with oneOfs and references. Passes the `expandAllBranches` flag
 * down to the `retrieveSchemaInternal()`, `resolveReference()` and `withExactlyOneSubschema()` helper calls.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be forwarded to all the APIs
 * @param schema - The schema for which resolving a dependent schema is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param dependencyKey - The key name of the dependency
 * @param dependencyValue - The potentially dependent schema
 * @param expandAllBranches - Flag, if true, will return all possible branches of conditions, any/oneOf and dependencies
 *          as a list of schemas
 * @param recurseList - The list of recursive references already processed
 * @param [formData]- The current formData to assist retrieving a schema
 * @returns - The list of schemas with the dependent schema resolved into them
 */
function withDependentSchema(validator, schema, rootSchema, dependencyKey, dependencyValue, expandAllBranches, recurseList, formData) {
    const dependentSchemas = retrieveSchemaInternal(validator, dependencyValue, rootSchema, formData, expandAllBranches, recurseList);
    return dependentSchemas.flatMap((dependent) => {
        const { oneOf, ...dependentSchema } = dependent;
        schema = mergeSchemas(schema, dependentSchema);
        // Since it does not contain oneOf, we return the original schema.
        if (oneOf === undefined) {
            return schema;
        }
        // Resolve $refs inside oneOf.
        const resolvedOneOfs = oneOf.map((subschema) => {
            if (typeof subschema === 'boolean' || !(REF_KEY in subschema)) {
                return [subschema];
            }
            return resolveReference(validator, subschema, rootSchema, expandAllBranches, recurseList, formData);
        });
        const allPermutations = getAllPermutationsOfXxxOf(resolvedOneOfs);
        return allPermutations.flatMap((resolvedOneOf) => withExactlyOneSubschema(validator, schema, rootSchema, dependencyKey, resolvedOneOf, expandAllBranches, recurseList, formData));
    });
}
/** Returns a list of `schema`s with the best choice from the `oneOf` options merged into it. If `expandAllBranches` is
 * true, then a list of schemas for ALL options are retrieved and returned. Passes the `expandAllBranches` flag down to
 * the `retrieveSchemaInternal()` helper call.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used to validate oneOf options
 * @param schema - The schema for which resolving a oneOf subschema is desired
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param dependencyKey - The key name of the oneOf dependency
 * @param oneOf - The list of schemas representing the oneOf options
 * @param expandAllBranches - Flag, if true, will return all possible branches of conditions, any/oneOf and dependencies
 *          as a list of schemas
 * @param recurseList - The list of recursive references already processed
 * @param [formData] - The current formData to assist retrieving a schema
 * @returns - Either an array containing the best matching option or all options if `expandAllBranches` is true
 */
function withExactlyOneSubschema(validator, schema, rootSchema, dependencyKey, oneOf, expandAllBranches, recurseList, formData) {
    const validSubschemas = oneOf.filter((subschema) => {
        if (typeof subschema === 'boolean' || !subschema || !subschema.properties) {
            return false;
        }
        const { [dependencyKey]: conditionPropertySchema } = subschema.properties;
        if (conditionPropertySchema) {
            const conditionSchema = {
                type: 'object',
                properties: {
                    [dependencyKey]: conditionPropertySchema,
                },
            };
            return validator.isValid(conditionSchema, formData, rootSchema) || expandAllBranches;
        }
        return false;
    });
    if (!expandAllBranches && validSubschemas.length !== 1) {
        console.warn("ignoring oneOf in dependencies because there isn't exactly one subschema that is valid");
        return [schema];
    }
    return validSubschemas.flatMap((s) => {
        const subschema = s;
        const [dependentSubschema] = splitKeyElementFromObject(dependencyKey, subschema.properties);
        const dependentSchema = { ...subschema, properties: dependentSubschema };
        const schemas = retrieveSchemaInternal(validator, dependentSchema, rootSchema, formData, expandAllBranches, recurseList);
        return schemas.map((s) => mergeSchemas(schema, s));
    });
}
//# sourceMappingURL=retrieveSchema.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/getClosestMatchingOption.js













/** A junk option used to determine when the getFirstMatchingOption call really matches an option rather than returning
 * the first item
 */
const JUNK_OPTION = {
    type: 'object',
    $id: JUNK_OPTION_ID,
    properties: {
        __not_really_there__: {
            type: 'number',
        },
    },
};
/** Recursive function that calculates the score of a `formData` against the given `schema`. The computation is fairly
 * simple. Initially the total score is 0. When `schema.properties` object exists, then all the `key/value` pairs within
 * the object are processed as follows after obtaining the formValue from `formData` using the `key`:
 * - If the `value` contains a `$ref`, `calculateIndexScore()` is called recursively with the formValue and the new
 *   schema that is the result of the ref in the schema being resolved and that sub-schema's resulting score is added to
 *   the total.
 * - If the `value` contains a `oneOf` and there is a formValue, then score based on the index returned from calling
 *   `getClosestMatchingOption()` of that oneOf.
 * - If the type of the `value` is 'object', `calculateIndexScore()` is called recursively with the formValue and the
 *   `value` itself as the sub-schema, and the score is added to the total.
 * - If the type of the `value` matches the guessed-type of the `formValue`, the score is incremented by 1, UNLESS the
 *   value has a `default` or `const`. In those case, if the `default` or `const` and the `formValue` match, the score
 *   is incremented by another 1 otherwise it is decremented by 1.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param rootSchema - The root JSON schema of the entire form
 * @param schema - The schema for which the score is being calculated
 * @param formData - The form data associated with the schema, used to calculate the score
 * @returns - The score a schema against the formData
 */
function calculateIndexScore(validator, rootSchema, schema, formData = {}) {
    let totalScore = 0;
    if (schema) {
        if (isObject_default()(schema.properties)) {
            totalScore += reduce_default()(schema.properties, (score, value, key) => {
                const formValue = get_default()(formData, key);
                if (typeof value === 'boolean') {
                    return score;
                }
                if (has_default()(value, REF_KEY)) {
                    const newSchema = retrieveSchema(validator, value, rootSchema, formValue);
                    return score + calculateIndexScore(validator, rootSchema, newSchema, formValue || {});
                }
                if ((has_default()(value, ONE_OF_KEY) || has_default()(value, ANY_OF_KEY)) && formValue) {
                    const key = has_default()(value, ONE_OF_KEY) ? ONE_OF_KEY : ANY_OF_KEY;
                    const discriminator = getDiscriminatorFieldFromSchema(value);
                    return (score +
                        getClosestMatchingOption(validator, rootSchema, formValue, get_default()(value, key), -1, discriminator));
                }
                if (value.type === 'object') {
                    return score + calculateIndexScore(validator, rootSchema, value, formValue || {});
                }
                if (value.type === guessType(formValue)) {
                    // If the types match, then we bump the score by one
                    let newScore = score + 1;
                    if (value.default) {
                        // If the schema contains a readonly default value score the value that matches the default higher and
                        // any non-matching value lower
                        newScore += formValue === value.default ? 1 : -1;
                    }
                    else if (value.const) {
                        // If the schema contains a const value score the value that matches the default higher and
                        // any non-matching value lower
                        newScore += formValue === value.const ? 1 : -1;
                    }
                    // TODO eventually, deal with enums/arrays
                    return newScore;
                }
                return score;
            }, 0);
        }
        else if (isString_default()(schema.type) && schema.type === guessType(formData)) {
            totalScore += 1;
        }
    }
    return totalScore;
}
/** Determines which of the given `options` provided most closely matches the `formData`. Using
 * `getFirstMatchingOption()` to match two schemas that differ only by the readOnly, default or const value of a field
 * based on the `formData` and returns 0 when there is no match. Rather than passing in all the `options` at once to
 * this utility, instead an array of valid option indexes is created by iterating over the list of options, call
 * `getFirstMatchingOptions` with a list of one junk option and one good option, seeing if the good option is considered
 * matched.
 *
 * Once the list of valid indexes is created, if there is only one valid index, just return it. Otherwise, if there are
 * no valid indexes, then fill the valid indexes array with the indexes of all the options. Next, the index of the
 * option with the highest score is determined by iterating over the list of valid options, calling
 * `calculateIndexScore()` on each, comparing it against the current best score, and returning the index of the one that
 * eventually has the best score.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param rootSchema - The root JSON schema of the entire form
 * @param formData - The form data associated with the schema
 * @param options - The list of options that can be selected from
 * @param [selectedOption=-1] - The index of the currently selected option, defaulted to -1 if not specified
 * @param [discriminatorField] - The optional name of the field within the options object whose value is used to
 *          determine which option is selected
 * @returns - The index of the option that is the closest match to the `formData` or the `selectedOption` if no match
 */
function getClosestMatchingOption(validator, rootSchema, formData, options, selectedOption = -1, discriminatorField) {
    // First resolve any refs in the options
    const resolvedOptions = options.map((option) => {
        return resolveAllReferences(option, rootSchema, []);
    });
    const simpleDiscriminatorMatch = getOptionMatchingSimpleDiscriminator(formData, options, discriminatorField);
    if (isNumber_default()(simpleDiscriminatorMatch)) {
        return simpleDiscriminatorMatch;
    }
    // Reduce the array of options down to a list of the indexes that are considered matching options
    const allValidIndexes = resolvedOptions.reduce((validList, option, index) => {
        const testOptions = [JUNK_OPTION, option];
        const match = getFirstMatchingOption(validator, formData, testOptions, rootSchema, discriminatorField);
        // The match is the real option, so add its index to list of valid indexes
        if (match === 1) {
            validList.push(index);
        }
        return validList;
    }, []);
    // There is only one valid index, so return it!
    if (allValidIndexes.length === 1) {
        return allValidIndexes[0];
    }
    if (!allValidIndexes.length) {
        // No indexes were valid, so we'll score all the options, add all the indexes
        times_default()(resolvedOptions.length, (i) => allValidIndexes.push(i));
    }
    const scoreCount = new Set();
    // Score all the options in the list of valid indexes and return the index with the best score
    const { bestIndex } = allValidIndexes.reduce((scoreData, index) => {
        const { bestScore } = scoreData;
        const option = resolvedOptions[index];
        const score = calculateIndexScore(validator, rootSchema, option, formData);
        scoreCount.add(score);
        if (score > bestScore) {
            return { bestIndex: index, bestScore: score };
        }
        return scoreData;
    }, { bestIndex: selectedOption, bestScore: 0 });
    // if all scores are the same go with selectedOption
    if (scoreCount.size === 1 && selectedOption >= 0) {
        return selectedOption;
    }
    return bestIndex;
}
//# sourceMappingURL=getClosestMatchingOption.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/isFixedItems.js

/** Detects whether the given `schema` contains fixed items. This is the case when `schema.items` is a non-empty array
 * that only contains objects.
 *
 * @param schema - The schema in which to check for fixed items
 * @returns - True if there are fixed items in the schema, false otherwise
 */
function isFixedItems(schema) {
    return Array.isArray(schema.items) && schema.items.length > 0 && schema.items.every((item) => isObject(item));
}
//# sourceMappingURL=isFixedItems.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/mergeDefaultsWithFormData.js


/** Merges the `defaults` object of type `T` into the `formData` of type `T`
 *
 * When merging defaults and form data, we want to merge in this specific way:
 * - objects are deeply merged
 * - arrays are merged in such a way that:
 *   - when the array is set in form data, only array entries set in form data
 *     are deeply merged; additional entries from the defaults are ignored unless `mergeExtraArrayDefaults` is true, in
 *     which case the extras are appended onto the end of the form data
 *   - when the array is not set in form data, the default is copied over
 * - scalars are overwritten/set by form data
 *
 * @param [defaults] - The defaults to merge
 * @param [formData] - The form data into which the defaults will be merged
 * @param [mergeExtraArrayDefaults=false] - If true, any additional default array entries are appended onto the formData
 * @returns - The resulting merged form data with defaults
 */
function mergeDefaultsWithFormData(defaults, formData, mergeExtraArrayDefaults = false) {
    if (Array.isArray(formData)) {
        const defaultsArray = Array.isArray(defaults) ? defaults : [];
        const mapped = formData.map((value, idx) => {
            if (defaultsArray[idx]) {
                return mergeDefaultsWithFormData(defaultsArray[idx], value, mergeExtraArrayDefaults);
            }
            return value;
        });
        // Merge any extra defaults when mergeExtraArrayDefaults is true
        if (mergeExtraArrayDefaults && mapped.length < defaultsArray.length) {
            mapped.push(...defaultsArray.slice(mapped.length));
        }
        return mapped;
    }
    if (isObject(formData)) {
        const acc = Object.assign({}, defaults); // Prevent mutation of source object.
        return Object.keys(formData).reduce((acc, key) => {
            acc[key] = mergeDefaultsWithFormData(defaults ? get_default()(defaults, key) : {}, get_default()(formData, key), mergeExtraArrayDefaults);
            return acc;
        }, acc);
    }
    return formData;
}
//# sourceMappingURL=mergeDefaultsWithFormData.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/mergeObjects.js

/** Recursively merge deeply nested objects.
 *
 * @param obj1 - The first object to merge
 * @param obj2 - The second object to merge
 * @param [concatArrays=false] - Optional flag that, when true, will cause arrays to be concatenated. Use
 *          "preventDuplicates" to merge arrays in a manner that prevents any duplicate entries from being merged.
 *          NOTE: Uses shallow comparison for the duplicate checking.
 * @returns - A new object that is the merge of the two given objects
 */
function mergeObjects(obj1, obj2, concatArrays = false) {
    return Object.keys(obj2).reduce((acc, key) => {
        const left = obj1 ? obj1[key] : {}, right = obj2[key];
        if (obj1 && key in obj1 && isObject(right)) {
            acc[key] = mergeObjects(left, right, concatArrays);
        }
        else if (concatArrays && Array.isArray(left) && Array.isArray(right)) {
            let toMerge = right;
            if (concatArrays === 'preventDuplicates') {
                toMerge = right.reduce((result, value) => {
                    if (!left.includes(value)) {
                        result.push(value);
                    }
                    return result;
                }, []);
            }
            acc[key] = left.concat(toMerge);
        }
        else {
            acc[key] = right;
        }
        return acc;
    }, Object.assign({}, obj1)); // Prevent mutation of source object.
}
//# sourceMappingURL=mergeObjects.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/isConstant.js

/** This function checks if the given `schema` matches a single constant value. This happens when either the schema has
 * an `enum` array with a single value or there is a `const` defined.
 *
 * @param schema - The schema for a field
 * @returns - True if the `schema` has a single constant value, false otherwise
 */
function isConstant(schema) {
    return (Array.isArray(schema.enum) && schema.enum.length === 1) || CONST_KEY in schema;
}
//# sourceMappingURL=isConstant.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/isSelect.js


/** Checks to see if the `schema` combination represents a select
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param theSchema - The schema for which check for a select flag is desired
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s
 * @returns - True if schema contains a select, otherwise false
 */
function isSelect(validator, theSchema, rootSchema = {}) {
    const schema = retrieveSchema(validator, theSchema, rootSchema, undefined);
    const altSchemas = schema.oneOf || schema.anyOf;
    if (Array.isArray(schema.enum)) {
        return true;
    }
    if (Array.isArray(altSchemas)) {
        return altSchemas.every((altSchemas) => typeof altSchemas !== 'boolean' && isConstant(altSchemas));
    }
    return false;
}
//# sourceMappingURL=isSelect.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/isMultiSelect.js

/** Checks to see if the `schema` combination represents a multi-select
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param schema - The schema for which check for a multi-select flag is desired
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s
 * @returns - True if schema contains a multi-select, otherwise false
 */
function isMultiSelect(validator, schema, rootSchema) {
    if (!schema.uniqueItems || !schema.items || typeof schema.items === 'boolean') {
        return false;
    }
    return isSelect(validator, schema.items, rootSchema);
}
//# sourceMappingURL=isMultiSelect.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/getDefaultFormState.js














/** Enum that indicates how `schema.additionalItems` should be handled by the `getInnerSchemaForArrayItem()` function.
 */
var AdditionalItemsHandling;
(function (AdditionalItemsHandling) {
    AdditionalItemsHandling[AdditionalItemsHandling["Ignore"] = 0] = "Ignore";
    AdditionalItemsHandling[AdditionalItemsHandling["Invert"] = 1] = "Invert";
    AdditionalItemsHandling[AdditionalItemsHandling["Fallback"] = 2] = "Fallback";
})(AdditionalItemsHandling || (AdditionalItemsHandling = {}));
/** Given a `schema` will return an inner schema that for an array item. This is computed differently based on the
 * `additionalItems` enum and the value of `idx`. There are four possible returns:
 * 1. If `idx` is >= 0, then if `schema.items` is an array the `idx`th element of the array is returned if it is a valid
 *    index and not a boolean, otherwise it falls through to 3.
 * 2. If `schema.items` is not an array AND truthy and not a boolean, then `schema.items` is returned since it actually
 *    is a schema, otherwise it falls through to 3.
 * 3. If `additionalItems` is not `AdditionalItemsHandling.Ignore` and `schema.additionalItems` is an object, then
 *    `schema.additionalItems` is returned since it actually is a schema, otherwise it falls through to 4.
 * 4. {} is returned representing an empty schema
 *
 * @param schema - The schema from which to get the particular item
 * @param [additionalItems=AdditionalItemsHandling.Ignore] - How do we want to handle additional items?
 * @param [idx=-1] - Index, if non-negative, will be used to return the idx-th element in a `schema.items` array
 * @returns - The best fit schema object from the `schema` given the `additionalItems` and `idx` modifiers
 */
function getInnerSchemaForArrayItem(schema, additionalItems = AdditionalItemsHandling.Ignore, idx = -1) {
    if (idx >= 0) {
        if (Array.isArray(schema.items) && idx < schema.items.length) {
            const item = schema.items[idx];
            if (typeof item !== 'boolean') {
                return item;
            }
        }
    }
    else if (schema.items && !Array.isArray(schema.items) && typeof schema.items !== 'boolean') {
        return schema.items;
    }
    if (additionalItems !== AdditionalItemsHandling.Ignore && isObject(schema.additionalItems)) {
        return schema.additionalItems;
    }
    return {};
}
/** Either add `computedDefault` at `key` into `obj` or not add it based on its value, the value of
 * `includeUndefinedValues`, the value of `emptyObjectFields` and if its parent field is required. Generally undefined
 * `computedDefault` values are added only when `includeUndefinedValues` is either true/"excludeObjectChildren". If `
 * includeUndefinedValues` is false and `emptyObjectFields` is not "skipDefaults", then non-undefined and non-empty-object
 * values will be added based on certain conditions.
 *
 * @param obj - The object into which the computed default may be added
 * @param key - The key into the object at which the computed default may be added
 * @param computedDefault - The computed default value that maybe should be added to the obj
 * @param includeUndefinedValues - Optional flag, if true, cause undefined values to be added as defaults.
 *          If "excludeObjectChildren", cause undefined values for this object and pass `includeUndefinedValues` as
 *          false when computing defaults for any nested object properties. If "allowEmptyObject", prevents undefined
 *          values in this object while allow the object itself to be empty and passing `includeUndefinedValues` as
 *          false when computing defaults for any nested object properties.
 * @param isParentRequired - The optional boolean that indicates whether the parent field is required
 * @param requiredFields - The list of fields that are required
 * @param experimental_defaultFormStateBehavior - Optional configuration object, if provided, allows users to override
 *        default form state behavior
 */
function maybeAddDefaultToObject(obj, key, computedDefault, includeUndefinedValues, isParentRequired, requiredFields = [], experimental_defaultFormStateBehavior = {}) {
    const { emptyObjectFields = 'populateAllDefaults' } = experimental_defaultFormStateBehavior;
    if (includeUndefinedValues) {
        obj[key] = computedDefault;
    }
    else if (emptyObjectFields !== 'skipDefaults') {
        if (isObject(computedDefault)) {
            // If isParentRequired is undefined, then we are at the root level of the schema so defer to the requiredness of
            // the field key itself in the `requiredField` list
            const isSelfOrParentRequired = isParentRequired === undefined ? requiredFields.includes(key) : isParentRequired;
            // Store computedDefault if it's a non-empty object(e.g. not {}) and satisfies certain conditions
            // Condition 1: If computedDefault is not empty or if the key is a required field
            // Condition 2: If the parent object is required or emptyObjectFields is not 'populateRequiredDefaults'
            if ((!isEmpty_default()(computedDefault) || requiredFields.includes(key)) &&
                (isSelfOrParentRequired || emptyObjectFields !== 'populateRequiredDefaults')) {
                obj[key] = computedDefault;
            }
        }
        else if (
        // Store computedDefault if it's a defined primitive (e.g., true) and satisfies certain conditions
        // Condition 1: computedDefault is not undefined
        // Condition 2: If emptyObjectFields is 'populateAllDefaults' or if the key is a required field
        computedDefault !== undefined &&
            (emptyObjectFields === 'populateAllDefaults' || requiredFields.includes(key))) {
            obj[key] = computedDefault;
        }
    }
}
/** Computes the defaults for the current `schema` given the `rawFormData` and `parentDefaults` if any. This drills into
 * each level of the schema, recursively, to fill out every level of defaults provided by the schema.
 *
 * @param validator - an implementation of the `ValidatorType` interface that will be used when necessary
 * @param rawSchema - The schema for which the default state is desired
 * @param [props] - Optional props for this function
 * @param [props.parentDefaults] - Any defaults provided by the parent field in the schema
 * @param [props.rootSchema] - The options root schema, used to primarily to look up `$ref`s
 * @param [props.rawFormData] - The current formData, if any, onto which to provide any missing defaults
 * @param [props.includeUndefinedValues=false] - Optional flag, if true, cause undefined values to be added as defaults.
 *          If "excludeObjectChildren", cause undefined values for this object and pass `includeUndefinedValues` as
 *          false when computing defaults for any nested object properties.
 * @param [props._recurseList=[]] - The list of ref names currently being recursed, used to prevent infinite recursion
 * @param [props.experimental_defaultFormStateBehavior] Optional configuration object, if provided, allows users to override default form state behavior
 * @param [props.required] - Optional flag, if true, indicates this schema was required in the parent schema.
 * @returns - The resulting `formData` with all the defaults provided
 */
function computeDefaults(validator, rawSchema, { parentDefaults, rawFormData, rootSchema = {}, includeUndefinedValues = false, _recurseList = [], experimental_defaultFormStateBehavior = undefined, required, } = {}) {
    var _a, _b;
    const formData = (isObject(rawFormData) ? rawFormData : {});
    const schema = isObject(rawSchema) ? rawSchema : {};
    // Compute the defaults recursively: give highest priority to deepest nodes.
    let defaults = parentDefaults;
    // If we get a new schema, then we need to recompute defaults again for the new schema found.
    let schemaToCompute = null;
    let updatedRecurseList = _recurseList;
    if (isObject(defaults) && isObject(schema.default)) {
        // For object defaults, only override parent defaults that are defined in
        // schema.default.
        defaults = mergeObjects(defaults, schema.default);
    }
    else if (DEFAULT_KEY in schema) {
        defaults = schema.default;
    }
    else if (REF_KEY in schema) {
        const refName = schema[REF_KEY];
        // Use referenced schema defaults for this node.
        if (!_recurseList.includes(refName)) {
            updatedRecurseList = _recurseList.concat(refName);
            schemaToCompute = findSchemaDefinition(refName, rootSchema);
        }
    }
    else if (DEPENDENCIES_KEY in schema) {
        const resolvedSchema = resolveDependencies(validator, schema, rootSchema, false, [], formData);
        schemaToCompute = resolvedSchema[0]; // pick the first element from resolve dependencies
    }
    else if (isFixedItems(schema)) {
        defaults = schema.items.map((itemSchema, idx) => computeDefaults(validator, itemSchema, {
            rootSchema,
            includeUndefinedValues,
            _recurseList,
            experimental_defaultFormStateBehavior,
            parentDefaults: Array.isArray(parentDefaults) ? parentDefaults[idx] : undefined,
            rawFormData: formData,
            required,
        }));
    }
    else if (ONE_OF_KEY in schema) {
        const { oneOf, ...remaining } = schema;
        if (oneOf.length === 0) {
            return undefined;
        }
        const discriminator = getDiscriminatorFieldFromSchema(schema);
        schemaToCompute = oneOf[getClosestMatchingOption(validator, rootSchema, isEmpty_default()(formData) ? undefined : formData, oneOf, 0, discriminator)];
        schemaToCompute = mergeSchemas(remaining, schemaToCompute);
    }
    else if (ANY_OF_KEY in schema) {
        const { anyOf, ...remaining } = schema;
        if (anyOf.length === 0) {
            return undefined;
        }
        const discriminator = getDiscriminatorFieldFromSchema(schema);
        schemaToCompute = anyOf[getClosestMatchingOption(validator, rootSchema, isEmpty_default()(formData) ? undefined : formData, anyOf, 0, discriminator)];
        schemaToCompute = mergeSchemas(remaining, schemaToCompute);
    }
    if (schemaToCompute) {
        return computeDefaults(validator, schemaToCompute, {
            rootSchema,
            includeUndefinedValues,
            _recurseList: updatedRecurseList,
            experimental_defaultFormStateBehavior,
            parentDefaults: defaults,
            rawFormData: formData,
            required,
        });
    }
    // No defaults defined for this node, fallback to generic typed ones.
    if (defaults === undefined) {
        defaults = schema.default;
    }
    switch (getSchemaType(schema)) {
        // We need to recurse for object schema inner default values.
        case 'object': {
            // This is a custom addition that fixes this issue:
            // https://github.com/rjsf-team/react-jsonschema-form/issues/3832
            const retrievedSchema = (experimental_defaultFormStateBehavior === null || experimental_defaultFormStateBehavior === void 0 ? void 0 : experimental_defaultFormStateBehavior.allOf) === 'populateDefaults' && ALL_OF_KEY in schema
                ? retrieveSchema(validator, schema, rootSchema, formData)
                : schema;
            const objectDefaults = Object.keys(retrievedSchema.properties || {}).reduce((acc, key) => {
                var _a;
                // Compute the defaults for this node, with the parent defaults we might
                // have from a previous run: defaults[key].
                const computedDefault = computeDefaults(validator, get_default()(retrievedSchema, [PROPERTIES_KEY, key]), {
                    rootSchema,
                    _recurseList,
                    experimental_defaultFormStateBehavior,
                    includeUndefinedValues: includeUndefinedValues === true,
                    parentDefaults: get_default()(defaults, [key]),
                    rawFormData: get_default()(formData, [key]),
                    required: (_a = retrievedSchema.required) === null || _a === void 0 ? void 0 : _a.includes(key),
                });
                maybeAddDefaultToObject(acc, key, computedDefault, includeUndefinedValues, required, retrievedSchema.required, experimental_defaultFormStateBehavior);
                return acc;
            }, {});
            if (retrievedSchema.additionalProperties) {
                // as per spec additionalProperties may be either schema or boolean
                const additionalPropertiesSchema = isObject(retrievedSchema.additionalProperties)
                    ? retrievedSchema.additionalProperties
                    : {};
                const keys = new Set();
                if (isObject(defaults)) {
                    Object.keys(defaults)
                        .filter((key) => !retrievedSchema.properties || !retrievedSchema.properties[key])
                        .forEach((key) => keys.add(key));
                }
                const formDataRequired = [];
                Object.keys(formData)
                    .filter((key) => !retrievedSchema.properties || !retrievedSchema.properties[key])
                    .forEach((key) => {
                    keys.add(key);
                    formDataRequired.push(key);
                });
                keys.forEach((key) => {
                    var _a;
                    const computedDefault = computeDefaults(validator, additionalPropertiesSchema, {
                        rootSchema,
                        _recurseList,
                        experimental_defaultFormStateBehavior,
                        includeUndefinedValues: includeUndefinedValues === true,
                        parentDefaults: get_default()(defaults, [key]),
                        rawFormData: get_default()(formData, [key]),
                        required: (_a = retrievedSchema.required) === null || _a === void 0 ? void 0 : _a.includes(key),
                    });
                    // Since these are additional properties we don't need to add the `experimental_defaultFormStateBehavior` prop
                    maybeAddDefaultToObject(objectDefaults, key, computedDefault, includeUndefinedValues, required, formDataRequired);
                });
            }
            return objectDefaults;
        }
        case 'array': {
            const neverPopulate = ((_a = experimental_defaultFormStateBehavior === null || experimental_defaultFormStateBehavior === void 0 ? void 0 : experimental_defaultFormStateBehavior.arrayMinItems) === null || _a === void 0 ? void 0 : _a.populate) === 'never';
            const ignoreMinItemsFlagSet = ((_b = experimental_defaultFormStateBehavior === null || experimental_defaultFormStateBehavior === void 0 ? void 0 : experimental_defaultFormStateBehavior.arrayMinItems) === null || _b === void 0 ? void 0 : _b.populate) === 'requiredOnly';
            // Inject defaults into existing array defaults
            if (Array.isArray(defaults)) {
                defaults = defaults.map((item, idx) => {
                    const schemaItem = getInnerSchemaForArrayItem(schema, AdditionalItemsHandling.Fallback, idx);
                    return computeDefaults(validator, schemaItem, {
                        rootSchema,
                        _recurseList,
                        experimental_defaultFormStateBehavior,
                        parentDefaults: item,
                        required,
                    });
                });
            }
            // Deeply inject defaults into already existing form data
            if (Array.isArray(rawFormData)) {
                const schemaItem = getInnerSchemaForArrayItem(schema);
                if (neverPopulate) {
                    defaults = rawFormData;
                }
                else {
                    defaults = rawFormData.map((item, idx) => {
                        return computeDefaults(validator, schemaItem, {
                            rootSchema,
                            _recurseList,
                            experimental_defaultFormStateBehavior,
                            rawFormData: item,
                            parentDefaults: get_default()(defaults, [idx]),
                            required,
                        });
                    });
                }
            }
            if (neverPopulate) {
                return defaults !== null && defaults !== void 0 ? defaults : [];
            }
            if (ignoreMinItemsFlagSet && !required) {
                // If no form data exists or defaults are set leave the field empty/non-existent, otherwise
                // return form data/defaults
                return defaults ? defaults : undefined;
            }
            const defaultsLength = Array.isArray(defaults) ? defaults.length : 0;
            if (!schema.minItems ||
                isMultiSelect(validator, schema, rootSchema) ||
                schema.minItems <= defaultsLength) {
                return defaults ? defaults : [];
            }
            const defaultEntries = (defaults || []);
            const fillerSchema = getInnerSchemaForArrayItem(schema, AdditionalItemsHandling.Invert);
            const fillerDefault = fillerSchema.default;
            // Calculate filler entries for remaining items (minItems - existing raw data/defaults)
            const fillerEntries = new Array(schema.minItems - defaultsLength).fill(computeDefaults(validator, fillerSchema, {
                parentDefaults: fillerDefault,
                rootSchema,
                _recurseList,
                experimental_defaultFormStateBehavior,
                required,
            }));
            // then fill up the rest with either the item default or empty, up to minItems
            return defaultEntries.concat(fillerEntries);
        }
    }
    return defaults;
}
/** Returns the superset of `formData` that includes the given set updated to include any missing fields that have
 * computed to have defaults provided in the `schema`.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param theSchema - The schema for which the default state is desired
 * @param [formData] - The current formData, if any, onto which to provide any missing defaults
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s
 * @param [includeUndefinedValues=false] - Optional flag, if true, cause undefined values to be added as defaults.
 *          If "excludeObjectChildren", cause undefined values for this object and pass `includeUndefinedValues` as
 *          false when computing defaults for any nested object properties.
 * @param [experimental_defaultFormStateBehavior] Optional configuration object, if provided, allows users to override default form state behavior
 * @returns - The resulting `formData` with all the defaults provided
 */
function getDefaultFormState(validator, theSchema, formData, rootSchema, includeUndefinedValues = false, experimental_defaultFormStateBehavior) {
    if (!isObject(theSchema)) {
        throw new Error('Invalid schema: ' + theSchema);
    }
    const schema = retrieveSchema(validator, theSchema, rootSchema, formData);
    const defaults = computeDefaults(validator, schema, {
        rootSchema,
        includeUndefinedValues,
        experimental_defaultFormStateBehavior,
        rawFormData: formData,
    });
    if (formData === undefined || formData === null || (typeof formData === 'number' && isNaN(formData))) {
        // No form data? Use schema defaults.
        return defaults;
    }
    const { mergeExtraDefaults } = (experimental_defaultFormStateBehavior === null || experimental_defaultFormStateBehavior === void 0 ? void 0 : experimental_defaultFormStateBehavior.arrayMinItems) || {};
    if (isObject(formData)) {
        return mergeDefaultsWithFormData(defaults, formData, mergeExtraDefaults);
    }
    if (Array.isArray(formData)) {
        return mergeDefaultsWithFormData(defaults, formData, mergeExtraDefaults);
    }
    return formData;
}
//# sourceMappingURL=getDefaultFormState.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/isCustomWidget.js

/** Checks to see if the `uiSchema` contains the `widget` field and that the widget is not `hidden`
 *
 * @param uiSchema - The UI Schema from which to detect if it is customized
 * @returns - True if the `uiSchema` describes a custom widget, false otherwise
 */
function isCustomWidget(uiSchema = {}) {
    return (
    // TODO: Remove the `&& uiSchema['ui:widget'] !== 'hidden'` once we support hidden widgets for arrays.
    // https://rjsf-team.github.io/react-jsonschema-form/docs/usage/widgets/#hidden-widgets
    'widget' in getUiOptions(uiSchema) && getUiOptions(uiSchema)['widget'] !== 'hidden');
}
//# sourceMappingURL=isCustomWidget.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/isFilesArray.js


/** Checks to see if the `schema` and `uiSchema` combination represents an array of files
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param schema - The schema for which check for array of files flag is desired
 * @param [uiSchema={}] - The UI schema from which to check the widget
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s
 * @returns - True if schema/uiSchema contains an array of files, otherwise false
 */
function isFilesArray(validator, schema, uiSchema = {}, rootSchema) {
    if (uiSchema[UI_WIDGET_KEY] === 'files') {
        return true;
    }
    if (schema.items) {
        const itemsSchema = retrieveSchema(validator, schema.items, rootSchema);
        return itemsSchema.type === 'string' && itemsSchema.format === 'data-url';
    }
    return false;
}
//# sourceMappingURL=isFilesArray.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/getDisplayLabel.js






/** Determines whether the combination of `schema` and `uiSchema` properties indicates that the label for the `schema`
 * should be displayed in a UI.
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param schema - The schema for which the display label flag is desired
 * @param [uiSchema={}] - The UI schema from which to derive potentially displayable information
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s
 * @param [globalOptions={}] - The optional Global UI Schema from which to get any fallback `xxx` options
 * @returns - True if the label should be displayed or false if it should not
 */
function getDisplayLabel(validator, schema, uiSchema = {}, rootSchema, globalOptions) {
    const uiOptions = getUiOptions(uiSchema, globalOptions);
    const { label = true } = uiOptions;
    let displayLabel = !!label;
    const schemaType = getSchemaType(schema);
    if (schemaType === 'array') {
        displayLabel =
            isMultiSelect(validator, schema, rootSchema) ||
                isFilesArray(validator, schema, uiSchema, rootSchema) ||
                isCustomWidget(uiSchema);
    }
    if (schemaType === 'object') {
        displayLabel = false;
    }
    if (schemaType === 'boolean' && !uiSchema[UI_WIDGET_KEY]) {
        displayLabel = false;
    }
    if (uiSchema[UI_FIELD_KEY]) {
        displayLabel = false;
    }
    return displayLabel;
}
//# sourceMappingURL=getDisplayLabel.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/mergeValidationData.js


/** Merges the errors in `additionalErrorSchema` into the existing `validationData` by combining the hierarchies in the
 * two `ErrorSchema`s and then appending the error list from the `additionalErrorSchema` obtained by calling
 * `validator.toErrorList()` onto the `errors` in the `validationData`. If no `additionalErrorSchema` is passed, then
 * `validationData` is returned.
 *
 * @param validator - The validator used to convert an ErrorSchema to a list of errors
 * @param validationData - The current `ValidationData` into which to merge the additional errors
 * @param [additionalErrorSchema] - The additional set of errors in an `ErrorSchema`
 * @returns - The `validationData` with the additional errors from `additionalErrorSchema` merged into it, if provided.
 * @deprecated - Use the `validationDataMerge()` function exported from `@rjsf/utils` instead. This function will be
 *        removed in the next major release.
 */
function mergeValidationData(validator, validationData, additionalErrorSchema) {
    if (!additionalErrorSchema) {
        return validationData;
    }
    const { errors: oldErrors, errorSchema: oldErrorSchema } = validationData;
    let errors = validator.toErrorList(additionalErrorSchema);
    let errorSchema = additionalErrorSchema;
    if (!isEmpty_default()(oldErrorSchema)) {
        errorSchema = mergeObjects(oldErrorSchema, additionalErrorSchema, true);
        errors = [...oldErrors].concat(errors);
    }
    return { errorSchema, errors };
}
//# sourceMappingURL=mergeValidationData.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/sanitizeDataForNewSchema.js




const NO_VALUE = Symbol('no Value');
/** Sanitize the `data` associated with the `oldSchema` so it is considered appropriate for the `newSchema`. If the new
 * schema does not contain any properties, then `undefined` is returned to clear all the form data. Due to the nature
 * of schemas, this sanitization happens recursively for nested objects of data. Also, any properties in the old schema
 * that are non-existent in the new schema are set to `undefined`. The data sanitization process has the following flow:
 *
 * - If the new schema is an object that contains a `properties` object then:
 *   - Create a `removeOldSchemaData` object, setting each key in the `oldSchema.properties` having `data` to undefined
 *   - Create an empty `nestedData` object for use in the key filtering below:
 *   - Iterate over each key in the `newSchema.properties` as follows:
 *     - Get the `formValue` of the key from the `data`
 *     - Get the `oldKeySchema` and `newKeyedSchema` for the key, defaulting to `{}` when it doesn't exist
 *     - Retrieve the schema for any refs within each `oldKeySchema` and/or `newKeySchema`
 *     - Get the types of the old and new keyed schemas and if the old doesn't exist or the old & new are the same then:
 *       - If `removeOldSchemaData` has an entry for the key, delete it since the new schema has the same property
 *       - If type of the key in the new schema is `object`:
 *         - Store the value from the recursive `sanitizeDataForNewSchema` call in `nestedData[key]`
 *       - Otherwise, check for default or const values:
 *         - Get the old and new `default` values from the schema and check:
 *           - If the new `default` value does not match the form value:
 *             - If the old `default` value DOES match the form value, then:
 *               - Replace `removeOldSchemaData[key]` with the new `default`
 *               - Otherwise, if the new schema is `readOnly` then replace `removeOldSchemaData[key]` with undefined
 *         - Get the old and new `const` values from the schema and check:
 *           - If the new `const` value does not match the form value:
 *           - If the old `const` value DOES match the form value, then:
 *             - Replace `removeOldSchemaData[key]` with the new `const`
 *             - Otherwise, replace `removeOldSchemaData[key]` with undefined
 *   - Once all keys have been processed, return an object built as follows:
 *     - `{ ...removeOldSchemaData, ...nestedData, ...pick(data, keysToKeep) }`
 * - If the new and old schema types are array and the `data` is an array then:
 *   - If the type of the old and new schema `items` are a non-array objects:
 *     - Retrieve the schema for any refs within each `oldKeySchema.items` and/or `newKeySchema.items`
 *     - If the `type`s of both items are the same (or the old does not have a type):
 *       - If the type is "object", then:
 *         - For each element in the `data` recursively sanitize the data, stopping at `maxItems` if specified
 *       - Otherwise, just return the `data` removing any values after `maxItems` if it is set
 *   - If the type of the old and new schema `items` are booleans of the same value, return `data` as is
 * - Otherwise return `undefined`
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param rootSchema - The root JSON schema of the entire form
 * @param [newSchema] - The new schema for which the data is being sanitized
 * @param [oldSchema] - The old schema from which the data originated
 * @param [data={}] - The form data associated with the schema, defaulting to an empty object when undefined
 * @returns - The new form data, with all the fields uniquely associated with the old schema set
 *      to `undefined`. Will return `undefined` if the new schema is not an object containing properties.
 */
function sanitizeDataForNewSchema(validator, rootSchema, newSchema, oldSchema, data = {}) {
    // By default, we will clear the form data
    let newFormData;
    // If the new schema is of type object and that object contains a list of properties
    if (has_default()(newSchema, PROPERTIES_KEY)) {
        // Create an object containing root-level keys in the old schema, setting each key to undefined to remove the data
        const removeOldSchemaData = {};
        if (has_default()(oldSchema, PROPERTIES_KEY)) {
            const properties = get_default()(oldSchema, PROPERTIES_KEY, {});
            Object.keys(properties).forEach((key) => {
                if (has_default()(data, key)) {
                    removeOldSchemaData[key] = undefined;
                }
            });
        }
        const keys = Object.keys(get_default()(newSchema, PROPERTIES_KEY, {}));
        // Create a place to store nested data that will be a side-effect of the filter
        const nestedData = {};
        keys.forEach((key) => {
            const formValue = get_default()(data, key);
            let oldKeyedSchema = get_default()(oldSchema, [PROPERTIES_KEY, key], {});
            let newKeyedSchema = get_default()(newSchema, [PROPERTIES_KEY, key], {});
            // Resolve the refs if they exist
            if (has_default()(oldKeyedSchema, REF_KEY)) {
                oldKeyedSchema = retrieveSchema(validator, oldKeyedSchema, rootSchema, formValue);
            }
            if (has_default()(newKeyedSchema, REF_KEY)) {
                newKeyedSchema = retrieveSchema(validator, newKeyedSchema, rootSchema, formValue);
            }
            // Now get types and see if they are the same
            const oldSchemaTypeForKey = get_default()(oldKeyedSchema, 'type');
            const newSchemaTypeForKey = get_default()(newKeyedSchema, 'type');
            // Check if the old option has the same key with the same type
            if (!oldSchemaTypeForKey || oldSchemaTypeForKey === newSchemaTypeForKey) {
                if (has_default()(removeOldSchemaData, key)) {
                    // SIDE-EFFECT: remove the undefined value for a key that has the same type between the old and new schemas
                    delete removeOldSchemaData[key];
                }
                // If it is an object, we'll recurse and store the resulting sanitized data for the key
                if (newSchemaTypeForKey === 'object' || (newSchemaTypeForKey === 'array' && Array.isArray(formValue))) {
                    // SIDE-EFFECT: process the new schema type of object recursively to save iterations
                    const itemData = sanitizeDataForNewSchema(validator, rootSchema, newKeyedSchema, oldKeyedSchema, formValue);
                    if (itemData !== undefined || newSchemaTypeForKey === 'array') {
                        // only put undefined values for the array type and not the object type
                        nestedData[key] = itemData;
                    }
                }
                else {
                    // Ok, the non-object types match, let's make sure that a default or a const of a different value is replaced
                    // with the new default or const. This allows the case where two schemas differ that only by the default/const
                    // value to be properly selected
                    const newOptionDefault = get_default()(newKeyedSchema, 'default', NO_VALUE);
                    const oldOptionDefault = get_default()(oldKeyedSchema, 'default', NO_VALUE);
                    if (newOptionDefault !== NO_VALUE && newOptionDefault !== formValue) {
                        if (oldOptionDefault === formValue) {
                            // If the old default matches the formValue, we'll update the new value to match the new default
                            removeOldSchemaData[key] = newOptionDefault;
                        }
                        else if (get_default()(newKeyedSchema, 'readOnly') === true) {
                            // If the new schema has the default set to read-only, treat it like a const and remove the value
                            removeOldSchemaData[key] = undefined;
                        }
                    }
                    const newOptionConst = get_default()(newKeyedSchema, 'const', NO_VALUE);
                    const oldOptionConst = get_default()(oldKeyedSchema, 'const', NO_VALUE);
                    if (newOptionConst !== NO_VALUE && newOptionConst !== formValue) {
                        // Since this is a const, if the old value matches, replace the value with the new const otherwise clear it
                        removeOldSchemaData[key] = oldOptionConst === formValue ? newOptionConst : undefined;
                    }
                }
            }
        });
        newFormData = {
            ...(typeof data == 'string' || Array.isArray(data) ? undefined : data),
            ...removeOldSchemaData,
            ...nestedData,
        };
        // First apply removing the old schema data, then apply the nested data, then apply the old data keys to keep
    }
    else if (get_default()(oldSchema, 'type') === 'array' && get_default()(newSchema, 'type') === 'array' && Array.isArray(data)) {
        let oldSchemaItems = get_default()(oldSchema, 'items');
        let newSchemaItems = get_default()(newSchema, 'items');
        // If any of the array types `items` are arrays (remember arrays are objects) then we'll just drop the data
        // Eventually, we may want to deal with when either of the `items` are arrays since those tuple validations
        if (typeof oldSchemaItems === 'object' &&
            typeof newSchemaItems === 'object' &&
            !Array.isArray(oldSchemaItems) &&
            !Array.isArray(newSchemaItems)) {
            if (has_default()(oldSchemaItems, REF_KEY)) {
                oldSchemaItems = retrieveSchema(validator, oldSchemaItems, rootSchema, data);
            }
            if (has_default()(newSchemaItems, REF_KEY)) {
                newSchemaItems = retrieveSchema(validator, newSchemaItems, rootSchema, data);
            }
            // Now get types and see if they are the same
            const oldSchemaType = get_default()(oldSchemaItems, 'type');
            const newSchemaType = get_default()(newSchemaItems, 'type');
            // Check if the old option has the same key with the same type
            if (!oldSchemaType || oldSchemaType === newSchemaType) {
                const maxItems = get_default()(newSchema, 'maxItems', -1);
                if (newSchemaType === 'object') {
                    newFormData = data.reduce((newValue, aValue) => {
                        const itemValue = sanitizeDataForNewSchema(validator, rootSchema, newSchemaItems, oldSchemaItems, aValue);
                        if (itemValue !== undefined && (maxItems < 0 || newValue.length < maxItems)) {
                            newValue.push(itemValue);
                        }
                        return newValue;
                    }, []);
                }
                else {
                    newFormData = maxItems > 0 && data.length > maxItems ? data.slice(0, maxItems) : data;
                }
            }
        }
        else if (typeof oldSchemaItems === 'boolean' &&
            typeof newSchemaItems === 'boolean' &&
            oldSchemaItems === newSchemaItems) {
            // If they are both booleans and have the same value just return the data as is otherwise fall-thru to undefined
            newFormData = data;
        }
        // Also probably want to deal with `prefixItems` as tuples with the latest 2020 draft
    }
    return newFormData;
}
//# sourceMappingURL=sanitizeDataForNewSchema.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/toIdSchema.js






/** An internal helper that generates an `IdSchema` object for the `schema`, recursively with protection against
 * infinite recursion
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param schema - The schema for which the `IdSchema` is desired
 * @param idPrefix - The prefix to use for the id
 * @param idSeparator - The separator to use for the path segments in the id
 * @param [id] - The base id for the schema
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s
 * @param [formData] - The current formData, if any, to assist retrieving a schema
 * @param [_recurseList=[]] - The list of retrieved schemas currently being recursed, used to prevent infinite recursion
 * @returns - The `IdSchema` object for the `schema`
 */
function toIdSchemaInternal(validator, schema, idPrefix, idSeparator, id, rootSchema, formData, _recurseList = []) {
    if (REF_KEY in schema || DEPENDENCIES_KEY in schema || ALL_OF_KEY in schema) {
        const _schema = retrieveSchema(validator, schema, rootSchema, formData);
        const sameSchemaIndex = _recurseList.findIndex((item) => isEqual_default()(item, _schema));
        if (sameSchemaIndex === -1) {
            return toIdSchemaInternal(validator, _schema, idPrefix, idSeparator, id, rootSchema, formData, _recurseList.concat(_schema));
        }
    }
    if (ITEMS_KEY in schema && !get_default()(schema, [ITEMS_KEY, REF_KEY])) {
        return toIdSchemaInternal(validator, get_default()(schema, ITEMS_KEY), idPrefix, idSeparator, id, rootSchema, formData, _recurseList);
    }
    const $id = id || idPrefix;
    const idSchema = { $id };
    if (getSchemaType(schema) === 'object' && PROPERTIES_KEY in schema) {
        for (const name in schema.properties) {
            const field = get_default()(schema, [PROPERTIES_KEY, name]);
            const fieldId = idSchema[ID_KEY] + idSeparator + name;
            idSchema[name] = toIdSchemaInternal(validator, isObject(field) ? field : {}, idPrefix, idSeparator, fieldId, rootSchema, 
            // It's possible that formData is not an object -- this can happen if an
            // array item has just been added, but not populated with data yet
            get_default()(formData, [name]), _recurseList);
        }
    }
    return idSchema;
}
/** Generates an `IdSchema` object for the `schema`, recursively
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param schema - The schema for which the `IdSchema` is desired
 * @param [id] - The base id for the schema
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s
 * @param [formData] - The current formData, if any, to assist retrieving a schema
 * @param [idPrefix='root'] - The prefix to use for the id
 * @param [idSeparator='_'] - The separator to use for the path segments in the id
 * @returns - The `IdSchema` object for the `schema`
 */
function toIdSchema(validator, schema, id, rootSchema, formData, idPrefix = 'root', idSeparator = '_') {
    return toIdSchemaInternal(validator, schema, idPrefix, idSeparator, id, rootSchema, formData);
}
//# sourceMappingURL=toIdSchema.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/toPathSchema.js







/** An internal helper that generates an `PathSchema` object for the `schema`, recursively with protection against
 * infinite recursion
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param schema - The schema for which the `PathSchema` is desired
 * @param [name=''] - The base name for the schema
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s
 * @param [formData] - The current formData, if any, to assist retrieving a schema
 * @param [_recurseList=[]] - The list of retrieved schemas currently being recursed, used to prevent infinite recursion
 * @returns - The `PathSchema` object for the `schema`
 */
function toPathSchemaInternal(validator, schema, name, rootSchema, formData, _recurseList = []) {
    if (REF_KEY in schema || DEPENDENCIES_KEY in schema || ALL_OF_KEY in schema) {
        const _schema = retrieveSchema(validator, schema, rootSchema, formData);
        const sameSchemaIndex = _recurseList.findIndex((item) => isEqual_default()(item, _schema));
        if (sameSchemaIndex === -1) {
            return toPathSchemaInternal(validator, _schema, name, rootSchema, formData, _recurseList.concat(_schema));
        }
    }
    let pathSchema = {
        [NAME_KEY]: name.replace(/^\./, ''),
    };
    if (ONE_OF_KEY in schema || ANY_OF_KEY in schema) {
        const xxxOf = ONE_OF_KEY in schema ? schema.oneOf : schema.anyOf;
        const discriminator = getDiscriminatorFieldFromSchema(schema);
        const index = getClosestMatchingOption(validator, rootSchema, formData, xxxOf, 0, discriminator);
        const _schema = xxxOf[index];
        pathSchema = {
            ...pathSchema,
            ...toPathSchemaInternal(validator, _schema, name, rootSchema, formData, _recurseList),
        };
    }
    if (ADDITIONAL_PROPERTIES_KEY in schema && schema[ADDITIONAL_PROPERTIES_KEY] !== false) {
        set_default()(pathSchema, RJSF_ADDITONAL_PROPERTIES_FLAG, true);
    }
    if (ITEMS_KEY in schema && Array.isArray(formData)) {
        const { items: schemaItems, additionalItems: schemaAdditionalItems } = schema;
        if (Array.isArray(schemaItems)) {
            formData.forEach((element, i) => {
                if (schemaItems[i]) {
                    pathSchema[i] = toPathSchemaInternal(validator, schemaItems[i], `${name}.${i}`, rootSchema, element, _recurseList);
                }
                else if (schemaAdditionalItems) {
                    pathSchema[i] = toPathSchemaInternal(validator, schemaAdditionalItems, `${name}.${i}`, rootSchema, element, _recurseList);
                }
                else {
                    console.warn(`Unable to generate path schema for "${name}.${i}". No schema defined for it`);
                }
            });
        }
        else {
            formData.forEach((element, i) => {
                pathSchema[i] = toPathSchemaInternal(validator, schemaItems, `${name}.${i}`, rootSchema, element, _recurseList);
            });
        }
    }
    else if (PROPERTIES_KEY in schema) {
        for (const property in schema.properties) {
            const field = get_default()(schema, [PROPERTIES_KEY, property]);
            pathSchema[property] = toPathSchemaInternal(validator, field, `${name}.${property}`, rootSchema, 
            // It's possible that formData is not an object -- this can happen if an
            // array item has just been added, but not populated with data yet
            get_default()(formData, [property]), _recurseList);
        }
    }
    return pathSchema;
}
/** Generates an `PathSchema` object for the `schema`, recursively
 *
 * @param validator - An implementation of the `ValidatorType` interface that will be used when necessary
 * @param schema - The schema for which the `PathSchema` is desired
 * @param [name=''] - The base name for the schema
 * @param [rootSchema] - The root schema, used to primarily to look up `$ref`s
 * @param [formData] - The current formData, if any, to assist retrieving a schema
 * @returns - The `PathSchema` object for the `schema`
 */
function toPathSchema(validator, schema, name = '', rootSchema, formData) {
    return toPathSchemaInternal(validator, schema, name, rootSchema, formData);
}
//# sourceMappingURL=toPathSchema.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schema/index.js














//# sourceMappingURL=index.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/createSchemaUtils.js


/** The `SchemaUtils` class provides a wrapper around the publicly exported APIs in the `utils/schema` directory such
 * that one does not have to explicitly pass the `validator`, `rootSchema`, or `experimental_defaultFormStateBehavior` to each method.
 * Since these generally do not change across a `Form`, this allows for providing a simplified set of APIs to the
 * `@rjsf/core` components and the various themes as well. This class implements the `SchemaUtilsType` interface.
 */
class SchemaUtils {
    /** Constructs the `SchemaUtils` instance with the given `validator` and `rootSchema` stored as instance variables
     *
     * @param validator - An implementation of the `ValidatorType` interface that will be forwarded to all the APIs
     * @param rootSchema - The root schema that will be forwarded to all the APIs
     * @param experimental_defaultFormStateBehavior - Configuration flags to allow users to override default form state behavior
     */
    constructor(validator, rootSchema, experimental_defaultFormStateBehavior) {
        this.rootSchema = rootSchema;
        this.validator = validator;
        this.experimental_defaultFormStateBehavior = experimental_defaultFormStateBehavior;
    }
    /** Returns the `ValidatorType` in the `SchemaUtilsType`
     *
     * @returns - The `ValidatorType`
     */
    getValidator() {
        return this.validator;
    }
    /** Determines whether either the `validator` and `rootSchema` differ from the ones associated with this instance of
     * the `SchemaUtilsType`. If either `validator` or `rootSchema` are falsy, then return false to prevent the creation
     * of a new `SchemaUtilsType` with incomplete properties.
     *
     * @param validator - An implementation of the `ValidatorType` interface that will be compared against the current one
     * @param rootSchema - The root schema that will be compared against the current one
     * @param [experimental_defaultFormStateBehavior] Optional configuration object, if provided, allows users to override default form state behavior
     * @returns - True if the `SchemaUtilsType` differs from the given `validator` or `rootSchema`
     */
    doesSchemaUtilsDiffer(validator, rootSchema, experimental_defaultFormStateBehavior = {}) {
        if (!validator || !rootSchema) {
            return false;
        }
        return (this.validator !== validator ||
            !deepEquals(this.rootSchema, rootSchema) ||
            !deepEquals(this.experimental_defaultFormStateBehavior, experimental_defaultFormStateBehavior));
    }
    /** Returns the superset of `formData` that includes the given set updated to include any missing fields that have
     * computed to have defaults provided in the `schema`.
     *
     * @param schema - The schema for which the default state is desired
     * @param [formData] - The current formData, if any, onto which to provide any missing defaults
     * @param [includeUndefinedValues=false] - Optional flag, if true, cause undefined values to be added as defaults.
     *          If "excludeObjectChildren", pass `includeUndefinedValues` as false when computing defaults for any nested
     *          object properties.
     * @returns - The resulting `formData` with all the defaults provided
     */
    getDefaultFormState(schema, formData, includeUndefinedValues = false) {
        return getDefaultFormState(this.validator, schema, formData, this.rootSchema, includeUndefinedValues, this.experimental_defaultFormStateBehavior);
    }
    /** Determines whether the combination of `schema` and `uiSchema` properties indicates that the label for the `schema`
     * should be displayed in a UI.
     *
     * @param schema - The schema for which the display label flag is desired
     * @param [uiSchema] - The UI schema from which to derive potentially displayable information
     * @param [globalOptions={}] - The optional Global UI Schema from which to get any fallback `xxx` options
     * @returns - True if the label should be displayed or false if it should not
     */
    getDisplayLabel(schema, uiSchema, globalOptions) {
        return getDisplayLabel(this.validator, schema, uiSchema, this.rootSchema, globalOptions);
    }
    /** Determines which of the given `options` provided most closely matches the `formData`.
     * Returns the index of the option that is valid and is the closest match, or 0 if there is no match.
     *
     * The closest match is determined using the number of matching properties, and more heavily favors options with
     * matching readOnly, default, or const values.
     *
     * @param formData - The form data associated with the schema
     * @param options - The list of options that can be selected from
     * @param [selectedOption] - The index of the currently selected option, defaulted to -1 if not specified
     * @param [discriminatorField] - The optional name of the field within the options object whose value is used to
     *          determine which option is selected
     * @returns - The index of the option that is the closest match to the `formData` or the `selectedOption` if no match
     */
    getClosestMatchingOption(formData, options, selectedOption, discriminatorField) {
        return getClosestMatchingOption(this.validator, this.rootSchema, formData, options, selectedOption, discriminatorField);
    }
    /** Given the `formData` and list of `options`, attempts to find the index of the first option that matches the data.
     * Always returns the first option if there is nothing that matches.
     *
     * @param formData - The current formData, if any, used to figure out a match
     * @param options - The list of options to find a matching options from
     * @param [discriminatorField] - The optional name of the field within the options object whose value is used to
     *          determine which option is selected
     * @returns - The firstindex of the matched option or 0 if none is available
     */
    getFirstMatchingOption(formData, options, discriminatorField) {
        return getFirstMatchingOption(this.validator, formData, options, this.rootSchema, discriminatorField);
    }
    /** Given the `formData` and list of `options`, attempts to find the index of the option that best matches the data.
     * Deprecated, use `getFirstMatchingOption()` instead.
     *
     * @param formData - The current formData, if any, onto which to provide any missing defaults
     * @param options - The list of options to find a matching options from
     * @param [discriminatorField] - The optional name of the field within the options object whose value is used to
     *          determine which option is selected
     * @returns - The index of the matched option or 0 if none is available
     * @deprecated
     */
    getMatchingOption(formData, options, discriminatorField) {
        return getMatchingOption(this.validator, formData, options, this.rootSchema, discriminatorField);
    }
    /** Checks to see if the `schema` and `uiSchema` combination represents an array of files
     *
     * @param schema - The schema for which check for array of files flag is desired
     * @param [uiSchema] - The UI schema from which to check the widget
     * @returns - True if schema/uiSchema contains an array of files, otherwise false
     */
    isFilesArray(schema, uiSchema) {
        return isFilesArray(this.validator, schema, uiSchema, this.rootSchema);
    }
    /** Checks to see if the `schema` combination represents a multi-select
     *
     * @param schema - The schema for which check for a multi-select flag is desired
     * @returns - True if schema contains a multi-select, otherwise false
     */
    isMultiSelect(schema) {
        return isMultiSelect(this.validator, schema, this.rootSchema);
    }
    /** Checks to see if the `schema` combination represents a select
     *
     * @param schema - The schema for which check for a select flag is desired
     * @returns - True if schema contains a select, otherwise false
     */
    isSelect(schema) {
        return isSelect(this.validator, schema, this.rootSchema);
    }
    /** Merges the errors in `additionalErrorSchema` into the existing `validationData` by combining the hierarchies in
     * the two `ErrorSchema`s and then appending the error list from the `additionalErrorSchema` obtained by calling
     * `getValidator().toErrorList()` onto the `errors` in the `validationData`. If no `additionalErrorSchema` is passed,
     * then `validationData` is returned.
     *
     * @param validationData - The current `ValidationData` into which to merge the additional errors
     * @param [additionalErrorSchema] - The additional set of errors
     * @returns - The `validationData` with the additional errors from `additionalErrorSchema` merged into it, if provided.
     * @deprecated - Use the `validationDataMerge()` function exported from `@rjsf/utils` instead. This function will be
     *        removed in the next major release.
     */
    mergeValidationData(validationData, additionalErrorSchema) {
        return mergeValidationData(this.validator, validationData, additionalErrorSchema);
    }
    /** Retrieves an expanded schema that has had all of its conditions, additional properties, references and
     * dependencies resolved and merged into the `schema` given a `rawFormData` that is used to do the potentially
     * recursive resolution.
     *
     * @param schema - The schema for which retrieving a schema is desired
     * @param [rawFormData] - The current formData, if any, to assist retrieving a schema
     * @returns - The schema having its conditions, additional properties, references and dependencies resolved
     */
    retrieveSchema(schema, rawFormData) {
        return retrieveSchema(this.validator, schema, this.rootSchema, rawFormData);
    }
    /** Sanitize the `data` associated with the `oldSchema` so it is considered appropriate for the `newSchema`. If the
     * new schema does not contain any properties, then `undefined` is returned to clear all the form data. Due to the
     * nature of schemas, this sanitization happens recursively for nested objects of data. Also, any properties in the
     * old schemas that are non-existent in the new schema are set to `undefined`.
     *
     * @param [newSchema] - The new schema for which the data is being sanitized
     * @param [oldSchema] - The old schema from which the data originated
     * @param [data={}] - The form data associated with the schema, defaulting to an empty object when undefined
     * @returns - The new form data, with all the fields uniquely associated with the old schema set
     *      to `undefined`. Will return `undefined` if the new schema is not an object containing properties.
     */
    sanitizeDataForNewSchema(newSchema, oldSchema, data) {
        return sanitizeDataForNewSchema(this.validator, this.rootSchema, newSchema, oldSchema, data);
    }
    /** Generates an `IdSchema` object for the `schema`, recursively
     *
     * @param schema - The schema for which the display label flag is desired
     * @param [id] - The base id for the schema
     * @param [formData] - The current formData, if any, onto which to provide any missing defaults
     * @param [idPrefix='root'] - The prefix to use for the id
     * @param [idSeparator='_'] - The separator to use for the path segments in the id
     * @returns - The `IdSchema` object for the `schema`
     */
    toIdSchema(schema, id, formData, idPrefix = 'root', idSeparator = '_') {
        return toIdSchema(this.validator, schema, id, this.rootSchema, formData, idPrefix, idSeparator);
    }
    /** Generates an `PathSchema` object for the `schema`, recursively
     *
     * @param schema - The schema for which the display label flag is desired
     * @param [name] - The base name for the schema
     * @param [formData] - The current formData, if any, onto which to provide any missing defaults
     * @returns - The `PathSchema` object for the `schema`
     */
    toPathSchema(schema, name, formData) {
        return toPathSchema(this.validator, schema, name, this.rootSchema, formData);
    }
}
/** Creates a `SchemaUtilsType` interface that is based around the given `validator` and `rootSchema` parameters. The
 * resulting interface implementation will forward the `validator` and `rootSchema` to all the wrapped APIs.
 *
 * @param validator - an implementation of the `ValidatorType` interface that will be forwarded to all the APIs
 * @param rootSchema - The root schema that will be forwarded to all the APIs
 * @param [experimental_defaultFormStateBehavior] Optional configuration object, if provided, allows users to override default form state behavior
 * @returns - An implementation of a `SchemaUtilsType` interface
 */
function createSchemaUtils(validator, rootSchema, experimental_defaultFormStateBehavior = {}) {
    return new SchemaUtils(validator, rootSchema, experimental_defaultFormStateBehavior);
}
//# sourceMappingURL=createSchemaUtils.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/dataURItoBlob.js
/** Given the `FileReader.readAsDataURL()` based `dataURI` extracts that data into an actual Blob along with the name
 * of that Blob if provided in the URL. If no name is provided, then the name falls back to `unknown`.
 *
 * @param dataURI - The `DataUrl` potentially containing name and raw data to be converted to a Blob
 * @returns - an object containing a Blob and its name, extracted from the URI
 */
function dataURItoBlob(dataURI) {
    // Split metadata from data
    const splitted = dataURI.split(',');
    // Split params
    const params = splitted[0].split(';');
    // Get mime-type from params
    const type = params[0].replace('data:', '');
    // Filter the name property from params
    const properties = params.filter((param) => {
        return param.split('=')[0] === 'name';
    });
    // Look for the name and use unknown if no name property.
    let name;
    if (properties.length !== 1) {
        name = 'unknown';
    }
    else {
        // Because we filtered out the other property,
        // we only have the name case here, which we decode to make it human-readable
        name = decodeURI(properties[0].split('=')[1]);
    }
    // Built the Uint8Array Blob parameter from the base64 string.
    try {
        const binary = atob(splitted[1]);
        const array = [];
        for (let i = 0; i < binary.length; i++) {
            array.push(binary.charCodeAt(i));
        }
        // Create the blob object
        const blob = new window.Blob([new Uint8Array(array)], { type });
        return { blob, name };
    }
    catch (error) {
        return { blob: { size: 0, type: error.message }, name: dataURI };
    }
}
//# sourceMappingURL=dataURItoBlob.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/replaceStringParameters.js
/** Potentially substitutes all replaceable parameters with the associated value(s) from the `params` if available. When
 * a `params` array is provided, each value in the array is used to replace any of the replaceable parameters in the
 * `inputString` using the `%1`, `%2`, etc. replacement specifiers.
 *
 * @param inputString - The string which will be potentially updated with replacement parameters
 * @param params - The optional list of replaceable parameter values to substitute into the english string
 * @returns - The updated string with any replacement specifiers replaced
 */
function replaceStringParameters(inputString, params) {
    let output = inputString;
    if (Array.isArray(params)) {
        const parts = output.split(/(%\d)/);
        params.forEach((param, index) => {
            const partIndex = parts.findIndex((part) => part === `%${index + 1}`);
            if (partIndex >= 0) {
                parts[partIndex] = param;
            }
        });
        output = parts.join('');
    }
    return output;
}
//# sourceMappingURL=replaceStringParameters.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/englishStringTranslator.js

/** Translates a `TranslatableString` value `stringToTranslate` into english. When a `params` array is provided, each
 * value in the array is used to replace any of the replaceable parameters in the `stringToTranslate` using the `%1`,
 * `%2`, etc. replacement specifiers.
 *
 * @param stringToTranslate - The `TranslatableString` value to convert to english
 * @param params - The optional list of replaceable parameter values to substitute into the english string
 * @returns - The `stringToTranslate` itself with any replaceable parameter values substituted
 */
function englishStringTranslator(stringToTranslate, params) {
    return replaceStringParameters(stringToTranslate, params);
}
//# sourceMappingURL=englishStringTranslator.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/enumOptionsValueForIndex.js
/** Returns the value(s) from `allEnumOptions` at the index(es) provided by `valueIndex`. If `valueIndex` is not an
 * array AND the index is not valid for `allEnumOptions`, `emptyValue` is returned. If `valueIndex` is an array, AND it
 * contains an invalid index, the returned array will have the resulting undefined values filtered out, leaving only
 * valid values or in the worst case, an empty array.
 *
 * @param valueIndex - The index(es) of the value(s) that should be returned
 * @param [allEnumOptions=[]] - The list of all the known enumOptions
 * @param [emptyValue] - The value to return when the non-array `valueIndex` does not refer to a real option
 * @returns - The single or list of values specified by the single or list of indexes if they are valid. Otherwise,
 *        `emptyValue` or an empty list.
 */
function enumOptionsValueForIndex(valueIndex, allEnumOptions = [], emptyValue) {
    if (Array.isArray(valueIndex)) {
        return valueIndex.map((index) => enumOptionsValueForIndex(index, allEnumOptions)).filter((val) => val);
    }
    // So Number(null) and Number('') both return 0, so use emptyValue for those two values
    const index = valueIndex === '' || valueIndex === null ? -1 : Number(valueIndex);
    const option = allEnumOptions[index];
    return option ? option.value : emptyValue;
}
//# sourceMappingURL=enumOptionsValueForIndex.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/enumOptionsDeselectValue.js


/** Removes the enum option value at the `valueIndex` from the currently `selected` (list of) value(s). If `selected` is
 * a list, then that list is updated to remove the enum option value with the `valueIndex` in `allEnumOptions`. If it is
 * a single value, then if the enum option value with the `valueIndex` in `allEnumOptions` matches `selected`, undefined
 * is returned, otherwise the `selected` value is returned.
 *
 * @param valueIndex - The index of the value to be removed from the selected list or single value
 * @param selected - The current (list of) selected value(s)
 * @param [allEnumOptions=[]] - The list of all the known enumOptions
 * @returns - The updated `selected` with the enum option value at `valueIndex` in `allEnumOptions` removed from it,
 *        unless `selected` is a single value. In that case, if the `valueIndex` value matches `selected`, returns
 *        undefined, otherwise `selected`.
 */
function enumOptionsDeselectValue(valueIndex, selected, allEnumOptions = []) {
    const value = enumOptionsValueForIndex(valueIndex, allEnumOptions);
    if (Array.isArray(selected)) {
        return selected.filter((v) => !isEqual_default()(v, value));
    }
    return isEqual_default()(value, selected) ? undefined : selected;
}
//# sourceMappingURL=enumOptionsDeselectValue.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/enumOptionsIsSelected.js

/** Determines whether the given `value` is (one of) the `selected` value(s).
 *
 * @param value - The value being checked to see if it is selected
 * @param selected - The current selected value or list of values
 * @returns - true if the `value` is one of the `selected` ones, false otherwise
 */
function enumOptionsIsSelected(value, selected) {
    if (Array.isArray(selected)) {
        return selected.some((sel) => isEqual_default()(sel, value));
    }
    return isEqual_default()(selected, value);
}
//# sourceMappingURL=enumOptionsIsSelected.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/enumOptionsIndexForValue.js

/** Returns the index(es) of the options in `allEnumOptions` whose value(s) match the ones in `value`. All the
 * `enumOptions` are filtered based on whether they are a "selected" `value` and the index of each selected one is then
 * stored in an array. If `multiple` is true, that array is returned, otherwise the first element in the array is
 * returned.
 *
 * @param value - The single value or list of values for which indexes are desired
 * @param [allEnumOptions=[]] - The list of all the known enumOptions
 * @param [multiple=false] - Optional flag, if true will return a list of index, otherwise a single one
 * @returns - A single string index for the first `value` in `allEnumOptions`, if not `multiple`. Otherwise, the list
 *        of indexes for (each of) the value(s) in `value`.
 */
function enumOptionsIndexForValue(value, allEnumOptions = [], multiple = false) {
    const selectedIndexes = allEnumOptions
        .map((opt, index) => (enumOptionsIsSelected(opt.value, value) ? String(index) : undefined))
        .filter((opt) => typeof opt !== 'undefined');
    if (!multiple) {
        return selectedIndexes[0];
    }
    return selectedIndexes;
}
//# sourceMappingURL=enumOptionsIndexForValue.js.map
// EXTERNAL MODULE: ../node_modules/lodash/isNil.js
var isNil = __webpack_require__(30644);
var isNil_default = /*#__PURE__*/__webpack_require__.n(isNil);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/enumOptionsSelectValue.js


/** Add the enum option value at the `valueIndex` to the list of `selected` values in the proper order as defined by
 * `allEnumOptions`
 *
 * @param valueIndex - The index of the value that should be selected
 * @param selected - The current list of selected values
 * @param [allEnumOptions=[]] - The list of all the known enumOptions
 * @returns - The updated list of selected enum values with enum value at the `valueIndex` added to it
 */
function enumOptionsSelectValue(valueIndex, selected, allEnumOptions = []) {
    const value = enumOptionsValueForIndex(valueIndex, allEnumOptions);
    if (!isNil_default()(value)) {
        const index = allEnumOptions.findIndex((opt) => value === opt.value);
        const all = allEnumOptions.map(({ value: val }) => val);
        const updated = selected.slice(0, index).concat(value, selected.slice(index));
        // As inserting values at predefined index positions doesn't work with empty
        // arrays, we need to reorder the updated selection to match the initial order
        return updated.sort((a, b) => Number(all.indexOf(a) > all.indexOf(b)));
    }
    return selected;
}
//# sourceMappingURL=enumOptionsSelectValue.js.map
// EXTERNAL MODULE: ../node_modules/lodash/cloneDeep.js
var cloneDeep = __webpack_require__(30454);
var cloneDeep_default = /*#__PURE__*/__webpack_require__.n(cloneDeep);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/ErrorSchemaBuilder.js




/** The `ErrorSchemaBuilder<T>` is used to build an `ErrorSchema<T>` since the definition of the `ErrorSchema` type is
 * designed for reading information rather than writing it. Use this class to add, replace or clear errors in an error
 * schema by using either dotted path or an array of path names. Once you are done building the `ErrorSchema`, you can
 * get the result and/or reset all the errors back to an initial set and start again.
 */
class ErrorSchemaBuilder {
    /** Construct an `ErrorSchemaBuilder` with an optional initial set of errors in an `ErrorSchema`.
     *
     * @param [initialSchema] - The optional set of initial errors, that will be cloned into the class
     */
    constructor(initialSchema) {
        /** The error schema being built
         *
         * @private
         */
        this.errorSchema = {};
        this.resetAllErrors(initialSchema);
    }
    /** Returns the `ErrorSchema` that has been updated by the methods of the `ErrorSchemaBuilder`
     */
    get ErrorSchema() {
        return this.errorSchema;
    }
    /** Will get an existing `ErrorSchema` at the specified `pathOfError` or create and return one.
     *
     * @param [pathOfError] - The optional path into the `ErrorSchema` at which to add the error(s)
     * @returns - The error block for the given `pathOfError` or the root if not provided
     * @private
     */
    getOrCreateErrorBlock(pathOfError) {
        const hasPath = (Array.isArray(pathOfError) && pathOfError.length > 0) || typeof pathOfError === 'string';
        let errorBlock = hasPath ? get_default()(this.errorSchema, pathOfError) : this.errorSchema;
        if (!errorBlock && pathOfError) {
            errorBlock = {};
            set_default()(this.errorSchema, pathOfError, errorBlock);
        }
        return errorBlock;
    }
    /** Resets all errors in the `ErrorSchemaBuilder` back to the `initialSchema` if provided, otherwise an empty set.
     *
     * @param [initialSchema] - The optional set of initial errors, that will be cloned into the class
     * @returns - The `ErrorSchemaBuilder` object for chaining purposes
     */
    resetAllErrors(initialSchema) {
        this.errorSchema = initialSchema ? cloneDeep_default()(initialSchema) : {};
        return this;
    }
    /** Adds the `errorOrList` to the list of errors in the `ErrorSchema` at either the root level or the location within
     * the schema described by the `pathOfError`. For more information about how to specify the path see the
     * [eslint lodash plugin docs](https://github.com/wix/eslint-plugin-lodash/blob/master/docs/rules/path-style.md).
     *
     * @param errorOrList - The error or list of errors to add into the `ErrorSchema`
     * @param [pathOfError] - The optional path into the `ErrorSchema` at which to add the error(s)
     * @returns - The `ErrorSchemaBuilder` object for chaining purposes
     */
    addErrors(errorOrList, pathOfError) {
        const errorBlock = this.getOrCreateErrorBlock(pathOfError);
        let errorsList = get_default()(errorBlock, ERRORS_KEY);
        if (!Array.isArray(errorsList)) {
            errorsList = [];
            errorBlock[ERRORS_KEY] = errorsList;
        }
        if (Array.isArray(errorOrList)) {
            errorsList.push(...errorOrList);
        }
        else {
            errorsList.push(errorOrList);
        }
        return this;
    }
    /** Sets/replaces the `errorOrList` as the error(s) in the `ErrorSchema` at either the root level or the location
     * within the schema described by the `pathOfError`. For more information about how to specify the path see the
     * [eslint lodash plugin docs](https://github.com/wix/eslint-plugin-lodash/blob/master/docs/rules/path-style.md).
     *
     * @param errorOrList - The error or list of errors to set into the `ErrorSchema`
     * @param [pathOfError] - The optional path into the `ErrorSchema` at which to set the error(s)
     * @returns - The `ErrorSchemaBuilder` object for chaining purposes
     */
    setErrors(errorOrList, pathOfError) {
        const errorBlock = this.getOrCreateErrorBlock(pathOfError);
        // Effectively clone the array being given to prevent accidental outside manipulation of the given list
        const listToAdd = Array.isArray(errorOrList) ? [...errorOrList] : [errorOrList];
        set_default()(errorBlock, ERRORS_KEY, listToAdd);
        return this;
    }
    /** Clears the error(s) in the `ErrorSchema` at either the root level or the location within the schema described by
     * the `pathOfError`. For more information about how to specify the path see the
     * [eslint lodash plugin docs](https://github.com/wix/eslint-plugin-lodash/blob/master/docs/rules/path-style.md).
     *
     * @param [pathOfError] - The optional path into the `ErrorSchema` at which to clear the error(s)
     * @returns - The `ErrorSchemaBuilder` object for chaining purposes
     */
    clearErrors(pathOfError) {
        const errorBlock = this.getOrCreateErrorBlock(pathOfError);
        set_default()(errorBlock, ERRORS_KEY, []);
        return this;
    }
}
//# sourceMappingURL=ErrorSchemaBuilder.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/getDateElementProps.js
/** Given date & time information with optional yearRange & format, returns props for DateElement
 *
 * @param date - Object containing date with optional time information
 * @param time - Determines whether to include time or not
 * @param [yearRange=[1900, new Date().getFullYear() + 2]] - Controls the list of years to be displayed
 * @param [format='YMD'] - Controls the order in which day, month and year input element will be displayed
 * @returns Array of props for DateElement
 */
function getDateElementProps(date, time, yearRange = [1900, new Date().getFullYear() + 2], format = 'YMD') {
    const { day, month, year, hour, minute, second } = date;
    const dayObj = { type: 'day', range: [1, 31], value: day };
    const monthObj = { type: 'month', range: [1, 12], value: month };
    const yearObj = { type: 'year', range: yearRange, value: year };
    const dateElementProp = [];
    switch (format) {
        case 'MDY':
            dateElementProp.push(monthObj, dayObj, yearObj);
            break;
        case 'DMY':
            dateElementProp.push(dayObj, monthObj, yearObj);
            break;
        case 'YMD':
        default:
            dateElementProp.push(yearObj, monthObj, dayObj);
    }
    if (time) {
        dateElementProp.push({ type: 'hour', range: [0, 23], value: hour }, { type: 'minute', range: [0, 59], value: minute }, { type: 'second', range: [0, 59], value: second });
    }
    return dateElementProp;
}
//# sourceMappingURL=getDateElementProps.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/rangeSpec.js
/** Extracts the range spec information `{ step?: number, min?: number, max?: number }` that can be spread onto an HTML
 * input from the range analog in the schema `{ multipleOf?: number, minimum?: number, maximum?: number }`.
 *
 * @param schema - The schema from which to extract the range spec
 * @returns - A range specification from the schema
 */
function rangeSpec(schema) {
    const spec = {};
    if (schema.multipleOf) {
        spec.step = schema.multipleOf;
    }
    if (schema.minimum || schema.minimum === 0) {
        spec.min = schema.minimum;
    }
    if (schema.maximum || schema.maximum === 0) {
        spec.max = schema.maximum;
    }
    return spec;
}
//# sourceMappingURL=rangeSpec.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/getInputProps.js

/** Using the `schema`, `defaultType` and `options`, extract out the props for the <input> element that make sense.
 *
 * @param schema - The schema for the field provided by the widget
 * @param [defaultType] - The default type, if any, for the field provided by the widget
 * @param [options={}] - The UI Options for the field provided by the widget
 * @param [autoDefaultStepAny=true] - Determines whether to auto-default step=any when the type is number and no step
 * @returns - The extracted `InputPropsType` object
 */
function getInputProps(schema, defaultType, options = {}, autoDefaultStepAny = true) {
    const inputProps = {
        type: defaultType || 'text',
        ...rangeSpec(schema),
    };
    // If options.inputType is set use that as the input type
    if (options.inputType) {
        inputProps.type = options.inputType;
    }
    else if (!defaultType) {
        // If the schema is of type number or integer, set the input type to number
        if (schema.type === 'number') {
            inputProps.type = 'number';
            // Only add step if one isn't already defined and we are auto-defaulting the "any" step
            if (autoDefaultStepAny && inputProps.step === undefined) {
                // Setting step to 'any' fixes a bug in Safari where decimals are not
                // allowed in number inputs
                inputProps.step = 'any';
            }
        }
        else if (schema.type === 'integer') {
            inputProps.type = 'number';
            // Only add step if one isn't already defined
            if (inputProps.step === undefined) {
                // Since this is integer, you always want to step up or down in multiples of 1
                inputProps.step = 1;
            }
        }
    }
    if (options.autocomplete) {
        inputProps.autoComplete = options.autocomplete;
    }
    return inputProps;
}
//# sourceMappingURL=getInputProps.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/getSubmitButtonOptions.js


/** The default submit button options, exported for testing purposes
 */
const DEFAULT_OPTIONS = {
    props: {
        disabled: false,
    },
    submitText: 'Submit',
    norender: false,
};
/** Extracts any `ui:submitButtonOptions` from the `uiSchema` and merges them onto the `DEFAULT_OPTIONS`
 *
 * @param [uiSchema={}] - the UI Schema from which to extract submit button props
 * @returns - The merging of the `DEFAULT_OPTIONS` with any custom ones
 */
function getSubmitButtonOptions(uiSchema = {}) {
    const uiOptions = getUiOptions(uiSchema);
    if (uiOptions && uiOptions[SUBMIT_BTN_OPTIONS_KEY]) {
        const options = uiOptions[SUBMIT_BTN_OPTIONS_KEY];
        return { ...DEFAULT_OPTIONS, ...options };
    }
    return DEFAULT_OPTIONS;
}
//# sourceMappingURL=getSubmitButtonOptions.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/getTemplate.js
/** Returns the template with the given `name` from either the `uiSchema` if it is defined or from the `registry`
 * otherwise. NOTE, since `ButtonTemplates` are not overridden in `uiSchema` only those in the `registry` are returned.
 *
 * @param name - The name of the template to fetch, restricted to the keys of `TemplatesType`
 * @param registry - The `Registry` from which to read the template
 * @param [uiOptions={}] - The `UIOptionsType` from which to read an alternate template
 * @returns - The template from either the `uiSchema` or `registry` for the `name`
 */
function getTemplate(name, registry, uiOptions = {}) {
    const { templates } = registry;
    if (name === 'ButtonTemplates') {
        return templates[name];
    }
    return (
    // Evaluating uiOptions[name] results in TS2590: Expression produces a union type that is too complex to represent
    // To avoid that, we cast uiOptions to `any` before accessing the name field
    uiOptions[name] || templates[name]);
}
//# sourceMappingURL=getTemplate.js.map
// EXTERNAL MODULE: ../node_modules/react/jsx-runtime.js
var jsx_runtime = __webpack_require__(24246);
// EXTERNAL MODULE: consume shared module (default) react@~18.2.0 (singleton) (fallback: ../node_modules/react/index.js)
var index_js_ = __webpack_require__(78156);
// EXTERNAL MODULE: ../node_modules/react-is/index.js
var react_is = __webpack_require__(19185);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/getWidget.js






/** The map of schema types to widget type to widget name
 */
const widgetMap = {
    boolean: {
        checkbox: 'CheckboxWidget',
        radio: 'RadioWidget',
        select: 'SelectWidget',
        hidden: 'HiddenWidget',
    },
    string: {
        text: 'TextWidget',
        password: 'PasswordWidget',
        email: 'EmailWidget',
        hostname: 'TextWidget',
        ipv4: 'TextWidget',
        ipv6: 'TextWidget',
        uri: 'URLWidget',
        'data-url': 'FileWidget',
        radio: 'RadioWidget',
        select: 'SelectWidget',
        textarea: 'TextareaWidget',
        hidden: 'HiddenWidget',
        date: 'DateWidget',
        datetime: 'DateTimeWidget',
        'date-time': 'DateTimeWidget',
        'alt-date': 'AltDateWidget',
        'alt-datetime': 'AltDateTimeWidget',
        time: 'TimeWidget',
        color: 'ColorWidget',
        file: 'FileWidget',
    },
    number: {
        text: 'TextWidget',
        select: 'SelectWidget',
        updown: 'UpDownWidget',
        range: 'RangeWidget',
        radio: 'RadioWidget',
        hidden: 'HiddenWidget',
    },
    integer: {
        text: 'TextWidget',
        select: 'SelectWidget',
        updown: 'UpDownWidget',
        range: 'RangeWidget',
        radio: 'RadioWidget',
        hidden: 'HiddenWidget',
    },
    array: {
        select: 'SelectWidget',
        checkboxes: 'CheckboxesWidget',
        files: 'FileWidget',
        hidden: 'HiddenWidget',
    },
};
/** Wraps the given widget with stateless functional component that will merge any `defaultProps.options` with the
 * `options` that are provided in the props. It will add the wrapper component as a `MergedWidget` property onto the
 * `Widget` so that future attempts to wrap `AWidget` will return the already existing wrapper.
 *
 * @param AWidget - A widget that will be wrapped or one that is already wrapped
 * @returns - The wrapper widget
 */
function mergeWidgetOptions(AWidget) {
    let MergedWidget = get_default()(AWidget, 'MergedWidget');
    // cache return value as property of widget for proper react reconciliation
    if (!MergedWidget) {
        const defaultOptions = (AWidget.defaultProps && AWidget.defaultProps.options) || {};
        MergedWidget = ({ options, ...props }) => {
            return (0,jsx_runtime.jsx)(AWidget, { options: { ...defaultOptions, ...options }, ...props });
        };
        set_default()(AWidget, 'MergedWidget', MergedWidget);
    }
    return MergedWidget;
}
/** Given a schema representing a field to render and either the name or actual `Widget` implementation, returns the
 * React component that is used to render the widget. If the `widget` is already a React component, then it is wrapped
 * with a `MergedWidget`. Otherwise an attempt is made to look up the widget inside of the `registeredWidgets` map based
 * on the schema type and `widget` name. If no widget component can be found an `Error` is thrown.
 *
 * @param schema - The schema for the field
 * @param [widget] - Either the name of the widget OR a `Widget` implementation to use
 * @param [registeredWidgets={}] - A registry of widget name to `Widget` implementation
 * @returns - The `Widget` component to use
 * @throws - An error if there is no `Widget` component that can be returned
 */
function getWidget(schema, widget, registeredWidgets = {}) {
    const type = getSchemaType(schema);
    if (typeof widget === 'function' ||
        (widget && react_is.isForwardRef((0,index_js_.createElement)(widget))) ||
        react_is.isMemo(widget)) {
        return mergeWidgetOptions(widget);
    }
    if (typeof widget !== 'string') {
        throw new Error(`Unsupported widget definition: ${typeof widget}`);
    }
    if (widget in registeredWidgets) {
        const registeredWidget = registeredWidgets[widget];
        return getWidget(schema, registeredWidget, registeredWidgets);
    }
    if (typeof type === 'string') {
        if (!(type in widgetMap)) {
            throw new Error(`No widget for type '${type}'`);
        }
        if (widget in widgetMap[type]) {
            const registeredWidget = registeredWidgets[widgetMap[type][widget]];
            return getWidget(schema, registeredWidget, registeredWidgets);
        }
    }
    throw new Error(`No widget '${widget}' for type '${type}'`);
}
//# sourceMappingURL=getWidget.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/hashForSchema.js
/** JS has no built-in hashing function, so rolling our own
 *  based on Java's hashing fn:
 *  http://www.java2s.com/example/nodejs-utility-method/string-hash/hashcode-4dc2b.html
 *
 * @param string - The string for which to get the hash
 * @returns - The resulting hash of the string in hex format
 */
function hashString(string) {
    let hash = 0;
    for (let i = 0; i < string.length; i += 1) {
        const chr = string.charCodeAt(i);
        hash = (hash << 5) - hash + chr;
        hash = hash & hash; // Convert to 32bit integer
    }
    return hash.toString(16);
}
/** Stringifies the schema and returns the hash of the resulting string. Sorts schema fields
 * in consistent order before stringify to prevent different hash ids for the same schema.
 *
 * @param schema - The schema for which the hash is desired
 * @returns - The string obtained from the hash of the stringified schema
 */
function hashForSchema(schema) {
    const allKeys = new Set();
    // solution source: https://stackoverflow.com/questions/16167581/sort-object-properties-and-json-stringify/53593328#53593328
    JSON.stringify(schema, (key, value) => (allKeys.add(key), value));
    return hashString(JSON.stringify(schema, Array.from(allKeys).sort()));
}
//# sourceMappingURL=hashForSchema.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/hasWidget.js

/** Detects whether the `widget` exists for the `schema` with the associated `registryWidgets` and returns true if it
 * does, or false if it doesn't.
 *
 * @param schema - The schema for the field
 * @param widget - Either the name of the widget OR a `Widget` implementation to use
 * @param [registeredWidgets={}] - A registry of widget name to `Widget` implementation
 * @returns - True if the widget exists, false otherwise
 */
function hasWidget(schema, widget, registeredWidgets = {}) {
    try {
        getWidget(schema, widget, registeredWidgets);
        return true;
    }
    catch (e) {
        const err = e;
        if (err.message && (err.message.startsWith('No widget') || err.message.startsWith('Unsupported widget'))) {
            return false;
        }
        throw e;
    }
}
//# sourceMappingURL=hasWidget.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/idGenerators.js


/** Generates a consistent `id` pattern for a given `id` and a `suffix`
 *
 * @param id - Either simple string id or an IdSchema from which to extract it
 * @param suffix - The suffix to append to the id
 */
function idGenerator(id, suffix) {
    const theId = isString_default()(id) ? id : id[ID_KEY];
    return `${theId}__${suffix}`;
}
/** Return a consistent `id` for the field description element
 *
 * @param id - Either simple string id or an IdSchema from which to extract it
 * @returns - The consistent id for the field description element from the given `id`
 */
function descriptionId(id) {
    return idGenerator(id, 'description');
}
/** Return a consistent `id` for the field error element
 *
 * @param id - Either simple string id or an IdSchema from which to extract it
 * @returns - The consistent id for the field error element from the given `id`
 */
function errorId(id) {
    return idGenerator(id, 'error');
}
/** Return a consistent `id` for the field examples element
 *
 * @param id - Either simple string id or an IdSchema from which to extract it
 * @returns - The consistent id for the field examples element from the given `id`
 */
function examplesId(id) {
    return idGenerator(id, 'examples');
}
/** Return a consistent `id` for the field help element
 *
 * @param id - Either simple string id or an IdSchema from which to extract it
 * @returns - The consistent id for the field help element from the given `id`
 */
function helpId(id) {
    return idGenerator(id, 'help');
}
/** Return a consistent `id` for the field title element
 *
 * @param id - Either simple string id or an IdSchema from which to extract it
 * @returns - The consistent id for the field title element from the given `id`
 */
function titleId(id) {
    return idGenerator(id, 'title');
}
/** Return a list of element ids that contain additional information about the field that can be used to as the aria
 * description of the field. This is correctly omitting `titleId` which would be "labeling" rather than "describing" the
 * element.
 *
 * @param id - Either simple string id or an IdSchema from which to extract it
 * @param [includeExamples=false] - Optional flag, if true, will add the `examplesId` into the list
 * @returns - The string containing the list of ids for use in an `aria-describedBy` attribute
 */
function ariaDescribedByIds(id, includeExamples = false) {
    const examples = includeExamples ? ` ${examplesId(id)}` : '';
    return `${errorId(id)} ${descriptionId(id)} ${helpId(id)}${examples}`;
}
/** Return a consistent `id` for the `optionIndex`s of a `Radio` or `Checkboxes` widget
 *
 * @param id - The id of the parent component for the option
 * @param optionIndex - The index of the option for which the id is desired
 * @returns - An id for the option index based on the parent `id`
 */
function optionId(id, optionIndex) {
    return `${id}-${optionIndex}`;
}
//# sourceMappingURL=idGenerators.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/labelValue.js
function labelValue(label, hideLabel, fallback) {
    return hideLabel ? fallback : label;
}
//# sourceMappingURL=labelValue.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/localToUTC.js
/** Converts a local Date string into a UTC date string
 *
 * @param dateString - The string representation of a date as accepted by the `Date()` constructor
 * @returns - A UTC date string if `dateString` is truthy, otherwise undefined
 */
function localToUTC(dateString) {
    return dateString ? new Date(dateString).toJSON() : undefined;
}
//# sourceMappingURL=localToUTC.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/toConstant.js

/** Returns the constant value from the schema when it is either a single value enum or has a const key. Otherwise
 * throws an error.
 *
 * @param schema - The schema from which to obtain the constant value
 * @returns - The constant value for the schema
 * @throws - Error when the schema does not have a constant value
 */
function toConstant(schema) {
    if (ENUM_KEY in schema && Array.isArray(schema.enum) && schema.enum.length === 1) {
        return schema.enum[0];
    }
    if (CONST_KEY in schema) {
        return schema.const;
    }
    throw new Error('schema cannot be inferred as a constant');
}
//# sourceMappingURL=toConstant.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/optionsList.js

/** Gets the list of options from the schema. If the schema has an enum list, then those enum values are returned. The
 * labels for the options will be extracted from the non-standard, RJSF-deprecated `enumNames` if it exists, otherwise
 * the label will be the same as the `value`. If the schema has a `oneOf` or `anyOf`, then the value is the list of
 * `const` values from the schema and the label is either the `schema.title` or the value.
 *
 * @param schema - The schema from which to extract the options list
 * @returns - The list of options from the schema
 */
function optionsList(schema) {
    // enumNames was deprecated in v5 and is intentionally omitted from the RJSFSchema type.
    // Cast the type to include enumNames so the feature still works.
    const schemaWithEnumNames = schema;
    if (schemaWithEnumNames.enumNames && "production" !== 'production') {}
    if (schema.enum) {
        return schema.enum.map((value, i) => {
            const label = (schemaWithEnumNames.enumNames && schemaWithEnumNames.enumNames[i]) || String(value);
            return { label, value };
        });
    }
    const altSchemas = schema.oneOf || schema.anyOf;
    return (altSchemas &&
        altSchemas.map((aSchemaDef) => {
            const aSchema = aSchemaDef;
            const value = toConstant(aSchema);
            const label = aSchema.title || String(value);
            return {
                schema: aSchema,
                label,
                value,
            };
        }));
}
//# sourceMappingURL=optionsList.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/orderProperties.js
/** Given a list of `properties` and an `order` list, returns a list that contains the `properties` ordered correctly.
 * If `order` is not an array, then the untouched `properties` list is returned. Otherwise `properties` is ordered per
 * the `order` list. If `order` contains a '*' then any `properties` that are not mentioned explicity in `order` will be
 * places in the location of the `*`.
 *
 * @param properties - The list of property keys to be ordered
 * @param order - An array of property keys to be ordered first, with an optional '*' property
 * @returns - A list with the `properties` ordered
 * @throws - Error when the properties cannot be ordered correctly
 */
function orderProperties(properties, order) {
    if (!Array.isArray(order)) {
        return properties;
    }
    const arrayToHash = (arr) => arr.reduce((prev, curr) => {
        prev[curr] = true;
        return prev;
    }, {});
    const errorPropList = (arr) => arr.length > 1 ? `properties '${arr.join("', '")}'` : `property '${arr[0]}'`;
    const propertyHash = arrayToHash(properties);
    const orderFiltered = order.filter((prop) => prop === '*' || propertyHash[prop]);
    const orderHash = arrayToHash(orderFiltered);
    const rest = properties.filter((prop) => !orderHash[prop]);
    const restIndex = orderFiltered.indexOf('*');
    if (restIndex === -1) {
        if (rest.length) {
            throw new Error(`uiSchema order list does not contain ${errorPropList(rest)}`);
        }
        return orderFiltered;
    }
    if (restIndex !== orderFiltered.lastIndexOf('*')) {
        throw new Error('uiSchema order list contains more than one wildcard item');
    }
    const complete = [...orderFiltered];
    complete.splice(restIndex, 1, ...rest);
    return complete;
}
//# sourceMappingURL=orderProperties.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/pad.js
/** Returns a string representation of the `num` that is padded with leading "0"s if necessary
 *
 * @param num - The number to pad
 * @param width - The width of the string at which no lead padding is necessary
 * @returns - The number converted to a string with leading zero padding if the number of digits is less than `width`
 */
function pad(num, width) {
    let s = String(num);
    while (s.length < width) {
        s = '0' + s;
    }
    return s;
}
//# sourceMappingURL=pad.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/parseDateString.js
/** Parses the `dateString` into a `DateObject`, including the time information when `includeTime` is true
 *
 * @param dateString - The date string to parse into a DateObject
 * @param [includeTime=true] - Optional flag, if false, will not include the time data into the object
 * @returns - The date string converted to a `DateObject`
 * @throws - Error when the date cannot be parsed from the string
 */
function parseDateString(dateString, includeTime = true) {
    if (!dateString) {
        return {
            year: -1,
            month: -1,
            day: -1,
            hour: includeTime ? -1 : 0,
            minute: includeTime ? -1 : 0,
            second: includeTime ? -1 : 0,
        };
    }
    const date = new Date(dateString);
    if (Number.isNaN(date.getTime())) {
        throw new Error('Unable to parse date ' + dateString);
    }
    return {
        year: date.getUTCFullYear(),
        month: date.getUTCMonth() + 1,
        day: date.getUTCDate(),
        hour: includeTime ? date.getUTCHours() : 0,
        minute: includeTime ? date.getUTCMinutes() : 0,
        second: includeTime ? date.getUTCSeconds() : 0,
    };
}
//# sourceMappingURL=parseDateString.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/schemaRequiresTrueValue.js
/** Check to see if a `schema` specifies that a value must be true. This happens when:
 * - `schema.const` is truthy
 * - `schema.enum` == `[true]`
 * - `schema.anyOf` or `schema.oneOf` has a single value which recursively returns true
 * - `schema.allOf` has at least one value which recursively returns true
 *
 * @param schema - The schema to check
 * @returns - True if the schema specifies a value that must be true, false otherwise
 */
function schemaRequiresTrueValue(schema) {
    // Check if const is a truthy value
    if (schema.const) {
        return true;
    }
    // Check if an enum has a single value of true
    if (schema.enum && schema.enum.length === 1 && schema.enum[0] === true) {
        return true;
    }
    // If anyOf has a single value, evaluate the subschema
    if (schema.anyOf && schema.anyOf.length === 1) {
        return schemaRequiresTrueValue(schema.anyOf[0]);
    }
    // If oneOf has a single value, evaluate the subschema
    if (schema.oneOf && schema.oneOf.length === 1) {
        return schemaRequiresTrueValue(schema.oneOf[0]);
    }
    // Evaluate each subschema in allOf, to see if one of them requires a true value
    if (schema.allOf) {
        const schemaSome = (subSchema) => schemaRequiresTrueValue(subSchema);
        return schema.allOf.some(schemaSome);
    }
    return false;
}
//# sourceMappingURL=schemaRequiresTrueValue.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/shouldRender.js

/** Determines whether the given `component` should be rerendered by comparing its current set of props and state
 * against the next set. If either of those two sets are not the same, then the component should be rerendered.
 *
 * @param component - A React component being checked
 * @param nextProps - The next set of props against which to check
 * @param nextState - The next set of state against which to check
 * @returns - True if the component should be re-rendered, false otherwise
 */
function shouldRender(component, nextProps, nextState) {
    const { props, state } = component;
    return !deepEquals(props, nextProps) || !deepEquals(state, nextState);
}
//# sourceMappingURL=shouldRender.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/toDateString.js
/** Returns a UTC date string for the given `dateObject`. If `time` is false, then the time portion of the string is
 * removed.
 *
 * @param dateObject - The `DateObject` to convert to a date string
 * @param [time=true] - Optional flag used to remove the time portion of the date string if false
 * @returns - The UTC date string
 */
function toDateString(dateObject, time = true) {
    const { year, month, day, hour = 0, minute = 0, second = 0 } = dateObject;
    const utcTime = Date.UTC(year, month - 1, day, hour, minute, second);
    const datetime = new Date(utcTime).toJSON();
    return time ? datetime : datetime.slice(0, 10);
}
//# sourceMappingURL=toDateString.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/toErrorList.js


/** Converts an `errorSchema` into a list of `RJSFValidationErrors`
 *
 * @param errorSchema - The `ErrorSchema` instance to convert
 * @param [fieldPath=[]] - The current field path, defaults to [] if not specified
 * @returns - The list of `RJSFValidationErrors` extracted from the `errorSchema`
 */
function toErrorList(errorSchema, fieldPath = []) {
    if (!errorSchema) {
        return [];
    }
    let errorList = [];
    if (ERRORS_KEY in errorSchema) {
        errorList = errorList.concat(errorSchema[ERRORS_KEY].map((message) => {
            const property = `.${fieldPath.join('.')}`;
            return {
                property,
                message,
                stack: `${property} ${message}`,
            };
        }));
    }
    return Object.keys(errorSchema).reduce((acc, key) => {
        if (key !== ERRORS_KEY) {
            const childSchema = errorSchema[key];
            if (isPlainObject_default()(childSchema)) {
                acc = acc.concat(toErrorList(childSchema, [...fieldPath, key]));
            }
        }
        return acc;
    }, errorList);
}
//# sourceMappingURL=toErrorList.js.map
// EXTERNAL MODULE: ../node_modules/lodash/toPath.js
var toPath = __webpack_require__(40110);
var toPath_default = /*#__PURE__*/__webpack_require__.n(toPath);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/toErrorSchema.js


/** Transforms a rjsf validation errors list:
 * [
 *   {property: '.level1.level2[2].level3', message: 'err a'},
 *   {property: '.level1.level2[2].level3', message: 'err b'},
 *   {property: '.level1.level2[4].level3', message: 'err b'},
 * ]
 * Into an error tree:
 * {
 *   level1: {
 *     level2: {
 *       2: {level3: {errors: ['err a', 'err b']}},
 *       4: {level3: {errors: ['err b']}},
 *     }
 *   }
 * };
 *
 * @param errors - The list of RJSFValidationError objects
 * @returns - The `ErrorSchema` built from the list of `RJSFValidationErrors`
 */
function toErrorSchema(errors) {
    const builder = new ErrorSchemaBuilder();
    if (errors.length) {
        errors.forEach((error) => {
            const { property, message } = error;
            // When the property is the root element, just use an empty array for the path
            const path = property === '.' ? [] : toPath_default()(property);
            // If the property is at the root (.level1) then toPath creates
            // an empty array element at the first index. Remove it.
            if (path.length > 0 && path[0] === '') {
                path.splice(0, 1);
            }
            if (message) {
                builder.addErrors(message, path);
            }
        });
    }
    return builder.ErrorSchema;
}
//# sourceMappingURL=toErrorSchema.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/unwrapErrorHandler.js

/** Unwraps the `errorHandler` structure into the associated `ErrorSchema`, stripping the `addError()` functions from it
 *
 * @param errorHandler - The `FormValidation` error handling structure
 * @returns - The `ErrorSchema` resulting from the stripping of the `addError()` function
 */
function unwrapErrorHandler(errorHandler) {
    return Object.keys(errorHandler).reduce((acc, key) => {
        if (key === 'addError') {
            return acc;
        }
        else {
            const childSchema = errorHandler[key];
            if (isPlainObject_default()(childSchema)) {
                return {
                    ...acc,
                    [key]: unwrapErrorHandler(childSchema),
                };
            }
            return { ...acc, [key]: childSchema };
        }
    }, {});
}
//# sourceMappingURL=unwrapErrorHandler.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/utcToLocal.js

/** Converts a UTC date string into a local Date format
 *
 * @param jsonDate - A UTC date string
 * @returns - An empty string when `jsonDate` is falsey, otherwise a date string in local format
 */
function utcToLocal(jsonDate) {
    if (!jsonDate) {
        return '';
    }
    // required format of `'yyyy-MM-ddThh:mm' followed by optional ':ss' or ':ss.SSS'
    // https://html.spec.whatwg.org/multipage/input.html#local-date-and-time-state-(type%3Ddatetime-local)
    // > should be a _valid local date and time string_ (not GMT)
    // Note - date constructor passed local ISO-8601 does not correctly
    // change time to UTC in node pre-8
    const date = new Date(jsonDate);
    const yyyy = pad(date.getFullYear(), 4);
    const MM = pad(date.getMonth() + 1, 2);
    const dd = pad(date.getDate(), 2);
    const hh = pad(date.getHours(), 2);
    const mm = pad(date.getMinutes(), 2);
    const ss = pad(date.getSeconds(), 2);
    const SSS = pad(date.getMilliseconds(), 3);
    return `${yyyy}-${MM}-${dd}T${hh}:${mm}:${ss}.${SSS}`;
}
//# sourceMappingURL=utcToLocal.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/validationDataMerge.js



/** Merges the errors in `additionalErrorSchema` into the existing `validationData` by combining the hierarchies in the
 * two `ErrorSchema`s and then appending the error list from the `additionalErrorSchema` obtained by calling
 * `toErrorList()` on the `errors` in the `validationData`. If no `additionalErrorSchema` is passed, then
 * `validationData` is returned.
 *
 * @param validationData - The current `ValidationData` into which to merge the additional errors
 * @param [additionalErrorSchema] - The optional additional set of errors in an `ErrorSchema`
 * @returns - The `validationData` with the additional errors from `additionalErrorSchema` merged into it, if provided.
 */
function validationDataMerge(validationData, additionalErrorSchema) {
    if (!additionalErrorSchema) {
        return validationData;
    }
    const { errors: oldErrors, errorSchema: oldErrorSchema } = validationData;
    let errors = toErrorList(additionalErrorSchema);
    let errorSchema = additionalErrorSchema;
    if (!isEmpty_default()(oldErrorSchema)) {
        errorSchema = mergeObjects(oldErrorSchema, additionalErrorSchema, true);
        errors = [...oldErrors].concat(errors);
    }
    return { errorSchema, errors };
}
//# sourceMappingURL=validationDataMerge.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/withIdRefPrefix.js


/** Takes a `node` object and transforms any contained `$ref` node variables with a prefix, recursively calling
 * `withIdRefPrefix` for any other elements.
 *
 * @param node - The object node to which a ROOT_SCHEMA_PREFIX is added when a REF_KEY is part of it
 */
function withIdRefPrefixObject(node) {
    for (const key in node) {
        const realObj = node;
        const value = realObj[key];
        if (key === REF_KEY && typeof value === 'string' && value.startsWith('#')) {
            realObj[key] = ROOT_SCHEMA_PREFIX + value;
        }
        else {
            realObj[key] = withIdRefPrefix(value);
        }
    }
    return node;
}
/** Takes a `node` object list and transforms any contained `$ref` node variables with a prefix, recursively calling
 * `withIdRefPrefix` for any other elements.
 *
 * @param node - The list of object nodes to which a ROOT_SCHEMA_PREFIX is added when a REF_KEY is part of it
 */
function withIdRefPrefixArray(node) {
    for (let i = 0; i < node.length; i++) {
        node[i] = withIdRefPrefix(node[i]);
    }
    return node;
}
/** Recursively prefixes all `$ref`s in a schema with the value of the `ROOT_SCHEMA_PREFIX` constant.
 * This is used in isValid to make references to the rootSchema
 *
 * @param schemaNode - The object node to which a ROOT_SCHEMA_PREFIX is added when a REF_KEY is part of it
 * @returns - A copy of the `schemaNode` with updated `$ref`s
 */
function withIdRefPrefix(schemaNode) {
    if (Array.isArray(schemaNode)) {
        return withIdRefPrefixArray([...schemaNode]);
    }
    if (isObject_default()(schemaNode)) {
        return withIdRefPrefixObject({ ...schemaNode });
    }
    return schemaNode;
}
//# sourceMappingURL=withIdRefPrefix.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/enums.js
/** An enumeration of all the translatable strings used by `@rjsf/core` and its themes. The value of each of the
 * enumeration keys is expected to be the actual english string. Some strings contain replaceable parameter values
 * as indicated by `%1`, `%2`, etc. The number after the `%` indicates the order of the parameter. The ordering of
 * parameters is important because some languages may choose to put the second parameter before the first in its
 * translation. Also, some strings are rendered using `markdown-to-jsx` and thus support markdown and inline html.
 */
var TranslatableString;
(function (TranslatableString) {
    /** Fallback title of an array item, used by ArrayField */
    TranslatableString["ArrayItemTitle"] = "Item";
    /** Missing items reason, used by ArrayField */
    TranslatableString["MissingItems"] = "Missing items definition";
    /** Yes label, used by BooleanField */
    TranslatableString["YesLabel"] = "Yes";
    /** No label, used by BooleanField */
    TranslatableString["NoLabel"] = "No";
    /** Close label, used by ErrorList */
    TranslatableString["CloseLabel"] = "Close";
    /** Errors label, used by ErrorList */
    TranslatableString["ErrorsLabel"] = "Errors";
    /** New additionalProperties string default value, used by ObjectField */
    TranslatableString["NewStringDefault"] = "New Value";
    /** Add button title, used by AddButton */
    TranslatableString["AddButton"] = "Add";
    /** Add button title, used by AddButton */
    TranslatableString["AddItemButton"] = "Add Item";
    /** Copy button title, used by IconButton */
    TranslatableString["CopyButton"] = "Copy";
    /** Move down button title, used by IconButton */
    TranslatableString["MoveDownButton"] = "Move down";
    /** Move up button title, used by IconButton */
    TranslatableString["MoveUpButton"] = "Move up";
    /** Remove button title, used by IconButton */
    TranslatableString["RemoveButton"] = "Remove";
    /** Now label, used by AltDateWidget */
    TranslatableString["NowLabel"] = "Now";
    /** Clear label, used by AltDateWidget */
    TranslatableString["ClearLabel"] = "Clear";
    /** Aria date label, used by DateWidget */
    TranslatableString["AriaDateLabel"] = "Select a date";
    /** File preview label, used by FileWidget */
    TranslatableString["PreviewLabel"] = "Preview";
    /** Decrement button aria label, used by UpDownWidget */
    TranslatableString["DecrementAriaLabel"] = "Decrease value by 1";
    /** Increment button aria label, used by UpDownWidget */
    TranslatableString["IncrementAriaLabel"] = "Increase value by 1";
    // Strings with replaceable parameters
    /** Unknown field type reason, where %1 will be replaced with the type as provided by SchemaField */
    TranslatableString["UnknownFieldType"] = "Unknown field type %1";
    /** Option prefix, where %1 will be replaced with the option index as provided by MultiSchemaField */
    TranslatableString["OptionPrefix"] = "Option %1";
    /** Option prefix, where %1 and %2 will be replaced by the schema title and option index, respectively as provided by
     * MultiSchemaField
     */
    TranslatableString["TitleOptionPrefix"] = "%1 option %2";
    /** Key label, where %1 will be replaced by the label as provided by WrapIfAdditionalTemplate */
    TranslatableString["KeyLabel"] = "%1 Key";
    // Strings with replaceable parameters AND/OR that support markdown and html
    /** Invalid object field configuration as provided by the ObjectField */
    TranslatableString["InvalidObjectField"] = "Invalid \"%1\" object field configuration: <em>%2</em>.";
    /** Unsupported field schema, used by UnsupportedField */
    TranslatableString["UnsupportedField"] = "Unsupported field schema.";
    /** Unsupported field schema, where %1 will be replaced by the idSchema.$id as provided by UnsupportedField */
    TranslatableString["UnsupportedFieldWithId"] = "Unsupported field schema for field <code>%1</code>.";
    /** Unsupported field schema, where %1 will be replaced by the reason string as provided by UnsupportedField */
    TranslatableString["UnsupportedFieldWithReason"] = "Unsupported field schema: <em>%1</em>.";
    /** Unsupported field schema, where %1 and %2 will be replaced by the idSchema.$id and reason strings, respectively,
     * as provided by UnsupportedField
     */
    TranslatableString["UnsupportedFieldWithIdAndReason"] = "Unsupported field schema for field <code>%1</code>: <em>%2</em>.";
    /** File name, type and size info, where %1, %2 and %3 will be replaced by the file name, file type and file size as
     * provided by FileWidget
     */
    TranslatableString["FilesInfo"] = "<strong>%1</strong> (%2, %3 bytes)";
})(TranslatableString || (TranslatableString = {}));
//# sourceMappingURL=enums.js.map
// EXTERNAL MODULE: ../node_modules/lodash/forEach.js
var forEach = __webpack_require__(47003);
var forEach_default = /*#__PURE__*/__webpack_require__.n(forEach);
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/parser/ParserValidator.js




/** An implementation of the `ValidatorType` interface that is designed for use in capturing schemas used by the
 * `isValid()` function. The rest of the implementation of the interface throws errors when it is attempted to be used.
 * An instance of the object allows the caller to capture the schemas used in calls to the `isValid()` function. These
 * captured schema, along with the root schema used to construct the object are stored in the map of schemas keyed by
 * the hashed value of the schema. NOTE: After hashing the schema, an $id with the hash value is added to the
 * schema IF that schema doesn't already have an $id, prior to putting the schema into the map.
 */
class ParserValidator {
    /** Construct the ParserValidator for the given `rootSchema`. This `rootSchema` will be stashed in the `schemaMap`
     * first.
     *
     * @param rootSchema - The root schema against which this validator will be executed
     */
    constructor(rootSchema) {
        /** The map of schemas encountered by the ParserValidator */
        this.schemaMap = {};
        this.rootSchema = rootSchema;
        this.addSchema(rootSchema, hashForSchema(rootSchema));
    }
    /** Adds the given `schema` to the `schemaMap` keyed by the `hash` or `ID_KEY` if present on the `schema`. If the
     * schema does not have an `ID_KEY`, then the `hash` will be added as the `ID_KEY` to allow the schema to be
     * associated with it's `hash` for future use (by a schema compiler).
     *
     * @param schema - The schema which is to be added to the map
     * @param hash - The hash value at which to map the schema
     */
    addSchema(schema, hash) {
        const key = get_default()(schema, ID_KEY, hash);
        const identifiedSchema = { ...schema, [ID_KEY]: key };
        const existing = this.schemaMap[key];
        if (!existing) {
            this.schemaMap[key] = identifiedSchema;
        }
        else if (!isEqual_default()(existing, identifiedSchema)) {
            console.error('existing schema:', JSON.stringify(existing, null, 2));
            console.error('new schema:', JSON.stringify(identifiedSchema, null, 2));
            throw new Error(`Two different schemas exist with the same key ${key}! What a bad coincidence. If possible, try adding an $id to one of the schemas`);
        }
    }
    /** Returns the current `schemaMap` to the caller
     */
    getSchemaMap() {
        return this.schemaMap;
    }
    /** Implements the `ValidatorType` `isValid()` method to capture the `schema` in the `schemaMap`. Throws an error when
     * the `rootSchema` is not the same as the root schema provided during construction.
     *
     * @param schema - The schema to record in the `schemaMap`
     * @param _formData - The formData parameter that is ignored
     * @param rootSchema - The root schema associated with the schema
     * @throws - Error when the given `rootSchema` differs from the root schema provided during construction
     */
    isValid(schema, _formData, rootSchema) {
        if (!isEqual_default()(rootSchema, this.rootSchema)) {
            throw new Error('Unexpectedly calling isValid() with a rootSchema that differs from the construction rootSchema');
        }
        this.addSchema(schema, hashForSchema(schema));
        return false;
    }
    /** Implements the `ValidatorType` `rawValidation()` method to throw an error since it is never supposed to be called
     *
     * @param _schema - The schema parameter that is ignored
     * @param _formData - The formData parameter that is ignored
     */
    rawValidation(_schema, _formData) {
        throw new Error('Unexpectedly calling the `rawValidation()` method during schema parsing');
    }
    /** Implements the `ValidatorType` `toErrorList()` method to throw an error since it is never supposed to be called
     *
     * @param _errorSchema - The error schema parameter that is ignored
     * @param _fieldPath - The field path parameter that is ignored
     */
    toErrorList(_errorSchema, _fieldPath) {
        throw new Error('Unexpectedly calling the `toErrorList()` method during schema parsing');
    }
    /** Implements the `ValidatorType` `validateFormData()` method to throw an error since it is never supposed to be
     * called
     *
     * @param _formData - The formData parameter that is ignored
     * @param _schema - The schema parameter that is ignored
     * @param _customValidate - The customValidate parameter that is ignored
     * @param _transformErrors - The transformErrors parameter that is ignored
     * @param _uiSchema - The uiSchema parameter that is ignored
     */
    validateFormData(_formData, _schema, _customValidate, _transformErrors, _uiSchema) {
        throw new Error('Unexpectedly calling the `validateFormData()` method during schema parsing');
    }
}
//# sourceMappingURL=ParserValidator.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/parser/schemaParser.js





/** Recursive function used to parse the given `schema` belonging to the `rootSchema`. The `validator` is used to
 * capture the sub-schemas that the `isValid()` function is called with. For each schema returned by the
 * `retrieveSchemaInternal()`, the `resolveAnyOrOneOfSchemas()` function is called. For each of the schemas returned
 * from THAT call have `properties`, then each of the sub-schema property objects are then recursively parsed.
 *
 * @param validator - The `ParserValidator` implementation used to capture `isValid()` calls during parsing
 * @param recurseList - The list of schemas returned from the `retrieveSchemaInternal`, preventing infinite recursion
 * @param rootSchema - The root schema from which the schema parsing began
 * @param schema - The current schema element being parsed
 */
function parseSchema(validator, recurseList, rootSchema, schema) {
    const schemas = retrieveSchemaInternal(validator, schema, rootSchema, undefined, true);
    schemas.forEach((schema) => {
        const sameSchemaIndex = recurseList.findIndex((item) => isEqual_default()(item, schema));
        if (sameSchemaIndex === -1) {
            recurseList.push(schema);
            const allOptions = resolveAnyOrOneOfSchemas(validator, schema, rootSchema, true);
            allOptions.forEach((s) => {
                if (PROPERTIES_KEY in s && s[PROPERTIES_KEY]) {
                    forEach_default()(schema[PROPERTIES_KEY], (value) => {
                        parseSchema(validator, recurseList, rootSchema, value);
                    });
                }
            });
            if (ITEMS_KEY in schema && !Array.isArray(schema.items) && typeof schema.items !== 'boolean') {
                parseSchema(validator, recurseList, rootSchema, schema.items);
            }
        }
    });
}
/** Parses the given `rootSchema` to extract out all the sub-schemas that maybe contained within it. Returns a map of
 * the hash of the schema to schema/sub-schema.
 *
 * @param rootSchema - The root schema to parse for sub-schemas used by `isValid()` calls
 * @returns - The `SchemaMap` of all schemas that were parsed
 */
function schemaParser(rootSchema) {
    const validator = new ParserValidator(rootSchema);
    const recurseList = [];
    parseSchema(validator, recurseList, rootSchema, rootSchema);
    return validator.getSchemaMap();
}
//# sourceMappingURL=schemaParser.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/parser/index.js


//# sourceMappingURL=index.js.map
;// CONCATENATED MODULE: ../node_modules/@rjsf/utils/lib/index.js



























































//# sourceMappingURL=index.js.map

/***/ }),

/***/ 15422:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";


// MODULES //

var isArray = __webpack_require__( 17604 ),
	isIntegerArray = __webpack_require__( 9333 ),
	isFunction = __webpack_require__( 86401 );


// VARIABLES //

var MAXINT = Math.pow( 2, 31 ) - 1;


// FUNCTIONS //

/**
* FUNCTION: gcd( a, b )
*	Computes the greatest common divisor of two integers `a` and `b`, using the binary GCD algorithm.
*
* @param {Number} a - integer
* @param {Number} b - integer
* @returns {Number} greatest common divisor
*/
function gcd( a, b ) {
	var k = 1,
		t;
	// Simple cases:
	if ( a === 0 ) {
		return b;
	}
	if ( b === 0 ) {
		return a;
	}
	// Reduce `a` and/or `b` to odd numbers and keep track of the greatest power of 2 dividing both `a` and `b`...
	while ( a%2 === 0 && b%2 === 0 ) {
		a = a / 2; // right shift
		b = b / 2; // right shift
		k = k * 2; // left shift
	}
	// Reduce `a` to an odd number...
	while ( a%2 === 0 ) {
		a = a / 2; // right shift
	}
	// Henceforth, `a` is always odd...
	while ( b ) {
		// Remove all factors of 2 in `b`, as they are not common...
		while ( b%2 === 0 ) {
			b = b / 2; // right shift
		}
		// `a` and `b` are both odd. Swap values such that `b` is the larger of the two values, and then set `b` to the difference (which is even)...
		if ( a > b ) {
			t = b;
			b = a;
			a = t;
		}
		b = b - a; // b=0 iff b=a
	}
	// Restore common factors of 2...
	return k * a;
} // end FUNCTION gcd()

/**
* FUNCTION: bitwise( a, b )
*	Computes the greatest common divisor of two integers `a` and `b`, using the binary GCD algorithm and bitwise operations.
*
* @param {Number} a - safe integer
* @param {Number} b - safe integer
* @returns {Number} greatest common divisor
*/
function bitwise( a, b ) {
	var k = 0,
		t;
	// Simple cases:
	if ( a === 0 ) {
		return b;
	}
	if ( b === 0 ) {
		return a;
	}
	// Reduce `a` and/or `b` to odd numbers and keep track of the greatest power of 2 dividing both `a` and `b`...
	while ( (a & 1) === 0 && (b & 1) === 0 ) {
		a >>>= 1; // right shift
		b >>>= 1; // right shift
		k++;
	}
	// Reduce `a` to an odd number...
	while ( (a & 1) === 0 ) {
		a >>>= 1; // right shift
	}
	// Henceforth, `a` is always odd...
	while ( b ) {
		// Remove all factors of 2 in `b`, as they are not common...
		while ( (b & 1) === 0 ) {
			b >>>= 1; // right shift
		}
		// `a` and `b` are both odd. Swap values such that `b` is the larger of the two values, and then set `b` to the difference (which is even)...
		if ( a > b ) {
			t = b;
			b = a;
			a = t;
		}
		b = b - a; // b=0 iff b=a
	}
	// Restore common factors of 2...
	return a << k;
} // end FUNCTION bitwise()


// GREATEST COMMON DIVISOR //

/**
* FUNCTION: compute( arr[, clbk] )
*	Computes the greatest common divisor.
*
* @param {Number[]|Number} arr - input array of integers
* @param {Function|Number} [clbk] - accessor function for accessing array values
* @returns {Number|Null} greatest common divisor or null
*/
function compute() {
	var nargs = arguments.length,
		args,
		clbk,
		arr,
		len,
		a, b,
		i;

	// Copy the input arguments to an array...
	args = new Array( nargs );
	for ( i = 0; i < nargs; i++ ) {
		args[ i ] = arguments[ i ];
	}
	// Have we been provided with integer arguments?
	if ( isIntegerArray( args ) ) {
		if ( nargs === 2 ) {
			a = args[ 0 ];
			b = args[ 1 ];
			if ( a < 0 ) {
				a = -a;
			}
			if ( b < 0 ) {
				b = -b;
			}
			if ( a <= MAXINT && b <= MAXINT ) {
				return bitwise( a, b );
			} else {
				return gcd( a, b );
			}
		}
		arr = args;
	}
	// If not integers, ensure the first argument is an array...
	else if ( !isArray( args[ 0 ] ) ) {
		throw new TypeError( 'gcd()::invalid input argument. Must provide an array of integers. Value: `' + args[ 0 ] + '`.' );
	}
	// Have we been provided with more than one argument? If so, ensure that the accessor argument is a function...
	else if ( nargs > 1 ) {
		arr = args[ 0 ];
		clbk = args[ 1 ];
		if ( !isFunction( clbk ) ) {
			throw new TypeError( 'gcd()::invalid input argument. Accessor must be a function. Value: `' + clbk + '`.' );
		}
	}
	// We have been provided an array...
	else {
		arr = args[ 0 ];
	}
	len = arr.length;

	// Check if a sufficient number of values have been provided...
	if ( len < 2 ) {
		return null;
	}
	// If an accessor is provided, extract the array values...
	if ( clbk ) {
		a = new Array( len );
		for ( i = 0; i < len; i++ ) {
			a[ i ] = clbk( arr[ i ], i );
		}
		arr = a;
	}
	// Given an input array, ensure all array values are integers...
	if ( nargs < 3 ) {
		if ( !isIntegerArray( arr ) ) {
			throw new TypeError( 'gcd()::invalid input argument. Accessed array values must be integers. Value: `' + arr + '`.' );
		}
	}
	// Convert any negative integers to positive integers...
	for ( i = 0; i < len; i++ ) {
		a = arr[ i ];
		if ( a < 0 ) {
			arr[ i ] = -a;
		}
	}
	// Exploit the fact that the gcd is an associative function...
	a = arr[ 0 ];
	for ( i = 1; i < len; i++ ) {
		b = arr[ i ];
		if ( b <= MAXINT && a <= MAXINT ) {
			a = bitwise( a, b );
		} else {
			a = gcd( a, b );
		}
	}
	return a;
} // end FUNCTION compute()


// EXPORTS //

module.exports = compute;


/***/ }),

/***/ 72982:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";


// MODULES //

var gcd = __webpack_require__( 15422 ),
	isArray = __webpack_require__( 17604 ),
	isIntegerArray = __webpack_require__( 9333 ),
	isFunction = __webpack_require__( 86401 );


// LEAST COMMON MULTIPLE //

/**
* FUNCTION: lcm( arr[, clbk] )
*	Computes the least common multiple (lcm).
*
* @param {Number[]|Number} arr - input array of integers
* @param {Function|Number} [accessor] - accessor function for accessing array values
* @returns {Number|Null} least common multiple or null
*/
function lcm() {
	var nargs = arguments.length,
		args,
		clbk,
		arr,
		len,
		a, b,
		i;

	// Copy the input arguments to an array...
	args = new Array( nargs );
	for ( i = 0; i < nargs; i++ ) {
		args[ i ] = arguments[ i ];
	}
	// Have we been provided with integer arguments?
	if ( isIntegerArray( args ) ) {
		if ( nargs === 2 ) {
			a = args[ 0 ];
			b = args[ 1 ];
			if ( a < 0 ) {
				a = -a;
			}
			if ( b < 0 ) {
				b = -b;
			}
			if ( a === 0 || b === 0 ) {
				return 0;
			}
			return ( a/gcd(a,b) ) * b;
		}
		arr = args;
	}
	// If not integers, ensure that the first argument is an array...
	else if ( !isArray( args[ 0 ] ) ) {
		throw new TypeError( 'lcm()::invalid input argument. Must provide an array of integers. Value: `' + args[ 0 ] + '`.' );
	}
	// Have we been provided with more than one argument? If so, ensure that the accessor argument is a function...
	else if ( nargs > 1 ) {
		arr = args[ 0 ];
		clbk = args[ 1 ];
		if ( !isFunction( clbk ) ) {
			throw new TypeError( 'lcm()::invalid input argument. Accessor must be a function. Value: `' + clbk + '`.' );
		}
	}
	// We have been provided an array...
	else {
		arr = args[ 0 ];
	}
	len = arr.length;

	// Check if a sufficient number of values have been provided...
	if ( len < 2 ) {
		return null;
	}
	// If an accessor is provided, extract the array values...
	if ( clbk ) {
		a = new Array( len );
		for ( i = 0; i < len; i++ ) {
			a[ i ] = clbk( arr[ i ], i );
		}
		arr = a;
	}
	// Given an input array, ensure all array values are integers...
	if ( nargs < 3 ) {
		if ( !isIntegerArray( arr ) ) {
			throw new TypeError( 'lcm()::invalid input argument. Accessed array values must be integers. Value: `' + arr + '`.' );
		}
	}
	// Convert any negative integers to positive integers...
	for ( i = 0; i < len; i++ ) {
		a = arr[ i ];
		if ( a < 0 ) {
			arr[ i ] = -a;
		}
	}
	// Exploit the fact that the lcm is an associative function...
	a = arr[ 0 ];
	for ( i = 1; i < len; i++ ) {
		b = arr[ i ];
		if ( a === 0 || b === 0 ) {
			return 0;
		}
		a = ( a/gcd(a,b) ) * b;
	}
	return a;
} // end FUNCTION lcm()


// EXPORTS //

module.exports = lcm;


/***/ }),

/***/ 64316:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var isEqual = __webpack_require__(56141)
var sortBy = __webpack_require__(95099)
var uniq = __webpack_require__(83766)
var uniqWith = __webpack_require__(52929)
var defaults = __webpack_require__(70236)
var intersectionWith = __webpack_require__(28054)
var isPlainObject = __webpack_require__(40861)
var isBoolean = __webpack_require__(3819)

var normalizeArray = val => Array.isArray(val)
  ? val : [val]
var undef = val => val === undefined
var keys = obj => isPlainObject(obj) || Array.isArray(obj) ? Object.keys(obj) : []
var has = (obj, key) => obj.hasOwnProperty(key)
var stringArray = arr => sortBy(uniq(arr))
var undefEmpty = val => undef(val) || (Array.isArray(val) && val.length === 0)
var keyValEqual = (a, b, key, compare) => b && has(b, key) && a && has(a, key) && compare(a[key], b[key])
var undefAndZero = (a, b) => (undef(a) && b === 0) || (undef(b) && a === 0) || isEqual(a, b)
var falseUndefined = (a, b) => (undef(a) && b === false) || (undef(b) && a === false) || isEqual(a, b)
var emptySchema = schema => undef(schema) || isEqual(schema, {}) || schema === true
var emptyObjUndef = schema => undef(schema) || isEqual(schema, {})
var isSchema = val => undef(val) || isPlainObject(val) || val === true || val === false

function undefArrayEqual(a, b) {
  if (undefEmpty(a) && undefEmpty(b)) {
    return true
  } else {
    return isEqual(stringArray(a), stringArray(b))
  }
}

function unsortedNormalizedArray(a, b) {
  a = normalizeArray(a)
  b = normalizeArray(b)
  return isEqual(stringArray(a), stringArray(b))
}

function schemaGroup(a, b, key, compare) {
  var allProps = uniq(keys(a).concat(keys(b)))
  if (emptyObjUndef(a) && emptyObjUndef(b)) {
    return true
  } else if (emptyObjUndef(a) && keys(b).length) {
    return false
  } else if (emptyObjUndef(b) && keys(a).length) {
    return false
  }

  return allProps.every(function(key) {
    var aVal = a[key]
    var bVal = b[key]
    if (Array.isArray(aVal) && Array.isArray(bVal)) {
      return isEqual(stringArray(a), stringArray(b))
    } else if (Array.isArray(aVal) && !Array.isArray(bVal)) {
      return false
    } else if (Array.isArray(bVal) && !Array.isArray(aVal)) {
      return false
    }
    return keyValEqual(a, b, key, compare)
  })
}

function items(a, b, key, compare) {
  if (isPlainObject(a) && isPlainObject(b)) {
    return compare(a, b)
  } else if (Array.isArray(a) && Array.isArray(b)) {
    return schemaGroup(a, b, key, compare)
  } else {
    return isEqual(a, b)
  }
}

function unsortedArray(a, b, key, compare) {
  var uniqueA = uniqWith(a, compare)
  var uniqueB = uniqWith(b, compare)
  var inter = intersectionWith(uniqueA, uniqueB, compare)
  return inter.length === Math.max(uniqueA.length, uniqueB.length)
}

var comparers = {
  title: isEqual,
  uniqueItems: falseUndefined,
  minLength: undefAndZero,
  minItems: undefAndZero,
  minProperties: undefAndZero,
  required: undefArrayEqual,
  enum: undefArrayEqual,
  type: unsortedNormalizedArray,
  items: items,
  anyOf: unsortedArray,
  allOf: unsortedArray,
  oneOf: unsortedArray,
  properties: schemaGroup,
  patternProperties: schemaGroup,
  dependencies: schemaGroup
}

var acceptsUndefined = [
  'properties',
  'patternProperties',
  'dependencies',
  'uniqueItems',
  'minLength',
  'minItems',
  'minProperties',
  'required'
]

var schemaProps = ['additionalProperties', 'additionalItems', 'contains', 'propertyNames', 'not']

function compare(a, b, options) {
  options = defaults(options, {
    ignore: []
  })

  if (emptySchema(a) && emptySchema(b)) {
    return true
  }

  if (!isSchema(a) || !isSchema(b)) {
    throw new Error('Either of the values are not a JSON schema.')
  }
  if (a === b) {
    return true
  }

  if (isBoolean(a) && isBoolean(b)) {
    return a === b
  }

  if ((a === undefined && b === false) || (b === undefined && a === false)) {
    return false
  }

  if ((undef(a) && !undef(b)) || (!undef(a) && undef(b))) {
    return false
  }

  var allKeys = uniq(Object.keys(a).concat(Object.keys(b)))

  if (options.ignore.length) {
    allKeys = allKeys.filter(k => options.ignore.indexOf(k) === -1)
  }

  if (!allKeys.length) {
    return true
  }

  function innerCompare(a, b) {
    return compare(a, b, options)
  }

  return allKeys.every(function(key) {
    var aValue = a[key]
    var bValue = b[key]

    if (schemaProps.indexOf(key) !== -1) {
      return compare(aValue, bValue, options)
    }

    var comparer = comparers[key]
    if (!comparer) {
      comparer = isEqual
    }

    // do simple lodash check first
    if (isEqual(aValue, bValue)) {
      return true
    }

    if (acceptsUndefined.indexOf(key) === -1) {
      if ((!has(a, key) && has(b, key)) || (has(a, key) && !has(b, key))) {
        return aValue === bValue
      }
    }

    var result = comparer(aValue, bValue, key, innerCompare)
    if (!isBoolean(result)) {
      throw new Error('Comparer must return true or false')
    }
    return result
  })
}

module.exports = compare


/***/ }),

/***/ 2143:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const flatten = __webpack_require__(89754)
const flattenDeep = __webpack_require__(84812)
const isPlainObject = __webpack_require__(40861)
const uniq = __webpack_require__(83766)
const uniqWith = __webpack_require__(52929)
const without = __webpack_require__(81889)

function deleteUndefinedProps(returnObject) {
  // cleanup empty
  for (const prop in returnObject) {
    if (has(returnObject, prop) && isEmptySchema(returnObject[prop])) {
      delete returnObject[prop]
    }
  }
  return returnObject
}

const allUniqueKeys = (arr) => uniq(flattenDeep(arr.map(keys)))
const getValues = (schemas, key) => schemas.map(schema => schema && schema[key])
const has = (obj, propName) => Object.prototype.hasOwnProperty.call(obj, propName)
const keys = obj => {
  if (isPlainObject(obj) || Array.isArray(obj)) {
    return Object.keys(obj)
  } else {
    return []
  }
}

const notUndefined = (val) => val !== undefined
const isSchema = (val) => isPlainObject(val) || val === true || val === false
const isEmptySchema = (obj) => (!keys(obj).length) && obj !== false && obj !== true
const withoutArr = (arr, ...rest) => without.apply(null, [arr].concat(flatten(rest)))

module.exports = {
  allUniqueKeys,
  deleteUndefinedProps,
  getValues,
  has,
  isEmptySchema,
  isSchema,
  keys,
  notUndefined,
  uniqWith,
  withoutArr
}


/***/ }),

/***/ 71020:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


const compare = __webpack_require__(64316)
const forEach = __webpack_require__(47003)
const {
  allUniqueKeys,
  deleteUndefinedProps,
  has,
  isSchema,
  notUndefined,
  uniqWith
} = __webpack_require__(2143)

function removeFalseSchemasFromArray(target) {
  forEach(target, function(schema, index) {
    if (schema === false) {
      target.splice(index, 1)
    }
  })
}

function getItemSchemas(subSchemas, key) {
  return subSchemas.map(function(sub) {
    if (!sub) {
      return undefined
    }

    if (Array.isArray(sub.items)) {
      const schemaAtPos = sub.items[key]
      if (isSchema(schemaAtPos)) {
        return schemaAtPos
      } else if (has(sub, 'additionalItems')) {
        return sub.additionalItems
      }
    } else {
      return sub.items
    }

    return undefined
  })
}

function getAdditionalSchemas(subSchemas) {
  return subSchemas.map(function(sub) {
    if (!sub) {
      return undefined
    }
    if (Array.isArray(sub.items)) {
      return sub.additionalItems
    }
    return sub.items
  })
}

// Provide source when array
function mergeItems(group, mergeSchemas, items) {
  const allKeys = allUniqueKeys(items)
  return allKeys.reduce(function(all, key) {
    const schemas = getItemSchemas(group, key)
    const compacted = uniqWith(schemas.filter(notUndefined), compare)
    all[key] = mergeSchemas(compacted, key)
    return all
  }, [])
}

module.exports = {
  keywords: ['items', 'additionalItems'],
  resolver(values, parents, mergers) {
    // const createSubMerger = groupKey => (schemas, key) => mergeSchemas(schemas, parents.concat(groupKey, key))
    const items = values.map(s => s.items)
    const itemsCompacted = items.filter(notUndefined)
    const returnObject = {}

    // if all items keyword values are schemas, we can merge them as simple schemas
    // if not we need to merge them as mixed
    if (itemsCompacted.every(isSchema)) {
      returnObject.items = mergers.items(items)
    } else {
      returnObject.items = mergeItems(values, mergers.items, items)
    }

    let schemasAtLastPos
    if (itemsCompacted.every(Array.isArray)) {
      schemasAtLastPos = values.map(s => s.additionalItems)
    } else if (itemsCompacted.some(Array.isArray)) {
      schemasAtLastPos = getAdditionalSchemas(values)
    }

    if (schemasAtLastPos) {
      returnObject.additionalItems = mergers.additionalItems(schemasAtLastPos)
    }

    if (returnObject.additionalItems === false && Array.isArray(returnObject.items)) {
      removeFalseSchemasFromArray(returnObject.items)
    }

    return deleteUndefinedProps(returnObject)
  }
}


/***/ }),

/***/ 88807:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {


const compare = __webpack_require__(64316)
const forEach = __webpack_require__(47003)
const {
  allUniqueKeys,
  deleteUndefinedProps,
  getValues,
  keys,
  notUndefined,
  uniqWith,
  withoutArr
} = __webpack_require__(2143)

function removeFalseSchemas(target) {
  forEach(target, function(schema, prop) {
    if (schema === false) {
      delete target[prop]
    }
  })
}

function mergeSchemaGroup(group, mergeSchemas) {
  const allKeys = allUniqueKeys(group)
  return allKeys.reduce(function(all, key) {
    const schemas = getValues(group, key)
    const compacted = uniqWith(schemas.filter(notUndefined), compare)
    all[key] = mergeSchemas(compacted, key)
    return all
  }, {})
}

module.exports = {
  keywords: ['properties', 'patternProperties', 'additionalProperties'],
  resolver(values, parents, mergers, options) {
    // first get rid of all non permitted properties
    if (!options.ignoreAdditionalProperties) {
      values.forEach(function(subSchema) {
        const otherSubSchemas = values.filter(s => s !== subSchema)
        const ownKeys = keys(subSchema.properties)
        const ownPatternKeys = keys(subSchema.patternProperties)
        const ownPatterns = ownPatternKeys.map(k => new RegExp(k))
        otherSubSchemas.forEach(function(other) {
          const allOtherKeys = keys(other.properties)
          const keysMatchingPattern = allOtherKeys.filter(k => ownPatterns.some(pk => pk.test(k)))
          const additionalKeys = withoutArr(allOtherKeys, ownKeys, keysMatchingPattern)
          additionalKeys.forEach(function(key) {
            other.properties[key] = mergers.properties([
              other.properties[key], subSchema.additionalProperties
            ], key)
          })
        })
      })

      // remove disallowed patternProperties
      values.forEach(function(subSchema) {
        const otherSubSchemas = values.filter(s => s !== subSchema)
        const ownPatternKeys = keys(subSchema.patternProperties)
        if (subSchema.additionalProperties === false) {
          otherSubSchemas.forEach(function(other) {
            const allOtherPatterns = keys(other.patternProperties)
            const additionalPatternKeys = withoutArr(allOtherPatterns, ownPatternKeys)
            additionalPatternKeys.forEach(key => delete other.patternProperties[key])
          })
        }
      })
    }

    const returnObject = {
      additionalProperties: mergers.additionalProperties(values.map(s => s.additionalProperties)),
      patternProperties: mergeSchemaGroup(values.map(s => s.patternProperties), mergers.patternProperties),
      properties: mergeSchemaGroup(values.map(s => s.properties), mergers.properties)
    }

    if (returnObject.additionalProperties === false) {
      removeFalseSchemas(returnObject.properties)
    }

    return deleteUndefinedProps(returnObject)
  }
}


/***/ }),

/***/ 64717:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const cloneDeep = __webpack_require__(30454)
const compare = __webpack_require__(64316)
const computeLcm = __webpack_require__(72982)
const defaultsDeep = __webpack_require__(53533)
const flatten = __webpack_require__(89754)
const flattenDeep = __webpack_require__(84812)
const intersection = __webpack_require__(27921)
const intersectionWith = __webpack_require__(28054)
const isEqual = __webpack_require__(56141)
const isPlainObject = __webpack_require__(40861)
const pullAll = __webpack_require__(90254)
const sortBy = __webpack_require__(95099)
const uniq = __webpack_require__(83766)
const uniqWith = __webpack_require__(52929)

const propertiesResolver = __webpack_require__(88807)
const itemsResolver = __webpack_require__(71020)

const contains = (arr, val) => arr.indexOf(val) !== -1
const isSchema = (val) => isPlainObject(val) || val === true || val === false
const isFalse = (val) => val === false
const isTrue = (val) => val === true
const schemaResolver = (compacted, key, mergeSchemas) => mergeSchemas(compacted)
const stringArray = (values) => sortBy(uniq(flattenDeep(values)))
const notUndefined = (val) => val !== undefined
const allUniqueKeys = (arr) => uniq(flattenDeep(arr.map(keys)))

// resolvers
const first = compacted => compacted[0]
const required = compacted => stringArray(compacted)
const maximumValue = compacted => Math.max.apply(Math, compacted)
const minimumValue = compacted => Math.min.apply(Math, compacted)
const uniqueItems = compacted => compacted.some(isTrue)
const examples = compacted => uniqWith(flatten(compacted), isEqual)

function compareProp(key) {
  return function(a, b) {
    return compare({
      [key]: a
    }, { [key]: b })
  }
}

function getAllOf(schema) {
  let { allOf = [], ...copy } = schema
  copy = isPlainObject(schema) ? copy : schema // if schema is boolean
  return [copy, ...allOf.map(getAllOf)]
}

function getValues(schemas, key) {
  return schemas.map(schema => schema && schema[key])
}

function tryMergeSchemaGroups(schemaGroups, mergeSchemas) {
  return schemaGroups.map(function(schemas, index) {
    try {
      return mergeSchemas(schemas, index)
    } catch (e) {
      return undefined
    }
  }).filter(notUndefined)
}

function keys(obj) {
  if (isPlainObject(obj) || Array.isArray(obj)) {
    return Object.keys(obj)
  } else {
    return []
  }
}

function getAnyOfCombinations(arrOfArrays, combinations) {
  combinations = combinations || []
  if (!arrOfArrays.length) {
    return combinations
  }

  const values = arrOfArrays.slice(0).shift()
  const rest = arrOfArrays.slice(1)
  if (combinations.length) {
    return getAnyOfCombinations(rest, flatten(combinations.map(combination => values.map(item => ([item].concat(combination))))))
  }
  return getAnyOfCombinations(rest, values.map(item => (item)))
}

function throwIncompatible(values, paths) {
  let asJSON
  try {
    asJSON = values.map(function(val) {
      return JSON.stringify(val, null, 2)
    }).join('\n')
  } catch (variable) {
    asJSON = values.join(', ')
  }
  throw new Error('Could not resolve values for path:"' + paths.join('.') + '". They are probably incompatible. Values: \n' + asJSON)
}

function callGroupResolver(complexKeywords, resolverName, schemas, mergeSchemas, options, parents) {
  if (complexKeywords.length) {
    const resolverConfig = options.complexResolvers[resolverName]
    if (!resolverConfig || !resolverConfig.resolver) {
      throw new Error('No resolver found for ' + resolverName)
    }

    // extract all keywords from all the schemas that have one or more
    // then remove all undefined ones and not unique
    const extractedKeywordsOnly = schemas.map(schema => complexKeywords.reduce((all, key) => {
      if (schema[key] !== undefined) all[key] = schema[key]
      return all
    }, {}))
    const unique = uniqWith(extractedKeywordsOnly, compare)

    // create mergers that automatically add the path of the keyword for use in the complex resolver
    const mergers = resolverConfig.keywords.reduce((all, key) => ({
      ...all,
      [key]: (schemas, extraKey = []) => mergeSchemas(schemas, null, parents.concat(key, extraKey))
    }), {})

    const result = resolverConfig.resolver(unique, parents.concat(resolverName), mergers, options)

    if (!isPlainObject(result)) {
      throwIncompatible(unique, parents.concat(resolverName))
    }

    return result
  }
}

function createRequiredMetaArray(arr) {
  return { required: arr }
}

const schemaGroupProps = ['properties', 'patternProperties', 'definitions', 'dependencies']
const schemaArrays = ['anyOf', 'oneOf']
const schemaProps = [
  'additionalProperties',
  'additionalItems',
  'contains',
  'propertyNames',
  'not',
  'items'
]

const defaultResolvers = {
  type(compacted) {
    if (compacted.some(Array.isArray)) {
      const normalized = compacted.map(function(val) {
        return Array.isArray(val)
          ? val
          : [val]
      })
      const common = intersection.apply(null, normalized)

      if (common.length === 1) {
        return common[0]
      } else if (common.length > 1) {
        return uniq(common)
      }
    }
  },
  dependencies(compacted, paths, mergeSchemas) {
    const allChildren = allUniqueKeys(compacted)

    return allChildren.reduce(function(all, childKey) {
      const childSchemas = getValues(compacted, childKey)
      let innerCompacted = uniqWith(childSchemas.filter(notUndefined), isEqual)

      // to support dependencies
      const innerArrays = innerCompacted.filter(Array.isArray)

      if (innerArrays.length) {
        if (innerArrays.length === innerCompacted.length) {
          all[childKey] = stringArray(innerCompacted)
        } else {
          const innerSchemas = innerCompacted.filter(isSchema)
          const arrayMetaScheams = innerArrays.map(createRequiredMetaArray)
          all[childKey] = mergeSchemas(innerSchemas.concat(arrayMetaScheams), childKey)
        }
        return all
      }

      innerCompacted = uniqWith(innerCompacted, compare)

      all[childKey] = mergeSchemas(innerCompacted, childKey)
      return all
    }, {})
  },
  oneOf(compacted, paths, mergeSchemas) {
    const combinations = getAnyOfCombinations(cloneDeep(compacted))
    const result = tryMergeSchemaGroups(combinations, mergeSchemas)
    const unique = uniqWith(result, compare)

    if (unique.length) {
      return unique
    }
  },
  not(compacted) {
    return { anyOf: compacted }
  },
  pattern(compacted) {
    return compacted.map(r => '(?=' + r + ')').join('')
  },
  multipleOf(compacted) {
    let integers = compacted.slice(0)
    let factor = 1
    while (integers.some(n => !Number.isInteger(n))) {
      integers = integers.map(n => n * 10)
      factor = factor * 10
    }
    return computeLcm(integers) / factor
  },
  enum(compacted) {
    const enums = intersectionWith.apply(null, compacted.concat(isEqual))
    if (enums.length) {
      return sortBy(enums)
    }
  }
}

defaultResolvers.$id = first
defaultResolvers.$ref = first
defaultResolvers.$schema = first
defaultResolvers.additionalItems = schemaResolver
defaultResolvers.additionalProperties = schemaResolver
defaultResolvers.anyOf = defaultResolvers.oneOf
defaultResolvers.contains = schemaResolver
defaultResolvers.default = first
defaultResolvers.definitions = defaultResolvers.dependencies
defaultResolvers.description = first
defaultResolvers.examples = examples
defaultResolvers.exclusiveMaximum = minimumValue
defaultResolvers.exclusiveMinimum = maximumValue
defaultResolvers.items = itemsResolver
defaultResolvers.maximum = minimumValue
defaultResolvers.maxItems = minimumValue
defaultResolvers.maxLength = minimumValue
defaultResolvers.maxProperties = minimumValue
defaultResolvers.minimum = maximumValue
defaultResolvers.minItems = maximumValue
defaultResolvers.minLength = maximumValue
defaultResolvers.minProperties = maximumValue
defaultResolvers.properties = propertiesResolver
defaultResolvers.propertyNames = schemaResolver
defaultResolvers.required = required
defaultResolvers.title = first
defaultResolvers.uniqueItems = uniqueItems

const defaultComplexResolvers = {
  properties: propertiesResolver,
  items: itemsResolver
}

function merger(rootSchema, options, totalSchemas) {
  totalSchemas = totalSchemas || []
  options = defaultsDeep(options, {
    ignoreAdditionalProperties: false,
    resolvers: defaultResolvers,
    complexResolvers: defaultComplexResolvers,
    deep: true
  })

  const complexResolvers = Object.entries(options.complexResolvers)

  function mergeSchemas(schemas, base, parents) {
    schemas = cloneDeep(schemas.filter(notUndefined))
    parents = parents || []
    const merged = isPlainObject(base)
      ? base
      : {}

    // return undefined, an empty schema
    if (!schemas.length) {
      return
    }

    if (schemas.some(isFalse)) {
      return false
    }

    if (schemas.every(isTrue)) {
      return true
    }

    // there are no false and we don't need the true ones as they accept everything
    schemas = schemas.filter(isPlainObject)

    const allKeys = allUniqueKeys(schemas)
    if (options.deep && contains(allKeys, 'allOf')) {
      return merger({
        allOf: schemas
      }, options, totalSchemas)
    }

    const complexKeysArr = complexResolvers.map(([mainKeyWord, resolverConf]) =>
      allKeys.filter(k => resolverConf.keywords.includes(k)))

    // remove all complex keys before simple resolvers
    complexKeysArr.forEach(keys => pullAll(allKeys, keys))

    // call all simple resolvers for relevant keywords
    allKeys.forEach(function(key) {
      const values = getValues(schemas, key)
      const compacted = uniqWith(values.filter(notUndefined), compareProp(key))

      // arrayprops like anyOf and oneOf must be merged first, as they contains schemas
      // allOf is treated differently alltogether
      if (compacted.length === 1 && contains(schemaArrays, key)) {
        merged[key] = compacted[0].map(schema => mergeSchemas([schema], schema))
        // prop groups must always be resolved
      } else if (compacted.length === 1 && !contains(schemaGroupProps, key) && !contains(schemaProps, key)) {
        merged[key] = compacted[0]
      } else {
        const resolver = options.resolvers[key] || options.resolvers.defaultResolver
        if (!resolver) throw new Error('No resolver found for key ' + key + '. You can provide a resolver for this keyword in the options, or provide a default resolver.')

        const merger = (schemas, extraKey = []) => mergeSchemas(schemas, null, parents.concat(key, extraKey))
        merged[key] = resolver(compacted, parents.concat(key), merger, options)

        if (merged[key] === undefined) {
          throwIncompatible(compacted, parents.concat(key))
        } else if (merged[key] === undefined) {
          delete merged[key]
        }
      }
    })

    return complexResolvers.reduce((all, [resolverKeyword, config], index) => ({
      ...all,
      ...callGroupResolver(complexKeysArr[index], resolverKeyword, schemas, mergeSchemas, options, parents)
    }), merged)
  }

  const allSchemas = flattenDeep(getAllOf(rootSchema))
  const merged = mergeSchemas(allSchemas)

  return merged
}

merger.options = {
  resolvers: defaultResolvers
}

module.exports = merger


/***/ }),

/***/ 88208:
/***/ ((__unused_webpack_module, exports) => {

var hasExcape = /~/
var escapeMatcher = /~[01]/g
function escapeReplacer (m) {
  switch (m) {
    case '~1': return '/'
    case '~0': return '~'
  }
  throw new Error('Invalid tilde escape: ' + m)
}

function untilde (str) {
  if (!hasExcape.test(str)) return str
  return str.replace(escapeMatcher, escapeReplacer)
}

function setter (obj, pointer, value) {
  var part
  var hasNextPart

  for (var p = 1, len = pointer.length; p < len;) {
    if (pointer[p] === 'constructor' || pointer[p] === 'prototype' || pointer[p] === '__proto__') return obj

    part = untilde(pointer[p++])
    hasNextPart = len > p

    if (typeof obj[part] === 'undefined') {
      // support setting of /-
      if (Array.isArray(obj) && part === '-') {
        part = obj.length
      }

      // support nested objects/array when setting values
      if (hasNextPart) {
        if ((pointer[p] !== '' && pointer[p] < Infinity) || pointer[p] === '-') obj[part] = []
        else obj[part] = {}
      }
    }

    if (!hasNextPart) break
    obj = obj[part]
  }

  var oldValue = obj[part]
  if (value === undefined) delete obj[part]
  else obj[part] = value
  return oldValue
}

function compilePointer (pointer) {
  if (typeof pointer === 'string') {
    pointer = pointer.split('/')
    if (pointer[0] === '') return pointer
    throw new Error('Invalid JSON pointer.')
  } else if (Array.isArray(pointer)) {
    for (const part of pointer) {
      if (typeof part !== 'string' && typeof part !== 'number') {
        throw new Error('Invalid JSON pointer. Must be of type string or number.')
      }
    }
    return pointer
  }

  throw new Error('Invalid JSON pointer.')
}

function get (obj, pointer) {
  if (typeof obj !== 'object') throw new Error('Invalid input object.')
  pointer = compilePointer(pointer)
  var len = pointer.length
  if (len === 1) return obj

  for (var p = 1; p < len;) {
    obj = obj[untilde(pointer[p++])]
    if (len === p) return obj
    if (typeof obj !== 'object' || obj === null) return undefined
  }
}

function set (obj, pointer, value) {
  if (typeof obj !== 'object') throw new Error('Invalid input object.')
  pointer = compilePointer(pointer)
  if (pointer.length === 0) throw new Error('Invalid JSON pointer for set.')
  return setter(obj, pointer, value)
}

function compile (pointer) {
  var compiled = compilePointer(pointer)
  return {
    get: function (object) {
      return get(object, compiled)
    },
    set: function (object, value) {
      return set(object, compiled, value)
    }
  }
}

exports.get = get
exports.set = set
exports.compile = compile


/***/ }),

/***/ 52485:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var MapCache = __webpack_require__(74554),
    setCacheAdd = __webpack_require__(38639),
    setCacheHas = __webpack_require__(88379);

/**
 *
 * Creates an array cache object to store unique values.
 *
 * @private
 * @constructor
 * @param {Array} [values] The values to cache.
 */
function SetCache(values) {
  var index = -1,
      length = values == null ? 0 : values.length;

  this.__data__ = new MapCache;
  while (++index < length) {
    this.add(values[index]);
  }
}

// Add methods to `SetCache`.
SetCache.prototype.add = SetCache.prototype.push = setCacheAdd;
SetCache.prototype.has = setCacheHas;

module.exports = SetCache;


/***/ }),

/***/ 58095:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseIndexOf = __webpack_require__(8817);

/**
 * A specialized version of `_.includes` for arrays without support for
 * specifying an index to search from.
 *
 * @private
 * @param {Array} [array] The array to inspect.
 * @param {*} target The value to search for.
 * @returns {boolean} Returns `true` if `target` is found, else `false`.
 */
function arrayIncludes(array, value) {
  var length = array == null ? 0 : array.length;
  return !!length && baseIndexOf(array, value, 0) > -1;
}

module.exports = arrayIncludes;


/***/ }),

/***/ 21796:
/***/ ((module) => {

/**
 * This function is like `arrayIncludes` except that it accepts a comparator.
 *
 * @private
 * @param {Array} [array] The array to inspect.
 * @param {*} target The value to search for.
 * @param {Function} comparator The comparator invoked per element.
 * @returns {boolean} Returns `true` if `target` is found, else `false`.
 */
function arrayIncludesWith(array, value, comparator) {
  var index = -1,
      length = array == null ? 0 : array.length;

  while (++index < length) {
    if (comparator(value, array[index])) {
      return true;
    }
  }
  return false;
}

module.exports = arrayIncludesWith;


/***/ }),

/***/ 6446:
/***/ ((module) => {

/**
 * A specialized version of `_.reduce` for arrays without support for
 * iteratee shorthands.
 *
 * @private
 * @param {Array} [array] The array to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @param {*} [accumulator] The initial value.
 * @param {boolean} [initAccum] Specify using the first element of `array` as
 *  the initial value.
 * @returns {*} Returns the accumulated value.
 */
function arrayReduce(array, iteratee, accumulator, initAccum) {
  var index = -1,
      length = array == null ? 0 : array.length;

  if (initAccum && length) {
    accumulator = array[++index];
  }
  while (++index < length) {
    accumulator = iteratee(accumulator, array[index], index, array);
  }
  return accumulator;
}

module.exports = arrayReduce;


/***/ }),

/***/ 90756:
/***/ ((module) => {

/**
 * A specialized version of `_.some` for arrays without support for iteratee
 * shorthands.
 *
 * @private
 * @param {Array} [array] The array to iterate over.
 * @param {Function} predicate The function invoked per iteration.
 * @returns {boolean} Returns `true` if any element passes the predicate check,
 *  else `false`.
 */
function arraySome(array, predicate) {
  var index = -1,
      length = array == null ? 0 : array.length;

  while (++index < length) {
    if (predicate(array[index], index, array)) {
      return true;
    }
  }
  return false;
}

module.exports = arraySome;


/***/ }),

/***/ 73140:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseAssignValue = __webpack_require__(88799),
    eq = __webpack_require__(85638);

/**
 * This function is like `assignValue` except that it doesn't assign
 * `undefined` values.
 *
 * @private
 * @param {Object} object The object to modify.
 * @param {string} key The key of the property to assign.
 * @param {*} value The value to assign.
 */
function assignMergeValue(object, key, value) {
  if ((value !== undefined && !eq(object[key], value)) ||
      (value === undefined && !(key in object))) {
    baseAssignValue(object, key, value);
  }
}

module.exports = assignMergeValue;


/***/ }),

/***/ 11005:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var SetCache = __webpack_require__(52485),
    arrayIncludes = __webpack_require__(58095),
    arrayIncludesWith = __webpack_require__(21796),
    arrayMap = __webpack_require__(66070),
    baseUnary = __webpack_require__(39334),
    cacheHas = __webpack_require__(65581);

/** Used as the size to enable large array optimizations. */
var LARGE_ARRAY_SIZE = 200;

/**
 * The base implementation of methods like `_.difference` without support
 * for excluding multiple arrays or iteratee shorthands.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {Array} values The values to exclude.
 * @param {Function} [iteratee] The iteratee invoked per element.
 * @param {Function} [comparator] The comparator invoked per element.
 * @returns {Array} Returns the new array of filtered values.
 */
function baseDifference(array, values, iteratee, comparator) {
  var index = -1,
      includes = arrayIncludes,
      isCommon = true,
      length = array.length,
      result = [],
      valuesLength = values.length;

  if (!length) {
    return result;
  }
  if (iteratee) {
    values = arrayMap(values, baseUnary(iteratee));
  }
  if (comparator) {
    includes = arrayIncludesWith;
    isCommon = false;
  }
  else if (values.length >= LARGE_ARRAY_SIZE) {
    includes = cacheHas;
    isCommon = false;
    values = new SetCache(values);
  }
  outer:
  while (++index < length) {
    var value = array[index],
        computed = iteratee == null ? value : iteratee(value);

    value = (comparator || value !== 0) ? value : 0;
    if (isCommon && computed === computed) {
      var valuesIndex = valuesLength;
      while (valuesIndex--) {
        if (values[valuesIndex] === computed) {
          continue outer;
        }
      }
      result.push(value);
    }
    else if (!includes(values, computed, comparator)) {
      result.push(value);
    }
  }
  return result;
}

module.exports = baseDifference;


/***/ }),

/***/ 52033:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseForOwn = __webpack_require__(26194),
    createBaseEach = __webpack_require__(26789);

/**
 * The base implementation of `_.forEach` without support for iteratee shorthands.
 *
 * @private
 * @param {Array|Object} collection The collection to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @returns {Array|Object} Returns `collection`.
 */
var baseEach = createBaseEach(baseForOwn);

module.exports = baseEach;


/***/ }),

/***/ 95372:
/***/ ((module) => {

/**
 * The base implementation of `_.findIndex` and `_.findLastIndex` without
 * support for iteratee shorthands.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {Function} predicate The function invoked per iteration.
 * @param {number} fromIndex The index to search from.
 * @param {boolean} [fromRight] Specify iterating from right to left.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */
function baseFindIndex(array, predicate, fromIndex, fromRight) {
  var length = array.length,
      index = fromIndex + (fromRight ? 1 : -1);

  while ((fromRight ? index-- : ++index < length)) {
    if (predicate(array[index], index, array)) {
      return index;
    }
  }
  return -1;
}

module.exports = baseFindIndex;


/***/ }),

/***/ 49819:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var createBaseFor = __webpack_require__(18911);

/**
 * The base implementation of `baseForOwn` which iterates over `object`
 * properties returned by `keysFunc` and invokes `iteratee` for each property.
 * Iteratee functions may exit iteration early by explicitly returning `false`.
 *
 * @private
 * @param {Object} object The object to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @param {Function} keysFunc The function to get the keys of `object`.
 * @returns {Object} Returns `object`.
 */
var baseFor = createBaseFor();

module.exports = baseFor;


/***/ }),

/***/ 26194:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseFor = __webpack_require__(49819),
    keys = __webpack_require__(50098);

/**
 * The base implementation of `_.forOwn` without support for iteratee shorthands.
 *
 * @private
 * @param {Object} object The object to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @returns {Object} Returns `object`.
 */
function baseForOwn(object, iteratee) {
  return object && baseFor(object, iteratee, keys);
}

module.exports = baseForOwn;


/***/ }),

/***/ 8817:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseFindIndex = __webpack_require__(95372),
    baseIsNaN = __webpack_require__(1129),
    strictIndexOf = __webpack_require__(58263);

/**
 * The base implementation of `_.indexOf` without `fromIndex` bounds checks.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {*} value The value to search for.
 * @param {number} fromIndex The index to search from.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */
function baseIndexOf(array, value, fromIndex) {
  return value === value
    ? strictIndexOf(array, value, fromIndex)
    : baseFindIndex(array, baseIsNaN, fromIndex);
}

module.exports = baseIndexOf;


/***/ }),

/***/ 56006:
/***/ ((module) => {

/**
 * This function is like `baseIndexOf` except that it accepts a comparator.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {*} value The value to search for.
 * @param {number} fromIndex The index to search from.
 * @param {Function} comparator The comparator invoked per element.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */
function baseIndexOfWith(array, value, fromIndex, comparator) {
  var index = fromIndex - 1,
      length = array.length;

  while (++index < length) {
    if (comparator(array[index], value)) {
      return index;
    }
  }
  return -1;
}

module.exports = baseIndexOfWith;


/***/ }),

/***/ 96638:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var SetCache = __webpack_require__(52485),
    arrayIncludes = __webpack_require__(58095),
    arrayIncludesWith = __webpack_require__(21796),
    arrayMap = __webpack_require__(66070),
    baseUnary = __webpack_require__(39334),
    cacheHas = __webpack_require__(65581);

/* Built-in method references for those with the same name as other `lodash` methods. */
var nativeMin = Math.min;

/**
 * The base implementation of methods like `_.intersection`, without support
 * for iteratee shorthands, that accepts an array of arrays to inspect.
 *
 * @private
 * @param {Array} arrays The arrays to inspect.
 * @param {Function} [iteratee] The iteratee invoked per element.
 * @param {Function} [comparator] The comparator invoked per element.
 * @returns {Array} Returns the new array of shared values.
 */
function baseIntersection(arrays, iteratee, comparator) {
  var includes = comparator ? arrayIncludesWith : arrayIncludes,
      length = arrays[0].length,
      othLength = arrays.length,
      othIndex = othLength,
      caches = Array(othLength),
      maxLength = Infinity,
      result = [];

  while (othIndex--) {
    var array = arrays[othIndex];
    if (othIndex && iteratee) {
      array = arrayMap(array, baseUnary(iteratee));
    }
    maxLength = nativeMin(array.length, maxLength);
    caches[othIndex] = !comparator && (iteratee || (length >= 120 && array.length >= 120))
      ? new SetCache(othIndex && array)
      : undefined;
  }
  array = arrays[0];

  var index = -1,
      seen = caches[0];

  outer:
  while (++index < length && result.length < maxLength) {
    var value = array[index],
        computed = iteratee ? iteratee(value) : value;

    value = (comparator || value !== 0) ? value : 0;
    if (!(seen
          ? cacheHas(seen, computed)
          : includes(result, computed, comparator)
        )) {
      othIndex = othLength;
      while (--othIndex) {
        var cache = caches[othIndex];
        if (!(cache
              ? cacheHas(cache, computed)
              : includes(arrays[othIndex], computed, comparator))
            ) {
          continue outer;
        }
      }
      if (seen) {
        seen.push(computed);
      }
      result.push(value);
    }
  }
  return result;
}

module.exports = baseIntersection;


/***/ }),

/***/ 32866:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseIsEqualDeep = __webpack_require__(12772),
    isObjectLike = __webpack_require__(92360);

/**
 * The base implementation of `_.isEqual` which supports partial comparisons
 * and tracks traversed objects.
 *
 * @private
 * @param {*} value The value to compare.
 * @param {*} other The other value to compare.
 * @param {boolean} bitmask The bitmask flags.
 *  1 - Unordered comparison
 *  2 - Partial comparison
 * @param {Function} [customizer] The function to customize comparisons.
 * @param {Object} [stack] Tracks traversed `value` and `other` objects.
 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
 */
function baseIsEqual(value, other, bitmask, customizer, stack) {
  if (value === other) {
    return true;
  }
  if (value == null || other == null || (!isObjectLike(value) && !isObjectLike(other))) {
    return value !== value && other !== other;
  }
  return baseIsEqualDeep(value, other, bitmask, customizer, baseIsEqual, stack);
}

module.exports = baseIsEqual;


/***/ }),

/***/ 12772:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var Stack = __webpack_require__(23694),
    equalArrays = __webpack_require__(27042),
    equalByTag = __webpack_require__(370),
    equalObjects = __webpack_require__(39584),
    getTag = __webpack_require__(3533),
    isArray = __webpack_require__(19785),
    isBuffer = __webpack_require__(43854),
    isTypedArray = __webpack_require__(48519);

/** Used to compose bitmasks for value comparisons. */
var COMPARE_PARTIAL_FLAG = 1;

/** `Object#toString` result references. */
var argsTag = '[object Arguments]',
    arrayTag = '[object Array]',
    objectTag = '[object Object]';

/** Used for built-in method references. */
var objectProto = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty = objectProto.hasOwnProperty;

/**
 * A specialized version of `baseIsEqual` for arrays and objects which performs
 * deep comparisons and tracks traversed objects enabling objects with circular
 * references to be compared.
 *
 * @private
 * @param {Object} object The object to compare.
 * @param {Object} other The other object to compare.
 * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.
 * @param {Function} customizer The function to customize comparisons.
 * @param {Function} equalFunc The function to determine equivalents of values.
 * @param {Object} [stack] Tracks traversed `object` and `other` objects.
 * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.
 */
function baseIsEqualDeep(object, other, bitmask, customizer, equalFunc, stack) {
  var objIsArr = isArray(object),
      othIsArr = isArray(other),
      objTag = objIsArr ? arrayTag : getTag(object),
      othTag = othIsArr ? arrayTag : getTag(other);

  objTag = objTag == argsTag ? objectTag : objTag;
  othTag = othTag == argsTag ? objectTag : othTag;

  var objIsObj = objTag == objectTag,
      othIsObj = othTag == objectTag,
      isSameTag = objTag == othTag;

  if (isSameTag && isBuffer(object)) {
    if (!isBuffer(other)) {
      return false;
    }
    objIsArr = true;
    objIsObj = false;
  }
  if (isSameTag && !objIsObj) {
    stack || (stack = new Stack);
    return (objIsArr || isTypedArray(object))
      ? equalArrays(object, other, bitmask, customizer, equalFunc, stack)
      : equalByTag(object, other, objTag, bitmask, customizer, equalFunc, stack);
  }
  if (!(bitmask & COMPARE_PARTIAL_FLAG)) {
    var objIsWrapped = objIsObj && hasOwnProperty.call(object, '__wrapped__'),
        othIsWrapped = othIsObj && hasOwnProperty.call(other, '__wrapped__');

    if (objIsWrapped || othIsWrapped) {
      var objUnwrapped = objIsWrapped ? object.value() : object,
          othUnwrapped = othIsWrapped ? other.value() : other;

      stack || (stack = new Stack);
      return equalFunc(objUnwrapped, othUnwrapped, bitmask, customizer, stack);
    }
  }
  if (!isSameTag) {
    return false;
  }
  stack || (stack = new Stack);
  return equalObjects(object, other, bitmask, customizer, equalFunc, stack);
}

module.exports = baseIsEqualDeep;


/***/ }),

/***/ 19850:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var Stack = __webpack_require__(23694),
    baseIsEqual = __webpack_require__(32866);

/** Used to compose bitmasks for value comparisons. */
var COMPARE_PARTIAL_FLAG = 1,
    COMPARE_UNORDERED_FLAG = 2;

/**
 * The base implementation of `_.isMatch` without support for iteratee shorthands.
 *
 * @private
 * @param {Object} object The object to inspect.
 * @param {Object} source The object of property values to match.
 * @param {Array} matchData The property names, values, and compare flags to match.
 * @param {Function} [customizer] The function to customize comparisons.
 * @returns {boolean} Returns `true` if `object` is a match, else `false`.
 */
function baseIsMatch(object, source, matchData, customizer) {
  var index = matchData.length,
      length = index,
      noCustomizer = !customizer;

  if (object == null) {
    return !length;
  }
  object = Object(object);
  while (index--) {
    var data = matchData[index];
    if ((noCustomizer && data[2])
          ? data[1] !== object[data[0]]
          : !(data[0] in object)
        ) {
      return false;
    }
  }
  while (++index < length) {
    data = matchData[index];
    var key = data[0],
        objValue = object[key],
        srcValue = data[1];

    if (noCustomizer && data[2]) {
      if (objValue === undefined && !(key in object)) {
        return false;
      }
    } else {
      var stack = new Stack;
      if (customizer) {
        var result = customizer(objValue, srcValue, key, object, source, stack);
      }
      if (!(result === undefined
            ? baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG, customizer, stack)
            : result
          )) {
        return false;
      }
    }
  }
  return true;
}

module.exports = baseIsMatch;


/***/ }),

/***/ 1129:
/***/ ((module) => {

/**
 * The base implementation of `_.isNaN` without support for number objects.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is `NaN`, else `false`.
 */
function baseIsNaN(value) {
  return value !== value;
}

module.exports = baseIsNaN;


/***/ }),

/***/ 89278:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseMatches = __webpack_require__(71410),
    baseMatchesProperty = __webpack_require__(57518),
    identity = __webpack_require__(31137),
    isArray = __webpack_require__(19785),
    property = __webpack_require__(96001);

/**
 * The base implementation of `_.iteratee`.
 *
 * @private
 * @param {*} [value=_.identity] The value to convert to an iteratee.
 * @returns {Function} Returns the iteratee.
 */
function baseIteratee(value) {
  // Don't store the `typeof` result in a variable to avoid a JIT bug in Safari 9.
  // See https://bugs.webkit.org/show_bug.cgi?id=156034 for more details.
  if (typeof value == 'function') {
    return value;
  }
  if (value == null) {
    return identity;
  }
  if (typeof value == 'object') {
    return isArray(value)
      ? baseMatchesProperty(value[0], value[1])
      : baseMatches(value);
  }
  return property(value);
}

module.exports = baseIteratee;


/***/ }),

/***/ 67375:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseEach = __webpack_require__(52033),
    isArrayLike = __webpack_require__(80068);

/**
 * The base implementation of `_.map` without support for iteratee shorthands.
 *
 * @private
 * @param {Array|Object} collection The collection to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @returns {Array} Returns the new mapped array.
 */
function baseMap(collection, iteratee) {
  var index = -1,
      result = isArrayLike(collection) ? Array(collection.length) : [];

  baseEach(collection, function(value, key, collection) {
    result[++index] = iteratee(value, key, collection);
  });
  return result;
}

module.exports = baseMap;


/***/ }),

/***/ 71410:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseIsMatch = __webpack_require__(19850),
    getMatchData = __webpack_require__(68125),
    matchesStrictComparable = __webpack_require__(65042);

/**
 * The base implementation of `_.matches` which doesn't clone `source`.
 *
 * @private
 * @param {Object} source The object of property values to match.
 * @returns {Function} Returns the new spec function.
 */
function baseMatches(source) {
  var matchData = getMatchData(source);
  if (matchData.length == 1 && matchData[0][2]) {
    return matchesStrictComparable(matchData[0][0], matchData[0][1]);
  }
  return function(object) {
    return object === source || baseIsMatch(object, source, matchData);
  };
}

module.exports = baseMatches;


/***/ }),

/***/ 57518:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseIsEqual = __webpack_require__(32866),
    get = __webpack_require__(99729),
    hasIn = __webpack_require__(79749),
    isKey = __webpack_require__(40318),
    isStrictComparable = __webpack_require__(68302),
    matchesStrictComparable = __webpack_require__(65042),
    toKey = __webpack_require__(37948);

/** Used to compose bitmasks for value comparisons. */
var COMPARE_PARTIAL_FLAG = 1,
    COMPARE_UNORDERED_FLAG = 2;

/**
 * The base implementation of `_.matchesProperty` which doesn't clone `srcValue`.
 *
 * @private
 * @param {string} path The path of the property to get.
 * @param {*} srcValue The value to match.
 * @returns {Function} Returns the new spec function.
 */
function baseMatchesProperty(path, srcValue) {
  if (isKey(path) && isStrictComparable(srcValue)) {
    return matchesStrictComparable(toKey(path), srcValue);
  }
  return function(object) {
    var objValue = get(object, path);
    return (objValue === undefined && objValue === srcValue)
      ? hasIn(object, path)
      : baseIsEqual(srcValue, objValue, COMPARE_PARTIAL_FLAG | COMPARE_UNORDERED_FLAG);
  };
}

module.exports = baseMatchesProperty;


/***/ }),

/***/ 40015:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var Stack = __webpack_require__(23694),
    assignMergeValue = __webpack_require__(73140),
    baseFor = __webpack_require__(49819),
    baseMergeDeep = __webpack_require__(68867),
    isObject = __webpack_require__(11611),
    keysIn = __webpack_require__(53893),
    safeGet = __webpack_require__(97494);

/**
 * The base implementation of `_.merge` without support for multiple sources.
 *
 * @private
 * @param {Object} object The destination object.
 * @param {Object} source The source object.
 * @param {number} srcIndex The index of `source`.
 * @param {Function} [customizer] The function to customize merged values.
 * @param {Object} [stack] Tracks traversed source values and their merged
 *  counterparts.
 */
function baseMerge(object, source, srcIndex, customizer, stack) {
  if (object === source) {
    return;
  }
  baseFor(source, function(srcValue, key) {
    stack || (stack = new Stack);
    if (isObject(srcValue)) {
      baseMergeDeep(object, source, key, srcIndex, baseMerge, customizer, stack);
    }
    else {
      var newValue = customizer
        ? customizer(safeGet(object, key), srcValue, (key + ''), object, source, stack)
        : undefined;

      if (newValue === undefined) {
        newValue = srcValue;
      }
      assignMergeValue(object, key, newValue);
    }
  }, keysIn);
}

module.exports = baseMerge;


/***/ }),

/***/ 68867:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var assignMergeValue = __webpack_require__(73140),
    cloneBuffer = __webpack_require__(2734),
    cloneTypedArray = __webpack_require__(63428),
    copyArray = __webpack_require__(37561),
    initCloneObject = __webpack_require__(9560),
    isArguments = __webpack_require__(2900),
    isArray = __webpack_require__(19785),
    isArrayLikeObject = __webpack_require__(36468),
    isBuffer = __webpack_require__(43854),
    isFunction = __webpack_require__(28338),
    isObject = __webpack_require__(11611),
    isPlainObject = __webpack_require__(40861),
    isTypedArray = __webpack_require__(48519),
    safeGet = __webpack_require__(97494),
    toPlainObject = __webpack_require__(89328);

/**
 * A specialized version of `baseMerge` for arrays and objects which performs
 * deep merges and tracks traversed objects enabling objects with circular
 * references to be merged.
 *
 * @private
 * @param {Object} object The destination object.
 * @param {Object} source The source object.
 * @param {string} key The key of the value to merge.
 * @param {number} srcIndex The index of `source`.
 * @param {Function} mergeFunc The function to merge values.
 * @param {Function} [customizer] The function to customize assigned values.
 * @param {Object} [stack] Tracks traversed source values and their merged
 *  counterparts.
 */
function baseMergeDeep(object, source, key, srcIndex, mergeFunc, customizer, stack) {
  var objValue = safeGet(object, key),
      srcValue = safeGet(source, key),
      stacked = stack.get(srcValue);

  if (stacked) {
    assignMergeValue(object, key, stacked);
    return;
  }
  var newValue = customizer
    ? customizer(objValue, srcValue, (key + ''), object, source, stack)
    : undefined;

  var isCommon = newValue === undefined;

  if (isCommon) {
    var isArr = isArray(srcValue),
        isBuff = !isArr && isBuffer(srcValue),
        isTyped = !isArr && !isBuff && isTypedArray(srcValue);

    newValue = srcValue;
    if (isArr || isBuff || isTyped) {
      if (isArray(objValue)) {
        newValue = objValue;
      }
      else if (isArrayLikeObject(objValue)) {
        newValue = copyArray(objValue);
      }
      else if (isBuff) {
        isCommon = false;
        newValue = cloneBuffer(srcValue, true);
      }
      else if (isTyped) {
        isCommon = false;
        newValue = cloneTypedArray(srcValue, true);
      }
      else {
        newValue = [];
      }
    }
    else if (isPlainObject(srcValue) || isArguments(srcValue)) {
      newValue = objValue;
      if (isArguments(objValue)) {
        newValue = toPlainObject(objValue);
      }
      else if (!isObject(objValue) || isFunction(objValue)) {
        newValue = initCloneObject(srcValue);
      }
    }
    else {
      isCommon = false;
    }
  }
  if (isCommon) {
    // Recursively merge objects and arrays (susceptible to call stack limits).
    stack.set(srcValue, newValue);
    mergeFunc(newValue, srcValue, srcIndex, customizer, stack);
    stack['delete'](srcValue);
  }
  assignMergeValue(object, key, newValue);
}

module.exports = baseMergeDeep;


/***/ }),

/***/ 98497:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var arrayMap = __webpack_require__(66070),
    baseGet = __webpack_require__(79867),
    baseIteratee = __webpack_require__(89278),
    baseMap = __webpack_require__(67375),
    baseSortBy = __webpack_require__(73303),
    baseUnary = __webpack_require__(39334),
    compareMultiple = __webpack_require__(96348),
    identity = __webpack_require__(31137),
    isArray = __webpack_require__(19785);

/**
 * The base implementation of `_.orderBy` without param guards.
 *
 * @private
 * @param {Array|Object} collection The collection to iterate over.
 * @param {Function[]|Object[]|string[]} iteratees The iteratees to sort by.
 * @param {string[]} orders The sort orders of `iteratees`.
 * @returns {Array} Returns the new sorted array.
 */
function baseOrderBy(collection, iteratees, orders) {
  if (iteratees.length) {
    iteratees = arrayMap(iteratees, function(iteratee) {
      if (isArray(iteratee)) {
        return function(value) {
          return baseGet(value, iteratee.length === 1 ? iteratee[0] : iteratee);
        }
      }
      return iteratee;
    });
  } else {
    iteratees = [identity];
  }

  var index = -1;
  iteratees = arrayMap(iteratees, baseUnary(baseIteratee));

  var result = baseMap(collection, function(value, key, collection) {
    var criteria = arrayMap(iteratees, function(iteratee) {
      return iteratee(value);
    });
    return { 'criteria': criteria, 'index': ++index, 'value': value };
  });

  return baseSortBy(result, function(object, other) {
    return compareMultiple(object, other, orders);
  });
}

module.exports = baseOrderBy;


/***/ }),

/***/ 50517:
/***/ ((module) => {

/**
 * The base implementation of `_.property` without support for deep paths.
 *
 * @private
 * @param {string} key The key of the property to get.
 * @returns {Function} Returns the new accessor function.
 */
function baseProperty(key) {
  return function(object) {
    return object == null ? undefined : object[key];
  };
}

module.exports = baseProperty;


/***/ }),

/***/ 10301:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseGet = __webpack_require__(79867);

/**
 * A specialized version of `baseProperty` which supports deep paths.
 *
 * @private
 * @param {Array|string} path The path of the property to get.
 * @returns {Function} Returns the new accessor function.
 */
function basePropertyDeep(path) {
  return function(object) {
    return baseGet(object, path);
  };
}

module.exports = basePropertyDeep;


/***/ }),

/***/ 61933:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var arrayMap = __webpack_require__(66070),
    baseIndexOf = __webpack_require__(8817),
    baseIndexOfWith = __webpack_require__(56006),
    baseUnary = __webpack_require__(39334),
    copyArray = __webpack_require__(37561);

/** Used for built-in method references. */
var arrayProto = Array.prototype;

/** Built-in value references. */
var splice = arrayProto.splice;

/**
 * The base implementation of `_.pullAllBy` without support for iteratee
 * shorthands.
 *
 * @private
 * @param {Array} array The array to modify.
 * @param {Array} values The values to remove.
 * @param {Function} [iteratee] The iteratee invoked per element.
 * @param {Function} [comparator] The comparator invoked per element.
 * @returns {Array} Returns `array`.
 */
function basePullAll(array, values, iteratee, comparator) {
  var indexOf = comparator ? baseIndexOfWith : baseIndexOf,
      index = -1,
      length = values.length,
      seen = array;

  if (array === values) {
    values = copyArray(values);
  }
  if (iteratee) {
    seen = arrayMap(array, baseUnary(iteratee));
  }
  while (++index < length) {
    var fromIndex = 0,
        value = values[index],
        computed = iteratee ? iteratee(value) : value;

    while ((fromIndex = indexOf(seen, computed, fromIndex, comparator)) > -1) {
      if (seen !== array) {
        splice.call(seen, fromIndex, 1);
      }
      splice.call(array, fromIndex, 1);
    }
  }
  return array;
}

module.exports = basePullAll;


/***/ }),

/***/ 19356:
/***/ ((module) => {

/**
 * The base implementation of `_.reduce` and `_.reduceRight`, without support
 * for iteratee shorthands, which iterates over `collection` using `eachFunc`.
 *
 * @private
 * @param {Array|Object} collection The collection to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @param {*} accumulator The initial value.
 * @param {boolean} initAccum Specify using the first or last element of
 *  `collection` as the initial value.
 * @param {Function} eachFunc The function to iterate over `collection`.
 * @returns {*} Returns the accumulated value.
 */
function baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {
  eachFunc(collection, function(value, index, collection) {
    accumulator = initAccum
      ? (initAccum = false, value)
      : iteratee(accumulator, value, index, collection);
  });
  return accumulator;
}

module.exports = baseReduce;


/***/ }),

/***/ 1197:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var identity = __webpack_require__(31137),
    overRest = __webpack_require__(11871),
    setToString = __webpack_require__(63132);

/**
 * The base implementation of `_.rest` which doesn't validate or coerce arguments.
 *
 * @private
 * @param {Function} func The function to apply a rest parameter to.
 * @param {number} [start=func.length-1] The start position of the rest parameter.
 * @returns {Function} Returns the new function.
 */
function baseRest(func, start) {
  return setToString(overRest(func, start, identity), func + '');
}

module.exports = baseRest;


/***/ }),

/***/ 73303:
/***/ ((module) => {

/**
 * The base implementation of `_.sortBy` which uses `comparer` to define the
 * sort order of `array` and replaces criteria objects with their corresponding
 * values.
 *
 * @private
 * @param {Array} array The array to sort.
 * @param {Function} comparer The function to define sort order.
 * @returns {Array} Returns `array`.
 */
function baseSortBy(array, comparer) {
  var length = array.length;

  array.sort(comparer);
  while (length--) {
    array[length] = array[length].value;
  }
  return array;
}

module.exports = baseSortBy;


/***/ }),

/***/ 74833:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var trimmedEndIndex = __webpack_require__(56127);

/** Used to match leading whitespace. */
var reTrimStart = /^\s+/;

/**
 * The base implementation of `_.trim`.
 *
 * @private
 * @param {string} string The string to trim.
 * @returns {string} Returns the trimmed string.
 */
function baseTrim(string) {
  return string
    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')
    : string;
}

module.exports = baseTrim;


/***/ }),

/***/ 92198:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var SetCache = __webpack_require__(52485),
    arrayIncludes = __webpack_require__(58095),
    arrayIncludesWith = __webpack_require__(21796),
    cacheHas = __webpack_require__(65581),
    createSet = __webpack_require__(47111),
    setToArray = __webpack_require__(43735);

/** Used as the size to enable large array optimizations. */
var LARGE_ARRAY_SIZE = 200;

/**
 * The base implementation of `_.uniqBy` without support for iteratee shorthands.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {Function} [iteratee] The iteratee invoked per element.
 * @param {Function} [comparator] The comparator invoked per element.
 * @returns {Array} Returns the new duplicate free array.
 */
function baseUniq(array, iteratee, comparator) {
  var index = -1,
      includes = arrayIncludes,
      length = array.length,
      isCommon = true,
      result = [],
      seen = result;

  if (comparator) {
    isCommon = false;
    includes = arrayIncludesWith;
  }
  else if (length >= LARGE_ARRAY_SIZE) {
    var set = iteratee ? null : createSet(array);
    if (set) {
      return setToArray(set);
    }
    isCommon = false;
    includes = cacheHas;
    seen = new SetCache;
  }
  else {
    seen = iteratee ? [] : result;
  }
  outer:
  while (++index < length) {
    var value = array[index],
        computed = iteratee ? iteratee(value) : value;

    value = (comparator || value !== 0) ? value : 0;
    if (isCommon && computed === computed) {
      var seenIndex = seen.length;
      while (seenIndex--) {
        if (seen[seenIndex] === computed) {
          continue outer;
        }
      }
      if (iteratee) {
        seen.push(computed);
      }
      result.push(value);
    }
    else if (!includes(seen, computed, comparator)) {
      if (seen !== result) {
        seen.push(computed);
      }
      result.push(value);
    }
  }
  return result;
}

module.exports = baseUniq;


/***/ }),

/***/ 65581:
/***/ ((module) => {

/**
 * Checks if a `cache` value for `key` exists.
 *
 * @private
 * @param {Object} cache The cache to query.
 * @param {string} key The key of the entry to check.
 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
 */
function cacheHas(cache, key) {
  return cache.has(key);
}

module.exports = cacheHas;


/***/ }),

/***/ 60923:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var isArrayLikeObject = __webpack_require__(36468);

/**
 * Casts `value` to an empty array if it's not an array like object.
 *
 * @private
 * @param {*} value The value to inspect.
 * @returns {Array|Object} Returns the cast array-like object.
 */
function castArrayLikeObject(value) {
  return isArrayLikeObject(value) ? value : [];
}

module.exports = castArrayLikeObject;


/***/ }),

/***/ 62079:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var identity = __webpack_require__(31137);

/**
 * Casts `value` to `identity` if it's not a function.
 *
 * @private
 * @param {*} value The value to inspect.
 * @returns {Function} Returns cast function.
 */
function castFunction(value) {
  return typeof value == 'function' ? value : identity;
}

module.exports = castFunction;


/***/ }),

/***/ 17845:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var isSymbol = __webpack_require__(55193);

/**
 * Compares values to sort them in ascending order.
 *
 * @private
 * @param {*} value The value to compare.
 * @param {*} other The other value to compare.
 * @returns {number} Returns the sort order indicator for `value`.
 */
function compareAscending(value, other) {
  if (value !== other) {
    var valIsDefined = value !== undefined,
        valIsNull = value === null,
        valIsReflexive = value === value,
        valIsSymbol = isSymbol(value);

    var othIsDefined = other !== undefined,
        othIsNull = other === null,
        othIsReflexive = other === other,
        othIsSymbol = isSymbol(other);

    if ((!othIsNull && !othIsSymbol && !valIsSymbol && value > other) ||
        (valIsSymbol && othIsDefined && othIsReflexive && !othIsNull && !othIsSymbol) ||
        (valIsNull && othIsDefined && othIsReflexive) ||
        (!valIsDefined && othIsReflexive) ||
        !valIsReflexive) {
      return 1;
    }
    if ((!valIsNull && !valIsSymbol && !othIsSymbol && value < other) ||
        (othIsSymbol && valIsDefined && valIsReflexive && !valIsNull && !valIsSymbol) ||
        (othIsNull && valIsDefined && valIsReflexive) ||
        (!othIsDefined && valIsReflexive) ||
        !othIsReflexive) {
      return -1;
    }
  }
  return 0;
}

module.exports = compareAscending;


/***/ }),

/***/ 96348:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var compareAscending = __webpack_require__(17845);

/**
 * Used by `_.orderBy` to compare multiple properties of a value to another
 * and stable sort them.
 *
 * If `orders` is unspecified, all values are sorted in ascending order. Otherwise,
 * specify an order of "desc" for descending or "asc" for ascending sort order
 * of corresponding values.
 *
 * @private
 * @param {Object} object The object to compare.
 * @param {Object} other The other object to compare.
 * @param {boolean[]|string[]} orders The order to sort by for each property.
 * @returns {number} Returns the sort order indicator for `object`.
 */
function compareMultiple(object, other, orders) {
  var index = -1,
      objCriteria = object.criteria,
      othCriteria = other.criteria,
      length = objCriteria.length,
      ordersLength = orders.length;

  while (++index < length) {
    var result = compareAscending(objCriteria[index], othCriteria[index]);
    if (result) {
      if (index >= ordersLength) {
        return result;
      }
      var order = orders[index];
      return result * (order == 'desc' ? -1 : 1);
    }
  }
  // Fixes an `Array#sort` bug in the JS engine embedded in Adobe applications
  // that causes it, under certain circumstances, to provide the same value for
  // `object` and `other`. See https://github.com/jashkenas/underscore/pull/1247
  // for more details.
  //
  // This also ensures a stable sort in V8 and other engines.
  // See https://bugs.chromium.org/p/v8/issues/detail?id=90 for more details.
  return object.index - other.index;
}

module.exports = compareMultiple;


/***/ }),

/***/ 7270:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseRest = __webpack_require__(1197),
    isIterateeCall = __webpack_require__(57535);

/**
 * Creates a function like `_.assign`.
 *
 * @private
 * @param {Function} assigner The function to assign values.
 * @returns {Function} Returns the new assigner function.
 */
function createAssigner(assigner) {
  return baseRest(function(object, sources) {
    var index = -1,
        length = sources.length,
        customizer = length > 1 ? sources[length - 1] : undefined,
        guard = length > 2 ? sources[2] : undefined;

    customizer = (assigner.length > 3 && typeof customizer == 'function')
      ? (length--, customizer)
      : undefined;

    if (guard && isIterateeCall(sources[0], sources[1], guard)) {
      customizer = length < 3 ? undefined : customizer;
      length = 1;
    }
    object = Object(object);
    while (++index < length) {
      var source = sources[index];
      if (source) {
        assigner(object, source, index, customizer);
      }
    }
    return object;
  });
}

module.exports = createAssigner;


/***/ }),

/***/ 26789:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var isArrayLike = __webpack_require__(80068);

/**
 * Creates a `baseEach` or `baseEachRight` function.
 *
 * @private
 * @param {Function} eachFunc The function to iterate over a collection.
 * @param {boolean} [fromRight] Specify iterating from right to left.
 * @returns {Function} Returns the new base function.
 */
function createBaseEach(eachFunc, fromRight) {
  return function(collection, iteratee) {
    if (collection == null) {
      return collection;
    }
    if (!isArrayLike(collection)) {
      return eachFunc(collection, iteratee);
    }
    var length = collection.length,
        index = fromRight ? length : -1,
        iterable = Object(collection);

    while ((fromRight ? index-- : ++index < length)) {
      if (iteratee(iterable[index], index, iterable) === false) {
        break;
      }
    }
    return collection;
  };
}

module.exports = createBaseEach;


/***/ }),

/***/ 18911:
/***/ ((module) => {

/**
 * Creates a base function for methods like `_.forIn` and `_.forOwn`.
 *
 * @private
 * @param {boolean} [fromRight] Specify iterating from right to left.
 * @returns {Function} Returns the new base function.
 */
function createBaseFor(fromRight) {
  return function(object, iteratee, keysFunc) {
    var index = -1,
        iterable = Object(object),
        props = keysFunc(object),
        length = props.length;

    while (length--) {
      var key = props[fromRight ? length : ++index];
      if (iteratee(iterable[key], key, iterable) === false) {
        break;
      }
    }
    return object;
  };
}

module.exports = createBaseFor;


/***/ }),

/***/ 47111:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var Set = __webpack_require__(46151),
    noop = __webpack_require__(50344),
    setToArray = __webpack_require__(43735);

/** Used as references for various `Number` constants. */
var INFINITY = 1 / 0;

/**
 * Creates a set object of `values`.
 *
 * @private
 * @param {Array} values The values to add to the set.
 * @returns {Object} Returns the new set.
 */
var createSet = !(Set && (1 / setToArray(new Set([,-0]))[1]) == INFINITY) ? noop : function(values) {
  return new Set(values);
};

module.exports = createSet;


/***/ }),

/***/ 19491:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseMerge = __webpack_require__(40015),
    isObject = __webpack_require__(11611);

/**
 * Used by `_.defaultsDeep` to customize its `_.merge` use to merge source
 * objects into destination objects that are passed thru.
 *
 * @private
 * @param {*} objValue The destination value.
 * @param {*} srcValue The source value.
 * @param {string} key The key of the property to merge.
 * @param {Object} object The parent object of `objValue`.
 * @param {Object} source The parent object of `srcValue`.
 * @param {Object} [stack] Tracks traversed source values and their merged
 *  counterparts.
 * @returns {*} Returns the value to assign.
 */
function customDefaultsMerge(objValue, srcValue, key, object, source, stack) {
  if (isObject(objValue) && isObject(srcValue)) {
    // Recursively merge objects and arrays (susceptible to call stack limits).
    stack.set(srcValue, objValue);
    baseMerge(objValue, srcValue, undefined, customDefaultsMerge, stack);
    stack['delete'](srcValue);
  }
  return objValue;
}

module.exports = customDefaultsMerge;


/***/ }),

/***/ 27042:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var SetCache = __webpack_require__(52485),
    arraySome = __webpack_require__(90756),
    cacheHas = __webpack_require__(65581);

/** Used to compose bitmasks for value comparisons. */
var COMPARE_PARTIAL_FLAG = 1,
    COMPARE_UNORDERED_FLAG = 2;

/**
 * A specialized version of `baseIsEqualDeep` for arrays with support for
 * partial deep comparisons.
 *
 * @private
 * @param {Array} array The array to compare.
 * @param {Array} other The other array to compare.
 * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.
 * @param {Function} customizer The function to customize comparisons.
 * @param {Function} equalFunc The function to determine equivalents of values.
 * @param {Object} stack Tracks traversed `array` and `other` objects.
 * @returns {boolean} Returns `true` if the arrays are equivalent, else `false`.
 */
function equalArrays(array, other, bitmask, customizer, equalFunc, stack) {
  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,
      arrLength = array.length,
      othLength = other.length;

  if (arrLength != othLength && !(isPartial && othLength > arrLength)) {
    return false;
  }
  // Check that cyclic values are equal.
  var arrStacked = stack.get(array);
  var othStacked = stack.get(other);
  if (arrStacked && othStacked) {
    return arrStacked == other && othStacked == array;
  }
  var index = -1,
      result = true,
      seen = (bitmask & COMPARE_UNORDERED_FLAG) ? new SetCache : undefined;

  stack.set(array, other);
  stack.set(other, array);

  // Ignore non-index properties.
  while (++index < arrLength) {
    var arrValue = array[index],
        othValue = other[index];

    if (customizer) {
      var compared = isPartial
        ? customizer(othValue, arrValue, index, other, array, stack)
        : customizer(arrValue, othValue, index, array, other, stack);
    }
    if (compared !== undefined) {
      if (compared) {
        continue;
      }
      result = false;
      break;
    }
    // Recursively compare arrays (susceptible to call stack limits).
    if (seen) {
      if (!arraySome(other, function(othValue, othIndex) {
            if (!cacheHas(seen, othIndex) &&
                (arrValue === othValue || equalFunc(arrValue, othValue, bitmask, customizer, stack))) {
              return seen.push(othIndex);
            }
          })) {
        result = false;
        break;
      }
    } else if (!(
          arrValue === othValue ||
            equalFunc(arrValue, othValue, bitmask, customizer, stack)
        )) {
      result = false;
      break;
    }
  }
  stack['delete'](array);
  stack['delete'](other);
  return result;
}

module.exports = equalArrays;


/***/ }),

/***/ 370:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var Symbol = __webpack_require__(96539),
    Uint8Array = __webpack_require__(59942),
    eq = __webpack_require__(85638),
    equalArrays = __webpack_require__(27042),
    mapToArray = __webpack_require__(19383),
    setToArray = __webpack_require__(43735);

/** Used to compose bitmasks for value comparisons. */
var COMPARE_PARTIAL_FLAG = 1,
    COMPARE_UNORDERED_FLAG = 2;

/** `Object#toString` result references. */
var boolTag = '[object Boolean]',
    dateTag = '[object Date]',
    errorTag = '[object Error]',
    mapTag = '[object Map]',
    numberTag = '[object Number]',
    regexpTag = '[object RegExp]',
    setTag = '[object Set]',
    stringTag = '[object String]',
    symbolTag = '[object Symbol]';

var arrayBufferTag = '[object ArrayBuffer]',
    dataViewTag = '[object DataView]';

/** Used to convert symbols to primitives and strings. */
var symbolProto = Symbol ? Symbol.prototype : undefined,
    symbolValueOf = symbolProto ? symbolProto.valueOf : undefined;

/**
 * A specialized version of `baseIsEqualDeep` for comparing objects of
 * the same `toStringTag`.
 *
 * **Note:** This function only supports comparing values with tags of
 * `Boolean`, `Date`, `Error`, `Number`, `RegExp`, or `String`.
 *
 * @private
 * @param {Object} object The object to compare.
 * @param {Object} other The other object to compare.
 * @param {string} tag The `toStringTag` of the objects to compare.
 * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.
 * @param {Function} customizer The function to customize comparisons.
 * @param {Function} equalFunc The function to determine equivalents of values.
 * @param {Object} stack Tracks traversed `object` and `other` objects.
 * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.
 */
function equalByTag(object, other, tag, bitmask, customizer, equalFunc, stack) {
  switch (tag) {
    case dataViewTag:
      if ((object.byteLength != other.byteLength) ||
          (object.byteOffset != other.byteOffset)) {
        return false;
      }
      object = object.buffer;
      other = other.buffer;

    case arrayBufferTag:
      if ((object.byteLength != other.byteLength) ||
          !equalFunc(new Uint8Array(object), new Uint8Array(other))) {
        return false;
      }
      return true;

    case boolTag:
    case dateTag:
    case numberTag:
      // Coerce booleans to `1` or `0` and dates to milliseconds.
      // Invalid dates are coerced to `NaN`.
      return eq(+object, +other);

    case errorTag:
      return object.name == other.name && object.message == other.message;

    case regexpTag:
    case stringTag:
      // Coerce regexes to strings and treat strings, primitives and objects,
      // as equal. See http://www.ecma-international.org/ecma-262/7.0/#sec-regexp.prototype.tostring
      // for more details.
      return object == (other + '');

    case mapTag:
      var convert = mapToArray;

    case setTag:
      var isPartial = bitmask & COMPARE_PARTIAL_FLAG;
      convert || (convert = setToArray);

      if (object.size != other.size && !isPartial) {
        return false;
      }
      // Assume cyclic values are equal.
      var stacked = stack.get(object);
      if (stacked) {
        return stacked == other;
      }
      bitmask |= COMPARE_UNORDERED_FLAG;

      // Recursively compare objects (susceptible to call stack limits).
      stack.set(object, other);
      var result = equalArrays(convert(object), convert(other), bitmask, customizer, equalFunc, stack);
      stack['delete'](object);
      return result;

    case symbolTag:
      if (symbolValueOf) {
        return symbolValueOf.call(object) == symbolValueOf.call(other);
      }
  }
  return false;
}

module.exports = equalByTag;


/***/ }),

/***/ 39584:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var getAllKeys = __webpack_require__(51385);

/** Used to compose bitmasks for value comparisons. */
var COMPARE_PARTIAL_FLAG = 1;

/** Used for built-in method references. */
var objectProto = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty = objectProto.hasOwnProperty;

/**
 * A specialized version of `baseIsEqualDeep` for objects with support for
 * partial deep comparisons.
 *
 * @private
 * @param {Object} object The object to compare.
 * @param {Object} other The other object to compare.
 * @param {number} bitmask The bitmask flags. See `baseIsEqual` for more details.
 * @param {Function} customizer The function to customize comparisons.
 * @param {Function} equalFunc The function to determine equivalents of values.
 * @param {Object} stack Tracks traversed `object` and `other` objects.
 * @returns {boolean} Returns `true` if the objects are equivalent, else `false`.
 */
function equalObjects(object, other, bitmask, customizer, equalFunc, stack) {
  var isPartial = bitmask & COMPARE_PARTIAL_FLAG,
      objProps = getAllKeys(object),
      objLength = objProps.length,
      othProps = getAllKeys(other),
      othLength = othProps.length;

  if (objLength != othLength && !isPartial) {
    return false;
  }
  var index = objLength;
  while (index--) {
    var key = objProps[index];
    if (!(isPartial ? key in other : hasOwnProperty.call(other, key))) {
      return false;
    }
  }
  // Check that cyclic values are equal.
  var objStacked = stack.get(object);
  var othStacked = stack.get(other);
  if (objStacked && othStacked) {
    return objStacked == other && othStacked == object;
  }
  var result = true;
  stack.set(object, other);
  stack.set(other, object);

  var skipCtor = isPartial;
  while (++index < objLength) {
    key = objProps[index];
    var objValue = object[key],
        othValue = other[key];

    if (customizer) {
      var compared = isPartial
        ? customizer(othValue, objValue, key, other, object, stack)
        : customizer(objValue, othValue, key, object, other, stack);
    }
    // Recursively compare objects (susceptible to call stack limits).
    if (!(compared === undefined
          ? (objValue === othValue || equalFunc(objValue, othValue, bitmask, customizer, stack))
          : compared
        )) {
      result = false;
      break;
    }
    skipCtor || (skipCtor = key == 'constructor');
  }
  if (result && !skipCtor) {
    var objCtor = object.constructor,
        othCtor = other.constructor;

    // Non `Object` object instances with different constructors are not equal.
    if (objCtor != othCtor &&
        ('constructor' in object && 'constructor' in other) &&
        !(typeof objCtor == 'function' && objCtor instanceof objCtor &&
          typeof othCtor == 'function' && othCtor instanceof othCtor)) {
      result = false;
    }
  }
  stack['delete'](object);
  stack['delete'](other);
  return result;
}

module.exports = equalObjects;


/***/ }),

/***/ 68125:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var isStrictComparable = __webpack_require__(68302),
    keys = __webpack_require__(50098);

/**
 * Gets the property names, values, and compare flags of `object`.
 *
 * @private
 * @param {Object} object The object to query.
 * @returns {Array} Returns the match data of `object`.
 */
function getMatchData(object) {
  var result = keys(object),
      length = result.length;

  while (length--) {
    var key = result[length],
        value = object[key];

    result[length] = [key, value, isStrictComparable(value)];
  }
  return result;
}

module.exports = getMatchData;


/***/ }),

/***/ 57535:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var eq = __webpack_require__(85638),
    isArrayLike = __webpack_require__(80068),
    isIndex = __webpack_require__(42383),
    isObject = __webpack_require__(11611);

/**
 * Checks if the given arguments are from an iteratee call.
 *
 * @private
 * @param {*} value The potential iteratee value argument.
 * @param {*} index The potential iteratee index or key argument.
 * @param {*} object The potential iteratee object argument.
 * @returns {boolean} Returns `true` if the arguments are from an iteratee call,
 *  else `false`.
 */
function isIterateeCall(value, index, object) {
  if (!isObject(object)) {
    return false;
  }
  var type = typeof index;
  if (type == 'number'
        ? (isArrayLike(object) && isIndex(index, object.length))
        : (type == 'string' && index in object)
      ) {
    return eq(object[index], value);
  }
  return false;
}

module.exports = isIterateeCall;


/***/ }),

/***/ 68302:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var isObject = __webpack_require__(11611);

/**
 * Checks if `value` is suitable for strict equality comparisons, i.e. `===`.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` if suitable for strict
 *  equality comparisons, else `false`.
 */
function isStrictComparable(value) {
  return value === value && !isObject(value);
}

module.exports = isStrictComparable;


/***/ }),

/***/ 19383:
/***/ ((module) => {

/**
 * Converts `map` to its key-value pairs.
 *
 * @private
 * @param {Object} map The map to convert.
 * @returns {Array} Returns the key-value pairs.
 */
function mapToArray(map) {
  var index = -1,
      result = Array(map.size);

  map.forEach(function(value, key) {
    result[++index] = [key, value];
  });
  return result;
}

module.exports = mapToArray;


/***/ }),

/***/ 65042:
/***/ ((module) => {

/**
 * A specialized version of `matchesProperty` for source values suitable
 * for strict equality comparisons, i.e. `===`.
 *
 * @private
 * @param {string} key The key of the property to get.
 * @param {*} srcValue The value to match.
 * @returns {Function} Returns the new spec function.
 */
function matchesStrictComparable(key, srcValue) {
  return function(object) {
    if (object == null) {
      return false;
    }
    return object[key] === srcValue &&
      (srcValue !== undefined || (key in Object(object)));
  };
}

module.exports = matchesStrictComparable;


/***/ }),

/***/ 97494:
/***/ ((module) => {

/**
 * Gets the value at `key`, unless `key` is "__proto__" or "constructor".
 *
 * @private
 * @param {Object} object The object to query.
 * @param {string} key The key of the property to get.
 * @returns {*} Returns the property value.
 */
function safeGet(object, key) {
  if (key === 'constructor' && typeof object[key] === 'function') {
    return;
  }

  if (key == '__proto__') {
    return;
  }

  return object[key];
}

module.exports = safeGet;


/***/ }),

/***/ 38639:
/***/ ((module) => {

/** Used to stand-in for `undefined` hash values. */
var HASH_UNDEFINED = '__lodash_hash_undefined__';

/**
 * Adds `value` to the array cache.
 *
 * @private
 * @name add
 * @memberOf SetCache
 * @alias push
 * @param {*} value The value to cache.
 * @returns {Object} Returns the cache instance.
 */
function setCacheAdd(value) {
  this.__data__.set(value, HASH_UNDEFINED);
  return this;
}

module.exports = setCacheAdd;


/***/ }),

/***/ 88379:
/***/ ((module) => {

/**
 * Checks if `value` is in the array cache.
 *
 * @private
 * @name has
 * @memberOf SetCache
 * @param {*} value The value to search for.
 * @returns {number} Returns `true` if `value` is found, else `false`.
 */
function setCacheHas(value) {
  return this.__data__.has(value);
}

module.exports = setCacheHas;


/***/ }),

/***/ 43735:
/***/ ((module) => {

/**
 * Converts `set` to an array of its values.
 *
 * @private
 * @param {Object} set The set to convert.
 * @returns {Array} Returns the values.
 */
function setToArray(set) {
  var index = -1,
      result = Array(set.size);

  set.forEach(function(value) {
    result[++index] = value;
  });
  return result;
}

module.exports = setToArray;


/***/ }),

/***/ 58263:
/***/ ((module) => {

/**
 * A specialized version of `_.indexOf` which performs strict equality
 * comparisons of values, i.e. `===`.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {*} value The value to search for.
 * @param {number} fromIndex The index to search from.
 * @returns {number} Returns the index of the matched value, else `-1`.
 */
function strictIndexOf(array, value, fromIndex) {
  var index = fromIndex - 1,
      length = array.length;

  while (++index < length) {
    if (array[index] === value) {
      return index;
    }
  }
  return -1;
}

module.exports = strictIndexOf;


/***/ }),

/***/ 56127:
/***/ ((module) => {

/** Used to match a single whitespace character. */
var reWhitespace = /\s/;

/**
 * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace
 * character of `string`.
 *
 * @private
 * @param {string} string The string to inspect.
 * @returns {number} Returns the index of the last non-whitespace character.
 */
function trimmedEndIndex(string) {
  var index = string.length;

  while (index-- && reWhitespace.test(string.charAt(index))) {}
  return index;
}

module.exports = trimmedEndIndex;


/***/ }),

/***/ 70236:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseRest = __webpack_require__(1197),
    eq = __webpack_require__(85638),
    isIterateeCall = __webpack_require__(57535),
    keysIn = __webpack_require__(53893);

/** Used for built-in method references. */
var objectProto = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty = objectProto.hasOwnProperty;

/**
 * Assigns own and inherited enumerable string keyed properties of source
 * objects to the destination object for all destination properties that
 * resolve to `undefined`. Source objects are applied from left to right.
 * Once a property is set, additional values of the same property are ignored.
 *
 * **Note:** This method mutates `object`.
 *
 * @static
 * @since 0.1.0
 * @memberOf _
 * @category Object
 * @param {Object} object The destination object.
 * @param {...Object} [sources] The source objects.
 * @returns {Object} Returns `object`.
 * @see _.defaultsDeep
 * @example
 *
 * _.defaults({ 'a': 1 }, { 'b': 2 }, { 'a': 3 });
 * // => { 'a': 1, 'b': 2 }
 */
var defaults = baseRest(function(object, sources) {
  object = Object(object);

  var index = -1;
  var length = sources.length;
  var guard = length > 2 ? sources[2] : undefined;

  if (guard && isIterateeCall(sources[0], sources[1], guard)) {
    length = 1;
  }

  while (++index < length) {
    var source = sources[index];
    var props = keysIn(source);
    var propsIndex = -1;
    var propsLength = props.length;

    while (++propsIndex < propsLength) {
      var key = props[propsIndex];
      var value = object[key];

      if (value === undefined ||
          (eq(value, objectProto[key]) && !hasOwnProperty.call(object, key))) {
        object[key] = source[key];
      }
    }
  }

  return object;
});

module.exports = defaults;


/***/ }),

/***/ 53533:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var apply = __webpack_require__(79349),
    baseRest = __webpack_require__(1197),
    customDefaultsMerge = __webpack_require__(19491),
    mergeWith = __webpack_require__(73952);

/**
 * This method is like `_.defaults` except that it recursively assigns
 * default properties.
 *
 * **Note:** This method mutates `object`.
 *
 * @static
 * @memberOf _
 * @since 3.10.0
 * @category Object
 * @param {Object} object The destination object.
 * @param {...Object} [sources] The source objects.
 * @returns {Object} Returns `object`.
 * @see _.defaults
 * @example
 *
 * _.defaultsDeep({ 'a': { 'b': 2 } }, { 'a': { 'b': 1, 'c': 3 } });
 * // => { 'a': { 'b': 2, 'c': 3 } }
 */
var defaultsDeep = baseRest(function(args) {
  args.push(undefined, customDefaultsMerge);
  return apply(mergeWith, undefined, args);
});

module.exports = defaultsDeep;


/***/ }),

/***/ 84812:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseFlatten = __webpack_require__(23545);

/** Used as references for various `Number` constants. */
var INFINITY = 1 / 0;

/**
 * Recursively flattens `array`.
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category Array
 * @param {Array} array The array to flatten.
 * @returns {Array} Returns the new flattened array.
 * @example
 *
 * _.flattenDeep([1, [2, [3, [4]], 5]]);
 * // => [1, 2, 3, 4, 5]
 */
function flattenDeep(array) {
  var length = array == null ? 0 : array.length;
  return length ? baseFlatten(array, INFINITY) : [];
}

module.exports = flattenDeep;


/***/ }),

/***/ 47003:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var arrayEach = __webpack_require__(80594),
    baseEach = __webpack_require__(52033),
    castFunction = __webpack_require__(62079),
    isArray = __webpack_require__(19785);

/**
 * Iterates over elements of `collection` and invokes `iteratee` for each element.
 * The iteratee is invoked with three arguments: (value, index|key, collection).
 * Iteratee functions may exit iteration early by explicitly returning `false`.
 *
 * **Note:** As with other "Collections" methods, objects with a "length"
 * property are iterated like arrays. To avoid this behavior use `_.forIn`
 * or `_.forOwn` for object iteration.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @alias each
 * @category Collection
 * @param {Array|Object} collection The collection to iterate over.
 * @param {Function} [iteratee=_.identity] The function invoked per iteration.
 * @returns {Array|Object} Returns `collection`.
 * @see _.forEachRight
 * @example
 *
 * _.forEach([1, 2], function(value) {
 *   console.log(value);
 * });
 * // => Logs `1` then `2`.
 *
 * _.forEach({ 'a': 1, 'b': 2 }, function(value, key) {
 *   console.log(key);
 * });
 * // => Logs 'a' then 'b' (iteration order is not guaranteed).
 */
function forEach(collection, iteratee) {
  var func = isArray(collection) ? arrayEach : baseEach;
  return func(collection, castFunction(iteratee));
}

module.exports = forEach;


/***/ }),

/***/ 27921:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var arrayMap = __webpack_require__(66070),
    baseIntersection = __webpack_require__(96638),
    baseRest = __webpack_require__(1197),
    castArrayLikeObject = __webpack_require__(60923);

/**
 * Creates an array of unique values that are included in all given arrays
 * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * for equality comparisons. The order and references of result values are
 * determined by the first array.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Array
 * @param {...Array} [arrays] The arrays to inspect.
 * @returns {Array} Returns the new array of intersecting values.
 * @example
 *
 * _.intersection([2, 1], [2, 3]);
 * // => [2]
 */
var intersection = baseRest(function(arrays) {
  var mapped = arrayMap(arrays, castArrayLikeObject);
  return (mapped.length && mapped[0] === arrays[0])
    ? baseIntersection(mapped)
    : [];
});

module.exports = intersection;


/***/ }),

/***/ 28054:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var arrayMap = __webpack_require__(66070),
    baseIntersection = __webpack_require__(96638),
    baseRest = __webpack_require__(1197),
    castArrayLikeObject = __webpack_require__(60923),
    last = __webpack_require__(31159);

/**
 * This method is like `_.intersection` except that it accepts `comparator`
 * which is invoked to compare elements of `arrays`. The order and references
 * of result values are determined by the first array. The comparator is
 * invoked with two arguments: (arrVal, othVal).
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Array
 * @param {...Array} [arrays] The arrays to inspect.
 * @param {Function} [comparator] The comparator invoked per element.
 * @returns {Array} Returns the new array of intersecting values.
 * @example
 *
 * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }];
 * var others = [{ 'x': 1, 'y': 1 }, { 'x': 1, 'y': 2 }];
 *
 * _.intersectionWith(objects, others, _.isEqual);
 * // => [{ 'x': 1, 'y': 2 }]
 */
var intersectionWith = baseRest(function(arrays) {
  var comparator = last(arrays),
      mapped = arrayMap(arrays, castArrayLikeObject);

  comparator = typeof comparator == 'function' ? comparator : undefined;
  if (comparator) {
    mapped.pop();
  }
  return (mapped.length && mapped[0] === arrays[0])
    ? baseIntersection(mapped, undefined, comparator)
    : [];
});

module.exports = intersectionWith;


/***/ }),

/***/ 36468:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var isArrayLike = __webpack_require__(80068),
    isObjectLike = __webpack_require__(92360);

/**
 * This method is like `_.isArrayLike` except that it also checks if `value`
 * is an object.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an array-like object,
 *  else `false`.
 * @example
 *
 * _.isArrayLikeObject([1, 2, 3]);
 * // => true
 *
 * _.isArrayLikeObject(document.body.children);
 * // => true
 *
 * _.isArrayLikeObject('abc');
 * // => false
 *
 * _.isArrayLikeObject(_.noop);
 * // => false
 */
function isArrayLikeObject(value) {
  return isObjectLike(value) && isArrayLike(value);
}

module.exports = isArrayLikeObject;


/***/ }),

/***/ 3819:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseGetTag = __webpack_require__(99736),
    isObjectLike = __webpack_require__(92360);

/** `Object#toString` result references. */
var boolTag = '[object Boolean]';

/**
 * Checks if `value` is classified as a boolean primitive or object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a boolean, else `false`.
 * @example
 *
 * _.isBoolean(false);
 * // => true
 *
 * _.isBoolean(null);
 * // => false
 */
function isBoolean(value) {
  return value === true || value === false ||
    (isObjectLike(value) && baseGetTag(value) == boolTag);
}

module.exports = isBoolean;


/***/ }),

/***/ 56141:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseIsEqual = __webpack_require__(32866);

/**
 * Performs a deep comparison between two values to determine if they are
 * equivalent.
 *
 * **Note:** This method supports comparing arrays, array buffers, booleans,
 * date objects, error objects, maps, numbers, `Object` objects, regexes,
 * sets, strings, symbols, and typed arrays. `Object` objects are compared
 * by their own, not inherited, enumerable properties. Functions and DOM
 * nodes are compared by strict equality, i.e. `===`.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to compare.
 * @param {*} other The other value to compare.
 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
 * @example
 *
 * var object = { 'a': 1 };
 * var other = { 'a': 1 };
 *
 * _.isEqual(object, other);
 * // => true
 *
 * object === other;
 * // => false
 */
function isEqual(value, other) {
  return baseIsEqual(value, other);
}

module.exports = isEqual;


/***/ }),

/***/ 65191:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseIsEqual = __webpack_require__(32866);

/**
 * This method is like `_.isEqual` except that it accepts `customizer` which
 * is invoked to compare values. If `customizer` returns `undefined`, comparisons
 * are handled by the method instead. The `customizer` is invoked with up to
 * six arguments: (objValue, othValue [, index|key, object, other, stack]).
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to compare.
 * @param {*} other The other value to compare.
 * @param {Function} [customizer] The function to customize comparisons.
 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
 * @example
 *
 * function isGreeting(value) {
 *   return /^h(?:i|ello)$/.test(value);
 * }
 *
 * function customizer(objValue, othValue) {
 *   if (isGreeting(objValue) && isGreeting(othValue)) {
 *     return true;
 *   }
 * }
 *
 * var array = ['hello', 'goodbye'];
 * var other = ['hi', 'goodbye'];
 *
 * _.isEqualWith(array, other, customizer);
 * // => true
 */
function isEqualWith(value, other, customizer) {
  customizer = typeof customizer == 'function' ? customizer : undefined;
  var result = customizer ? customizer(value, other) : undefined;
  return result === undefined ? baseIsEqual(value, other, undefined, customizer) : !!result;
}

module.exports = isEqualWith;


/***/ }),

/***/ 30644:
/***/ ((module) => {

/**
 * Checks if `value` is `null` or `undefined`.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is nullish, else `false`.
 * @example
 *
 * _.isNil(null);
 * // => true
 *
 * _.isNil(void 0);
 * // => true
 *
 * _.isNil(NaN);
 * // => false
 */
function isNil(value) {
  return value == null;
}

module.exports = isNil;


/***/ }),

/***/ 78581:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseGetTag = __webpack_require__(99736),
    isObjectLike = __webpack_require__(92360);

/** `Object#toString` result references. */
var numberTag = '[object Number]';

/**
 * Checks if `value` is classified as a `Number` primitive or object.
 *
 * **Note:** To exclude `Infinity`, `-Infinity`, and `NaN`, which are
 * classified as numbers, use the `_.isFinite` method.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a number, else `false`.
 * @example
 *
 * _.isNumber(3);
 * // => true
 *
 * _.isNumber(Number.MIN_VALUE);
 * // => true
 *
 * _.isNumber(Infinity);
 * // => true
 *
 * _.isNumber('3');
 * // => false
 */
function isNumber(value) {
  return typeof value == 'number' ||
    (isObjectLike(value) && baseGetTag(value) == numberTag);
}

module.exports = isNumber;


/***/ }),

/***/ 47206:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseGetTag = __webpack_require__(99736),
    isArray = __webpack_require__(19785),
    isObjectLike = __webpack_require__(92360);

/** `Object#toString` result references. */
var stringTag = '[object String]';

/**
 * Checks if `value` is classified as a `String` primitive or object.
 *
 * @static
 * @since 0.1.0
 * @memberOf _
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a string, else `false`.
 * @example
 *
 * _.isString('abc');
 * // => true
 *
 * _.isString(1);
 * // => false
 */
function isString(value) {
  return typeof value == 'string' ||
    (!isArray(value) && isObjectLike(value) && baseGetTag(value) == stringTag);
}

module.exports = isString;


/***/ }),

/***/ 15409:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseMerge = __webpack_require__(40015),
    createAssigner = __webpack_require__(7270);

/**
 * This method is like `_.assign` except that it recursively merges own and
 * inherited enumerable string keyed properties of source objects into the
 * destination object. Source properties that resolve to `undefined` are
 * skipped if a destination value exists. Array and plain object properties
 * are merged recursively. Other objects and value types are overridden by
 * assignment. Source objects are applied from left to right. Subsequent
 * sources overwrite property assignments of previous sources.
 *
 * **Note:** This method mutates `object`.
 *
 * @static
 * @memberOf _
 * @since 0.5.0
 * @category Object
 * @param {Object} object The destination object.
 * @param {...Object} [sources] The source objects.
 * @returns {Object} Returns `object`.
 * @example
 *
 * var object = {
 *   'a': [{ 'b': 2 }, { 'd': 4 }]
 * };
 *
 * var other = {
 *   'a': [{ 'c': 3 }, { 'e': 5 }]
 * };
 *
 * _.merge(object, other);
 * // => { 'a': [{ 'b': 2, 'c': 3 }, { 'd': 4, 'e': 5 }] }
 */
var merge = createAssigner(function(object, source, srcIndex) {
  baseMerge(object, source, srcIndex);
});

module.exports = merge;


/***/ }),

/***/ 73952:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseMerge = __webpack_require__(40015),
    createAssigner = __webpack_require__(7270);

/**
 * This method is like `_.merge` except that it accepts `customizer` which
 * is invoked to produce the merged values of the destination and source
 * properties. If `customizer` returns `undefined`, merging is handled by the
 * method instead. The `customizer` is invoked with six arguments:
 * (objValue, srcValue, key, object, source, stack).
 *
 * **Note:** This method mutates `object`.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Object
 * @param {Object} object The destination object.
 * @param {...Object} sources The source objects.
 * @param {Function} customizer The function to customize assigned values.
 * @returns {Object} Returns `object`.
 * @example
 *
 * function customizer(objValue, srcValue) {
 *   if (_.isArray(objValue)) {
 *     return objValue.concat(srcValue);
 *   }
 * }
 *
 * var object = { 'a': [1], 'b': [2] };
 * var other = { 'a': [3], 'b': [4] };
 *
 * _.mergeWith(object, other, customizer);
 * // => { 'a': [1, 3], 'b': [2, 4] }
 */
var mergeWith = createAssigner(function(object, source, srcIndex, customizer) {
  baseMerge(object, source, srcIndex, customizer);
});

module.exports = mergeWith;


/***/ }),

/***/ 50344:
/***/ ((module) => {

/**
 * This method returns `undefined`.
 *
 * @static
 * @memberOf _
 * @since 2.3.0
 * @category Util
 * @example
 *
 * _.times(2, _.noop);
 * // => [undefined, undefined]
 */
function noop() {
  // No operation performed.
}

module.exports = noop;


/***/ }),

/***/ 96001:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseProperty = __webpack_require__(50517),
    basePropertyDeep = __webpack_require__(10301),
    isKey = __webpack_require__(40318),
    toKey = __webpack_require__(37948);

/**
 * Creates a function that returns the value at `path` of a given object.
 *
 * @static
 * @memberOf _
 * @since 2.4.0
 * @category Util
 * @param {Array|string} path The path of the property to get.
 * @returns {Function} Returns the new accessor function.
 * @example
 *
 * var objects = [
 *   { 'a': { 'b': 2 } },
 *   { 'a': { 'b': 1 } }
 * ];
 *
 * _.map(objects, _.property('a.b'));
 * // => [2, 1]
 *
 * _.map(_.sortBy(objects, _.property(['a', 'b'])), 'a.b');
 * // => [1, 2]
 */
function property(path) {
  return isKey(path) ? baseProperty(toKey(path)) : basePropertyDeep(path);
}

module.exports = property;


/***/ }),

/***/ 90254:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var basePullAll = __webpack_require__(61933);

/**
 * This method is like `_.pull` except that it accepts an array of values to remove.
 *
 * **Note:** Unlike `_.difference`, this method mutates `array`.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Array
 * @param {Array} array The array to modify.
 * @param {Array} values The values to remove.
 * @returns {Array} Returns `array`.
 * @example
 *
 * var array = ['a', 'b', 'c', 'a', 'b', 'c'];
 *
 * _.pullAll(array, ['a', 'c']);
 * console.log(array);
 * // => ['b', 'b']
 */
function pullAll(array, values) {
  return (array && array.length && values && values.length)
    ? basePullAll(array, values)
    : array;
}

module.exports = pullAll;


/***/ }),

/***/ 34172:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var arrayReduce = __webpack_require__(6446),
    baseEach = __webpack_require__(52033),
    baseIteratee = __webpack_require__(89278),
    baseReduce = __webpack_require__(19356),
    isArray = __webpack_require__(19785);

/**
 * Reduces `collection` to a value which is the accumulated result of running
 * each element in `collection` thru `iteratee`, where each successive
 * invocation is supplied the return value of the previous. If `accumulator`
 * is not given, the first element of `collection` is used as the initial
 * value. The iteratee is invoked with four arguments:
 * (accumulator, value, index|key, collection).
 *
 * Many lodash methods are guarded to work as iteratees for methods like
 * `_.reduce`, `_.reduceRight`, and `_.transform`.
 *
 * The guarded methods are:
 * `assign`, `defaults`, `defaultsDeep`, `includes`, `merge`, `orderBy`,
 * and `sortBy`
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Collection
 * @param {Array|Object} collection The collection to iterate over.
 * @param {Function} [iteratee=_.identity] The function invoked per iteration.
 * @param {*} [accumulator] The initial value.
 * @returns {*} Returns the accumulated value.
 * @see _.reduceRight
 * @example
 *
 * _.reduce([1, 2], function(sum, n) {
 *   return sum + n;
 * }, 0);
 * // => 3
 *
 * _.reduce({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {
 *   (result[value] || (result[value] = [])).push(key);
 *   return result;
 * }, {});
 * // => { '1': ['a', 'c'], '2': ['b'] } (iteration order is not guaranteed)
 */
function reduce(collection, iteratee, accumulator) {
  var func = isArray(collection) ? arrayReduce : baseReduce,
      initAccum = arguments.length < 3;

  return func(collection, baseIteratee(iteratee, 4), accumulator, initAccum, baseEach);
}

module.exports = reduce;


/***/ }),

/***/ 95099:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseFlatten = __webpack_require__(23545),
    baseOrderBy = __webpack_require__(98497),
    baseRest = __webpack_require__(1197),
    isIterateeCall = __webpack_require__(57535);

/**
 * Creates an array of elements, sorted in ascending order by the results of
 * running each element in a collection thru each iteratee. This method
 * performs a stable sort, that is, it preserves the original sort order of
 * equal elements. The iteratees are invoked with one argument: (value).
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Collection
 * @param {Array|Object} collection The collection to iterate over.
 * @param {...(Function|Function[])} [iteratees=[_.identity]]
 *  The iteratees to sort by.
 * @returns {Array} Returns the new sorted array.
 * @example
 *
 * var users = [
 *   { 'user': 'fred',   'age': 48 },
 *   { 'user': 'barney', 'age': 36 },
 *   { 'user': 'fred',   'age': 30 },
 *   { 'user': 'barney', 'age': 34 }
 * ];
 *
 * _.sortBy(users, [function(o) { return o.user; }]);
 * // => objects for [['barney', 36], ['barney', 34], ['fred', 48], ['fred', 30]]
 *
 * _.sortBy(users, ['user', 'age']);
 * // => objects for [['barney', 34], ['barney', 36], ['fred', 30], ['fred', 48]]
 */
var sortBy = baseRest(function(collection, iteratees) {
  if (collection == null) {
    return [];
  }
  var length = iteratees.length;
  if (length > 1 && isIterateeCall(collection, iteratees[0], iteratees[1])) {
    iteratees = [];
  } else if (length > 2 && isIterateeCall(iteratees[0], iteratees[1], iteratees[2])) {
    iteratees = [iteratees[0]];
  }
  return baseOrderBy(collection, baseFlatten(iteratees, 1), []);
});

module.exports = sortBy;


/***/ }),

/***/ 41571:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseTimes = __webpack_require__(24701),
    castFunction = __webpack_require__(62079),
    toInteger = __webpack_require__(47991);

/** Used as references for various `Number` constants. */
var MAX_SAFE_INTEGER = 9007199254740991;

/** Used as references for the maximum length and index of an array. */
var MAX_ARRAY_LENGTH = 4294967295;

/* Built-in method references for those with the same name as other `lodash` methods. */
var nativeMin = Math.min;

/**
 * Invokes the iteratee `n` times, returning an array of the results of
 * each invocation. The iteratee is invoked with one argument; (index).
 *
 * @static
 * @since 0.1.0
 * @memberOf _
 * @category Util
 * @param {number} n The number of times to invoke `iteratee`.
 * @param {Function} [iteratee=_.identity] The function invoked per iteration.
 * @returns {Array} Returns the array of results.
 * @example
 *
 * _.times(3, String);
 * // => ['0', '1', '2']
 *
 *  _.times(4, _.constant(0));
 * // => [0, 0, 0, 0]
 */
function times(n, iteratee) {
  n = toInteger(n);
  if (n < 1 || n > MAX_SAFE_INTEGER) {
    return [];
  }
  var index = MAX_ARRAY_LENGTH,
      length = nativeMin(n, MAX_ARRAY_LENGTH);

  iteratee = castFunction(iteratee);
  n -= MAX_ARRAY_LENGTH;

  var result = baseTimes(length, iteratee);
  while (++index < n) {
    iteratee(index);
  }
  return result;
}

module.exports = times;


/***/ }),

/***/ 94919:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var toNumber = __webpack_require__(91936);

/** Used as references for various `Number` constants. */
var INFINITY = 1 / 0,
    MAX_INTEGER = 1.7976931348623157e+308;

/**
 * Converts `value` to a finite number.
 *
 * @static
 * @memberOf _
 * @since 4.12.0
 * @category Lang
 * @param {*} value The value to convert.
 * @returns {number} Returns the converted number.
 * @example
 *
 * _.toFinite(3.2);
 * // => 3.2
 *
 * _.toFinite(Number.MIN_VALUE);
 * // => 5e-324
 *
 * _.toFinite(Infinity);
 * // => 1.7976931348623157e+308
 *
 * _.toFinite('3.2');
 * // => 3.2
 */
function toFinite(value) {
  if (!value) {
    return value === 0 ? value : 0;
  }
  value = toNumber(value);
  if (value === INFINITY || value === -INFINITY) {
    var sign = (value < 0 ? -1 : 1);
    return sign * MAX_INTEGER;
  }
  return value === value ? value : 0;
}

module.exports = toFinite;


/***/ }),

/***/ 47991:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var toFinite = __webpack_require__(94919);

/**
 * Converts `value` to an integer.
 *
 * **Note:** This method is loosely based on
 * [`ToInteger`](http://www.ecma-international.org/ecma-262/7.0/#sec-tointeger).
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to convert.
 * @returns {number} Returns the converted integer.
 * @example
 *
 * _.toInteger(3.2);
 * // => 3
 *
 * _.toInteger(Number.MIN_VALUE);
 * // => 0
 *
 * _.toInteger(Infinity);
 * // => 1.7976931348623157e+308
 *
 * _.toInteger('3.2');
 * // => 3
 */
function toInteger(value) {
  var result = toFinite(value),
      remainder = result % 1;

  return result === result ? (remainder ? result - remainder : result) : 0;
}

module.exports = toInteger;


/***/ }),

/***/ 91936:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseTrim = __webpack_require__(74833),
    isObject = __webpack_require__(11611),
    isSymbol = __webpack_require__(55193);

/** Used as references for various `Number` constants. */
var NAN = 0 / 0;

/** Used to detect bad signed hexadecimal string values. */
var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;

/** Used to detect binary string values. */
var reIsBinary = /^0b[01]+$/i;

/** Used to detect octal string values. */
var reIsOctal = /^0o[0-7]+$/i;

/** Built-in method references without a dependency on `root`. */
var freeParseInt = parseInt;

/**
 * Converts `value` to a number.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to process.
 * @returns {number} Returns the number.
 * @example
 *
 * _.toNumber(3.2);
 * // => 3.2
 *
 * _.toNumber(Number.MIN_VALUE);
 * // => 5e-324
 *
 * _.toNumber(Infinity);
 * // => Infinity
 *
 * _.toNumber('3.2');
 * // => 3.2
 */
function toNumber(value) {
  if (typeof value == 'number') {
    return value;
  }
  if (isSymbol(value)) {
    return NAN;
  }
  if (isObject(value)) {
    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;
    value = isObject(other) ? (other + '') : other;
  }
  if (typeof value != 'string') {
    return value === 0 ? value : +value;
  }
  value = baseTrim(value);
  var isBinary = reIsBinary.test(value);
  return (isBinary || reIsOctal.test(value))
    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)
    : (reIsBadHex.test(value) ? NAN : +value);
}

module.exports = toNumber;


/***/ }),

/***/ 89328:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var copyObject = __webpack_require__(35159),
    keysIn = __webpack_require__(53893);

/**
 * Converts `value` to a plain object flattening inherited enumerable string
 * keyed properties of `value` to own properties of the plain object.
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category Lang
 * @param {*} value The value to convert.
 * @returns {Object} Returns the converted plain object.
 * @example
 *
 * function Foo() {
 *   this.b = 2;
 * }
 *
 * Foo.prototype.c = 3;
 *
 * _.assign({ 'a': 1 }, new Foo);
 * // => { 'a': 1, 'b': 2 }
 *
 * _.assign({ 'a': 1 }, _.toPlainObject(new Foo));
 * // => { 'a': 1, 'b': 2, 'c': 3 }
 */
function toPlainObject(value) {
  return copyObject(value, keysIn(value));
}

module.exports = toPlainObject;


/***/ }),

/***/ 42673:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var arrayEach = __webpack_require__(80594),
    baseCreate = __webpack_require__(80158),
    baseForOwn = __webpack_require__(26194),
    baseIteratee = __webpack_require__(89278),
    getPrototype = __webpack_require__(2173),
    isArray = __webpack_require__(19785),
    isBuffer = __webpack_require__(43854),
    isFunction = __webpack_require__(28338),
    isObject = __webpack_require__(11611),
    isTypedArray = __webpack_require__(48519);

/**
 * An alternative to `_.reduce`; this method transforms `object` to a new
 * `accumulator` object which is the result of running each of its own
 * enumerable string keyed properties thru `iteratee`, with each invocation
 * potentially mutating the `accumulator` object. If `accumulator` is not
 * provided, a new object with the same `[[Prototype]]` will be used. The
 * iteratee is invoked with four arguments: (accumulator, value, key, object).
 * Iteratee functions may exit iteration early by explicitly returning `false`.
 *
 * @static
 * @memberOf _
 * @since 1.3.0
 * @category Object
 * @param {Object} object The object to iterate over.
 * @param {Function} [iteratee=_.identity] The function invoked per iteration.
 * @param {*} [accumulator] The custom accumulator value.
 * @returns {*} Returns the accumulated value.
 * @example
 *
 * _.transform([2, 3, 4], function(result, n) {
 *   result.push(n *= n);
 *   return n % 2 == 0;
 * }, []);
 * // => [4, 9]
 *
 * _.transform({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {
 *   (result[value] || (result[value] = [])).push(key);
 * }, {});
 * // => { '1': ['a', 'c'], '2': ['b'] }
 */
function transform(object, iteratee, accumulator) {
  var isArr = isArray(object),
      isArrLike = isArr || isBuffer(object) || isTypedArray(object);

  iteratee = baseIteratee(iteratee, 4);
  if (accumulator == null) {
    var Ctor = object && object.constructor;
    if (isArrLike) {
      accumulator = isArr ? new Ctor : [];
    }
    else if (isObject(object)) {
      accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};
    }
    else {
      accumulator = {};
    }
  }
  (isArrLike ? arrayEach : baseForOwn)(object, function(value, index, object) {
    return iteratee(accumulator, value, index, object);
  });
  return accumulator;
}

module.exports = transform;


/***/ }),

/***/ 68946:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseFlatten = __webpack_require__(23545),
    baseRest = __webpack_require__(1197),
    baseUniq = __webpack_require__(92198),
    isArrayLikeObject = __webpack_require__(36468);

/**
 * Creates an array of unique values, in order, from all given arrays using
 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * for equality comparisons.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Array
 * @param {...Array} [arrays] The arrays to inspect.
 * @returns {Array} Returns the new array of combined values.
 * @example
 *
 * _.union([2], [1, 2]);
 * // => [2, 1]
 */
var union = baseRest(function(arrays) {
  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true));
});

module.exports = union;


/***/ }),

/***/ 83766:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseUniq = __webpack_require__(92198);

/**
 * Creates a duplicate-free version of an array, using
 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * for equality comparisons, in which only the first occurrence of each element
 * is kept. The order of result values is determined by the order they occur
 * in the array.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Array
 * @param {Array} array The array to inspect.
 * @returns {Array} Returns the new duplicate free array.
 * @example
 *
 * _.uniq([2, 1, 2]);
 * // => [2, 1]
 */
function uniq(array) {
  return (array && array.length) ? baseUniq(array) : [];
}

module.exports = uniq;


/***/ }),

/***/ 52929:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseUniq = __webpack_require__(92198);

/**
 * This method is like `_.uniq` except that it accepts `comparator` which
 * is invoked to compare elements of `array`. The order of result values is
 * determined by the order they occur in the array.The comparator is invoked
 * with two arguments: (arrVal, othVal).
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Array
 * @param {Array} array The array to inspect.
 * @param {Function} [comparator] The comparator invoked per element.
 * @returns {Array} Returns the new duplicate free array.
 * @example
 *
 * var objects = [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }, { 'x': 1, 'y': 2 }];
 *
 * _.uniqWith(objects, _.isEqual);
 * // => [{ 'x': 1, 'y': 2 }, { 'x': 2, 'y': 1 }]
 */
function uniqWith(array, comparator) {
  comparator = typeof comparator == 'function' ? comparator : undefined;
  return (array && array.length) ? baseUniq(array, undefined, comparator) : [];
}

module.exports = uniqWith;


/***/ }),

/***/ 81889:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var baseDifference = __webpack_require__(11005),
    baseRest = __webpack_require__(1197),
    isArrayLikeObject = __webpack_require__(36468);

/**
 * Creates an array excluding all given values using
 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * for equality comparisons.
 *
 * **Note:** Unlike `_.pull`, this method returns a new array.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Array
 * @param {Array} array The array to inspect.
 * @param {...*} [values] The values to exclude.
 * @returns {Array} Returns the new array of filtered values.
 * @see _.difference, _.xor
 * @example
 *
 * _.without([2, 1, 2, 3], 1, 2);
 * // => [3]
 */
var without = baseRest(function(array, values) {
  return isArrayLikeObject(array)
    ? baseDifference(array, values)
    : [];
});

module.exports = without;


/***/ }),

/***/ 58702:
/***/ ((__unused_webpack_module, exports) => {

"use strict";
/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var b=Symbol.for("react.element"),c=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),e=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),g=Symbol.for("react.provider"),h=Symbol.for("react.context"),k=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),n=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),t=Symbol.for("react.offscreen"),u;u=Symbol.for("react.module.reference");
function v(a){if("object"===typeof a&&null!==a){var r=a.$$typeof;switch(r){case b:switch(a=a.type,a){case d:case f:case e:case m:case n:return a;default:switch(a=a&&a.$$typeof,a){case k:case h:case l:case q:case p:case g:return a;default:return r}}case c:return r}}}exports.ContextConsumer=h;exports.ContextProvider=g;exports.Element=b;exports.ForwardRef=l;exports.Fragment=d;exports.Lazy=q;exports.Memo=p;exports.Portal=c;exports.Profiler=f;exports.StrictMode=e;exports.Suspense=m;
exports.SuspenseList=n;exports.isAsyncMode=function(){return!1};exports.isConcurrentMode=function(){return!1};exports.isContextConsumer=function(a){return v(a)===h};exports.isContextProvider=function(a){return v(a)===g};exports.isElement=function(a){return"object"===typeof a&&null!==a&&a.$$typeof===b};exports.isForwardRef=function(a){return v(a)===l};exports.isFragment=function(a){return v(a)===d};exports.isLazy=function(a){return v(a)===q};exports.isMemo=function(a){return v(a)===p};
exports.isPortal=function(a){return v(a)===c};exports.isProfiler=function(a){return v(a)===f};exports.isStrictMode=function(a){return v(a)===e};exports.isSuspense=function(a){return v(a)===m};exports.isSuspenseList=function(a){return v(a)===n};
exports.isValidElementType=function(a){return"string"===typeof a||"function"===typeof a||a===d||a===f||a===e||a===m||a===n||a===t||"object"===typeof a&&null!==a&&(a.$$typeof===q||a.$$typeof===p||a.$$typeof===g||a.$$typeof===h||a.$$typeof===l||a.$$typeof===u||void 0!==a.getModuleId)?!0:!1};exports.typeOf=v;


/***/ }),

/***/ 19185:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";


if (true) {
  module.exports = __webpack_require__(58702);
} else {}


/***/ }),

/***/ 17604:
/***/ ((module) => {

"use strict";


/**
* FUNCTION: isArray( value )
*	Validates if a value is an array.
*
* @param {*} value - value to be validated
* @returns {Boolean} boolean indicating whether value is an array
*/
function isArray( value ) {
	return Object.prototype.toString.call( value ) === '[object Array]';
} // end FUNCTION isArray()

// EXPORTS //

module.exports = Array.isArray || isArray;


/***/ }),

/***/ 86401:
/***/ ((module) => {

"use strict";
/**
*
*	VALIDATE: function
*
*
*	DESCRIPTION:
*		- Validates if a value is a function.
*
*
*	NOTES:
*		[1]
*
*
*	TODO:
*		[1]
*
*
*	LICENSE:
*		MIT
*
*	Copyright (c) 2014. Athan Reines.
*
*
*	AUTHOR:
*		Athan Reines. <EMAIL>. 2014.
*
*/



/**
* FUNCTION: isFunction( value )
*	Validates if a value is a function.
*
* @param {*} value - value to be validated
* @returns {Boolean} boolean indicating whether value is a function
*/
function isFunction( value ) {
	return ( typeof value === 'function' );
} // end FUNCTION isFunction()


// EXPORTS //

module.exports = isFunction;


/***/ }),

/***/ 9333:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
/**
*
*	VALIDATE: integer-array
*
*
*	DESCRIPTION:
*		- Validates if a value is an integer array.
*
*
*	NOTES:
*		[1]
*
*
*	TODO:
*		[1]
*
*
*	LICENSE:
*		MIT
*
*	Copyright (c) 2015. Athan Reines.
*
*
*	AUTHOR:
*		Athan Reines. <EMAIL>. 2015.
*
*/



// MODULES //

var isArray = __webpack_require__( 17604 ),
	isInteger = __webpack_require__( 50459 );


// IS INTEGER ARRAY //

/**
* FUNCTION: isIntegerArray( value )
*	Validates if a value is an integer array.
*
* @param {*} value - value to be validated
* @returns {Boolean} boolean indicating if a value is an integer array
*/
function isIntegerArray( value ) {
	var len;
	if ( !isArray( value ) ) {
		return false;
	}
	len = value.length;
	if ( !len ) {
		return false;
	}
	for ( var i = 0; i < len; i++ ) {
		if ( !isInteger( value[i] ) ) {
			return false;
		}
	}
	return true;
} // end FUNCTION isIntegerArray()


// EXPORTS //

module.exports = isIntegerArray;


/***/ }),

/***/ 50459:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
/**
*
*	VALIDATE: integer
*
*
*	DESCRIPTION:
*		- Validates if a value is an integer.
*
*
*	NOTES:
*		[1]
*
*
*	TODO:
*		[1]
*
*
*	LICENSE:
*		MIT
*
*	Copyright (c) 2014. Athan Reines.
*
*
*	AUTHOR:
*		Athan Reines. <EMAIL>. 2014.
*
*/



// MODULES //

var isNumber = __webpack_require__( 7365 );


// ISINTEGER //

/**
* FUNCTION: isInteger( value )
*	Validates if a value is an integer.
*
* @param {Number} value - value to be validated
* @returns {Boolean} boolean indicating whether value is an integer
*/
function isInteger( value ) {
	return isNumber( value ) && value%1 === 0;
} // end FUNCTION isInteger()


// EXPORTS //

module.exports = isInteger;


/***/ }),

/***/ 7365:
/***/ ((module) => {

"use strict";
/**
*
*	VALIDATE: number
*
*
*	DESCRIPTION:
*		- Validates if a value is a number.
*
*
*	NOTES:
*		[1]
*
*
*	TODO:
*		[1]
*
*
*	LICENSE:
*		MIT
*
*	Copyright (c) 2014. Athan Reines.
*
*
*	AUTHOR:
*		Athan Reines. <EMAIL>. 2014.
*
*/



/**
* FUNCTION: isNumber( value )
*	Validates if a value is a number.
*
* @param {*} value - value to be validated
* @returns {Boolean} boolean indicating whether value is a number
*/
function isNumber( value ) {
	return ( typeof value === 'number' || Object.prototype.toString.call( value ) === '[object Number]' ) && value.valueOf() === value.valueOf();
} // end FUNCTION isNumber()


// EXPORTS //

module.exports = isNumber;


/***/ })

}]);
//# sourceMappingURL=7995.45be6443b704da1daafc.js.map?v=45be6443b704da1daafc