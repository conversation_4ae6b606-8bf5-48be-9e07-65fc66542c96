from __future__ import annotations
import sys
from importlib.machinery import ModuleSpec, PathFinder
from importlib.machinery import all_suffixes as module_suffixes
from importlib.util import spec_from_file_location
from itertools import chain
from pathlib import Path

MAPPING: dict[str, str] = {'nerfstudio': 'C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio'}
NAMESPACES: dict[str, list[str]] = {'nerfstudio.scripts.benchmarking': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\scripts\\benchmarking'], 'nerfstudio.scripts.datasets': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\scripts\\datasets'], 'nerfstudio.scripts.licensing': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\scripts\\licensing'], 'nerfstudio.scripts.maya': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\scripts\\maya'], 'nerfstudio.viewer_legacy.app': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app'], 'nerfstudio.viewer_legacy.app.public': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\public'], 'nerfstudio.viewer_legacy.app.src': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src'], 'nerfstudio.viewer_legacy.app.src.modules': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules'], 'nerfstudio.viewer_legacy.app.src.themes': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\themes'], 'nerfstudio.viewer_legacy.app.src.modules.Banner': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\Banner'], 'nerfstudio.viewer_legacy.app.src.modules.ConfigPanel': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\ConfigPanel'], 'nerfstudio.viewer_legacy.app.src.modules.LandingModal': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\LandingModal'], 'nerfstudio.viewer_legacy.app.src.modules.LoadPathModal': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\LoadPathModal'], 'nerfstudio.viewer_legacy.app.src.modules.RenderModal': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\RenderModal'], 'nerfstudio.viewer_legacy.app.src.modules.Scene': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\Scene'], 'nerfstudio.viewer_legacy.app.src.modules.SidePanel': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\SidePanel'], 'nerfstudio.viewer_legacy.app.src.modules.ViewerWindow': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\ViewerWindow'], 'nerfstudio.viewer_legacy.app.src.modules.ViewportControlsModal': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\ViewportControlsModal'], 'nerfstudio.viewer_legacy.app.src.modules.WebSocket': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\WebSocket'], 'nerfstudio.viewer_legacy.app.src.modules.SidePanel.CameraPanel': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\SidePanel\\CameraPanel'], 'nerfstudio.viewer_legacy.app.src.modules.SidePanel.ExportPanel': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\SidePanel\\ExportPanel'], 'nerfstudio.viewer_legacy.app.src.modules.SidePanel.ScenePanel': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\SidePanel\\ScenePanel'], 'nerfstudio.viewer_legacy.app.src.modules.SidePanel.StatusPanel': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\app\\src\\modules\\SidePanel\\StatusPanel'], 'nerfstudio.viewer_legacy.server.state': ['C:\\Users\\<USER>\\Downloads\\3DReconstuction\\nerfstudio-main\\nerfstudio-main\\nerfstudio\\viewer_legacy\\server\\state']}
PATH_PLACEHOLDER = '__editable__.nerfstudio-1.1.5.finder' + ".__path_hook__"


class _EditableFinder:  # MetaPathFinder
    @classmethod
    def find_spec(cls, fullname: str, path=None, target=None) -> ModuleSpec | None:  # type: ignore
        # Top-level packages and modules (we know these exist in the FS)
        if fullname in MAPPING:
            pkg_path = MAPPING[fullname]
            return cls._find_spec(fullname, Path(pkg_path))

        # Handle immediate children modules (required for namespaces to work)
        # To avoid problems with case sensitivity in the file system we delegate
        # to the importlib.machinery implementation.
        parent, _, child = fullname.rpartition(".")
        if parent and parent in MAPPING:
            return PathFinder.find_spec(fullname, path=[MAPPING[parent]])

        # Other levels of nesting should be handled automatically by importlib
        # using the parent path.
        return None

    @classmethod
    def _find_spec(cls, fullname: str, candidate_path: Path) -> ModuleSpec | None:
        init = candidate_path / "__init__.py"
        candidates = (candidate_path.with_suffix(x) for x in module_suffixes())
        for candidate in chain([init], candidates):
            if candidate.exists():
                return spec_from_file_location(fullname, candidate)
        return None


class _EditableNamespaceFinder:  # PathEntryFinder
    @classmethod
    def _path_hook(cls, path) -> type[_EditableNamespaceFinder]:
        if path == PATH_PLACEHOLDER:
            return cls
        raise ImportError

    @classmethod
    def _paths(cls, fullname: str) -> list[str]:
        paths = NAMESPACES[fullname]
        if not paths and fullname in MAPPING:
            paths = [MAPPING[fullname]]
        # Always add placeholder, for 2 reasons:
        # 1. __path__ cannot be empty for the spec to be considered namespace.
        # 2. In the case of nested namespaces, we need to force
        #    import machinery to query _EditableNamespaceFinder again.
        return [*paths, PATH_PLACEHOLDER]

    @classmethod
    def find_spec(cls, fullname: str, target=None) -> ModuleSpec | None:  # type: ignore
        if fullname in NAMESPACES:
            spec = ModuleSpec(fullname, None, is_package=True)
            spec.submodule_search_locations = cls._paths(fullname)
            return spec
        return None

    @classmethod
    def find_module(cls, _fullname) -> None:
        return None


def install():
    if not any(finder == _EditableFinder for finder in sys.meta_path):
        sys.meta_path.append(_EditableFinder)

    if not NAMESPACES:
        return

    if not any(hook == _EditableNamespaceFinder._path_hook for hook in sys.path_hooks):
        # PathEntryFinder is needed to create NamespaceSpec without private APIS
        sys.path_hooks.append(_EditableNamespaceFinder._path_hook)
    if PATH_PLACEHOLDER not in sys.path:
        sys.path.append(PATH_PLACEHOLDER)  # Used just to trigger the path hook
