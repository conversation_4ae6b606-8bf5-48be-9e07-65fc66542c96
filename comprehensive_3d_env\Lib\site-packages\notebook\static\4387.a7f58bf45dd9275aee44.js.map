{"version": 3, "file": "4387.a7f58bf45dd9275aee44.js?v=a7f58bf45dd9275aee44", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU,QAAQ;AAClB;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM,uBAAuB;AAC7B,2BAA2B;AAC3B,MAAM,eAAe;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN,cAAc;AACd;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/cypher.js"], "sourcesContent": ["var wordRegexp = function(words) {\n  return new RegExp(\"^(?:\" + words.join(\"|\") + \")$\", \"i\");\n};\n\nvar tokenBase = function(stream/*, state*/) {\n  curPunc = null;\n  var ch = stream.next();\n  if (ch ==='\"') {\n    stream.match(/^.*?\"/);\n    return \"string\";\n  }\n  if (ch === \"'\") {\n    stream.match(/^.*?'/);\n    return \"string\";\n  }\n  if (/[{}\\(\\),\\.;\\[\\]]/.test(ch)) {\n    curPunc = ch;\n    return \"punctuation\";\n  } else if (ch === \"/\" && stream.eat(\"/\")) {\n    stream.skipToEnd();\n    return \"comment\";\n  } else if (operatorChars.test(ch)) {\n    stream.eatWhile(operatorChars);\n    return null;\n  } else {\n    stream.eatWhile(/[_\\w\\d]/);\n    if (stream.eat(\":\")) {\n      stream.eatWhile(/[\\w\\d_\\-]/);\n      return \"atom\";\n    }\n    var word = stream.current();\n    if (funcs.test(word)) return \"builtin\";\n    if (preds.test(word)) return \"def\";\n    if (keywords.test(word) || systemKeywords.test(word)) return \"keyword\";\n    return \"variable\";\n  }\n};\nvar pushContext = function(state, type, col) {\n  return state.context = {\n    prev: state.context,\n    indent: state.indent,\n    col: col,\n    type: type\n  };\n};\nvar popContext = function(state) {\n  state.indent = state.context.indent;\n  return state.context = state.context.prev;\n};\nvar curPunc;\nvar funcs = wordRegexp([\"abs\", \"acos\", \"allShortestPaths\", \"asin\", \"atan\", \"atan2\", \"avg\", \"ceil\", \"coalesce\", \"collect\", \"cos\", \"cot\", \"count\", \"degrees\", \"e\", \"endnode\", \"exp\", \"extract\", \"filter\", \"floor\", \"haversin\", \"head\", \"id\", \"keys\", \"labels\", \"last\", \"left\", \"length\", \"log\", \"log10\", \"lower\", \"ltrim\", \"max\", \"min\", \"node\", \"nodes\", \"percentileCont\", \"percentileDisc\", \"pi\", \"radians\", \"rand\", \"range\", \"reduce\", \"rel\", \"relationship\", \"relationships\", \"replace\", \"reverse\", \"right\", \"round\", \"rtrim\", \"shortestPath\", \"sign\", \"sin\", \"size\", \"split\", \"sqrt\", \"startnode\", \"stdev\", \"stdevp\", \"str\", \"substring\", \"sum\", \"tail\", \"tan\", \"timestamp\", \"toFloat\", \"toInt\", \"toString\", \"trim\", \"type\", \"upper\"]);\nvar preds = wordRegexp([\"all\", \"and\", \"any\", \"contains\", \"exists\", \"has\", \"in\", \"none\", \"not\", \"or\", \"single\", \"xor\"]);\nvar keywords = wordRegexp([\"as\", \"asc\", \"ascending\", \"assert\", \"by\", \"case\", \"commit\", \"constraint\", \"create\", \"csv\", \"cypher\", \"delete\", \"desc\", \"descending\", \"detach\", \"distinct\", \"drop\", \"else\", \"end\", \"ends\", \"explain\", \"false\", \"fieldterminator\", \"foreach\", \"from\", \"headers\", \"in\", \"index\", \"is\", \"join\", \"limit\", \"load\", \"match\", \"merge\", \"null\", \"on\", \"optional\", \"order\", \"periodic\", \"profile\", \"remove\", \"return\", \"scan\", \"set\", \"skip\", \"start\", \"starts\", \"then\", \"true\", \"union\", \"unique\", \"unwind\", \"using\", \"when\", \"where\", \"with\", \"call\", \"yield\"]);\nvar systemKeywords = wordRegexp([\"access\", \"active\", \"assign\", \"all\", \"alter\", \"as\", \"catalog\", \"change\", \"copy\", \"create\", \"constraint\", \"constraints\", \"current\", \"database\", \"databases\", \"dbms\", \"default\", \"deny\", \"drop\", \"element\", \"elements\", \"exists\", \"from\", \"grant\", \"graph\", \"graphs\", \"if\", \"index\", \"indexes\", \"label\", \"labels\", \"management\", \"match\", \"name\", \"names\", \"new\", \"node\", \"nodes\", \"not\", \"of\", \"on\", \"or\", \"password\", \"populated\", \"privileges\", \"property\", \"read\", \"relationship\", \"relationships\", \"remove\", \"replace\", \"required\", \"revoke\", \"role\", \"roles\", \"set\", \"show\", \"start\", \"status\", \"stop\", \"suspended\", \"to\", \"traverse\", \"type\", \"types\", \"user\", \"users\", \"with\", \"write\"]);\nvar operatorChars = /[*+\\-<>=&|~%^]/;\n\nexport const cypher = {\n  name: \"cypher\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      context: null,\n      indent: 0,\n      col: 0\n    };\n  },\n  token: function(stream, state) {\n    if (stream.sol()) {\n      if (state.context && (state.context.align == null)) {\n        state.context.align = false;\n      }\n      state.indent = stream.indentation();\n    }\n    if (stream.eatSpace()) {\n      return null;\n    }\n    var style = state.tokenize(stream, state);\n    if (style !== \"comment\" && state.context && (state.context.align == null) && state.context.type !== \"pattern\") {\n      state.context.align = true;\n    }\n    if (curPunc === \"(\") {\n      pushContext(state, \")\", stream.column());\n    } else if (curPunc === \"[\") {\n      pushContext(state, \"]\", stream.column());\n    } else if (curPunc === \"{\") {\n      pushContext(state, \"}\", stream.column());\n    } else if (/[\\]\\}\\)]/.test(curPunc)) {\n      while (state.context && state.context.type === \"pattern\") {\n        popContext(state);\n      }\n      if (state.context && curPunc === state.context.type) {\n        popContext(state);\n      }\n    } else if (curPunc === \".\" && state.context && state.context.type === \"pattern\") {\n      popContext(state);\n    } else if (/atom|string|variable/.test(style) && state.context) {\n      if (/[\\}\\]]/.test(state.context.type)) {\n        pushContext(state, \"pattern\", stream.column());\n      } else if (state.context.type === \"pattern\" && !state.context.align) {\n        state.context.align = true;\n        state.context.col = stream.column();\n      }\n    }\n    return style;\n  },\n  indent: function(state, textAfter, cx) {\n    var firstChar = textAfter && textAfter.charAt(0);\n    var context = state.context;\n    if (/[\\]\\}]/.test(firstChar)) {\n      while (context && context.type === \"pattern\") {\n        context = context.prev;\n      }\n    }\n    var closing = context && firstChar === context.type;\n    if (!context) return 0;\n    if (context.type === \"keywords\") return null\n    if (context.align) return context.col + (closing ? 0 : 1);\n    return context.indent + (closing ? 0 : cx.unit);\n  }\n};\n"], "names": [], "sourceRoot": ""}