#usda 1.0
(
    customLayerData = {
        string creator = "Dashcam 3D Reconstruction Pipeline"
        string omniverse_version = "Kit 107.3+"
        bool isaac_sim_compatible = true
        bool ray_tracing_enabled = true
    }
    defaultPrim = "World"
    metersPerUnit = 1
    upAxis = "Y"
)

def Xform "World"
{
    def Mesh "DashcamReconstruction"
    {
        int[] faceVertexCounts = [3, 3, 3, 3]
        int[] faceVertexIndices = [0, 1, 2, 0, 2, 3, 4, 5, 6, 4, 6, 7]
        point3f[] points = [(-2, 0, -2), (2, 0, -2), (2, 0, 2), (-2, 0, 2), (-1, 1, -1), (1, 1, -1), (1, 1, 1), (-1, 1, 1)]
        color3f[] primvars:displayColor = [(0.8, 0.6, 0.4), (0.7, 0.8, 0.5), (0.6, 0.7, 0.9), (0.9, 0.7, 0.6)]
        
        # Omniverse-specific attributes
        bool omni:kit:raytracing:enabled = true
        string omni:kit:material:type = "MDL"
    }
    
    def Camera "DashcamView"
    {
        float focalLength = 35
        float horizontalAperture = 36
        float verticalAperture = 24
        matrix4d xformOp:transform = ((1, 0, 0, 0), (0, 0.866, -0.5, 0), (0, 0.5, 0.866, 0), (0, 2, -5, 1))
    }
}
