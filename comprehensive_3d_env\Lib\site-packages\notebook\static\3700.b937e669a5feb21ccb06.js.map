{"version": 3, "file": "3700.b937e669a5feb21ccb06.js?v=b937e669a5feb21ccb06", "mappings": ";;;;;;;;;;;AAAA;AACA,wGAAwG,KAAK,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,KAAK,GAAG,GAAG;AACnJ;;AAEO;AACP;AACA;AACA;AACA,2CAA2C;AAC3C,sCAAsC;AACtC;AACA,qCAAqC;AACrC;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA,oDAAoD;AACpD,4CAA4C;AAC5C,2DAA2D;;AAEpD;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,qBAAqB,oBAAoB;;AAEzC;AACA,oCAAoC;AACpC,mCAAmC;AACnC;;AAEA,kCAAkC,gBAAgB;AAClD,2BAA2B,KAAK,MAAM,gBAAgB,qBAAqB,eAAe;;AAE1F,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA,qCAAqC;AACrC,oCAAoC;AACpC,0BAA0B;AAC1B;;AAEA;AACA,0BAA0B;AAC1B;AACA;;AAEA;AACA;AACA,+BAA+B;AAC/B;AACA;AACA;AACA,kCAAkC;AAClC;AACA;AACA;AACA;AACA;;AAEA,sBAAsB,eAAe;AACrC,0BAA0B,iBAAiB;AAC3C,0BAA0B;AAC1B;AACA;;AAEA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/rpm.js"], "sourcesContent": ["var headerSeparator = /^-+$/;\nvar headerLine = /^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)  ?\\d{1,2} \\d{2}:\\d{2}(:\\d{2})? [A-Z]{3,4} \\d{4} - /;\nvar simpleEmail = /^[\\w+.-]+@[\\w.-]+/;\n\nexport const rpmChanges = {\n  name: \"rpmchanges\",\n  token: function(stream) {\n    if (stream.sol()) {\n      if (stream.match(headerSeparator)) { return 'tag'; }\n      if (stream.match(headerLine)) { return 'tag'; }\n    }\n    if (stream.match(simpleEmail)) { return 'string'; }\n    stream.next();\n    return null;\n  }\n}\n\n// Quick and dirty spec file highlighting\n\nvar arch = /^(i386|i586|i686|x86_64|ppc64le|ppc64|ppc|ia64|s390x|s390|sparc64|sparcv9|sparc|noarch|alphaev6|alpha|hppa|mipsel)/;\n\nvar preamble = /^[a-zA-Z0-9()]+:/;\nvar section = /^%(debug_package|package|description|prep|build|install|files|clean|changelog|preinstall|preun|postinstall|postun|pretrans|posttrans|pre|post|triggerin|triggerun|verifyscript|check|triggerpostun|triggerprein|trigger)/;\nvar control_flow_complex = /^%(ifnarch|ifarch|if)/; // rpm control flow macros\nvar control_flow_simple = /^%(else|endif)/; // rpm control flow macros\nvar operators = /^(\\!|\\?|\\<\\=|\\<|\\>\\=|\\>|\\=\\=|\\&\\&|\\|\\|)/; // operators in control flow macros\n\nexport const rpmSpec = {\n  name: \"rpmspec\",\n  startState: function () {\n    return {\n      controlFlow: false,\n      macroParameters: false,\n      section: false\n    };\n  },\n  token: function (stream, state) {\n    var ch = stream.peek();\n    if (ch == \"#\") { stream.skipToEnd(); return \"comment\"; }\n\n    if (stream.sol()) {\n      if (stream.match(preamble)) { return \"header\"; }\n      if (stream.match(section)) { return \"atom\"; }\n    }\n\n    if (stream.match(/^\\$\\w+/)) { return \"def\"; } // Variables like '$RPM_BUILD_ROOT'\n    if (stream.match(/^\\$\\{\\w+\\}/)) { return \"def\"; } // Variables like '${RPM_BUILD_ROOT}'\n\n    if (stream.match(control_flow_simple)) { return \"keyword\"; }\n    if (stream.match(control_flow_complex)) {\n      state.controlFlow = true;\n      return \"keyword\";\n    }\n    if (state.controlFlow) {\n      if (stream.match(operators)) { return \"operator\"; }\n      if (stream.match(/^(\\d+)/)) { return \"number\"; }\n      if (stream.eol()) { state.controlFlow = false; }\n    }\n\n    if (stream.match(arch)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"number\";\n    }\n\n    // Macros like '%make_install' or '%attr(0775,root,root)'\n    if (stream.match(/^%[\\w]+/)) {\n      if (stream.match('(')) { state.macroParameters = true; }\n      return \"keyword\";\n    }\n    if (state.macroParameters) {\n      if (stream.match(/^\\d+/)) { return \"number\";}\n      if (stream.match(')')) {\n        state.macroParameters = false;\n        return \"keyword\";\n      }\n    }\n\n    // Macros like '%{defined fedora}'\n    if (stream.match(/^%\\{\\??[\\w \\-\\:\\!]+\\}/)) {\n      if (stream.eol()) { state.controlFlow = false; }\n      return \"def\";\n    }\n\n    stream.next();\n    return null;\n  }\n};\n\n"], "names": [], "sourceRoot": ""}