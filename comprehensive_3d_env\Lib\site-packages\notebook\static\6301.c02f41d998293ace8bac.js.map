{"version": 3, "file": "6301.c02f41d998293ace8bac.js?v=c02f41d998293ace8bac", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAI8B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AAOA;;AAE9B;AAC4B;AAC2C;AACT;;AAE9D;AACsC;;AAEtC;AACA;AACA,0BAA0B,sEAAM;AAChC;AACA,CAAC;AACD,0BAA0B,sEAAM;AAChC,2BAA2B,sEAAM;AACjC,2BAA2B,sEAAM;AACjC;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,qCAAqC,sEAAM,kEAAkE,0EAAS,8BAA8B,0EAAS,mBAAmB,0EAAS,8BAA8B,0EAAS,mBAAmB,0EAAS;AAC5P,kCAAkC,sEAAM,2FAA2F,0EAAS,yDAAyD,0EAAS;AAC9M,sCAAsC,sEAAM;AAC5C,+CAA+C,0EAAS,4BAA4B,0EAAS,0BAA0B,0EAAS,oCAAoC,0EAAS;AAC7K;AACA,6CAA6C,0EAAS,4BAA4B,0EAAS,qDAAqD,0EAAS,uDAAuD,0EAAS,6BAA6B,0EAAS;AAC/P;AACA,CAAC;AACD,qCAAqC,sEAAM;AAC3C,mCAAmC,sEAAM;AACzC,uDAAuD,0EAAS;AAChE;AACA,uBAAuB,0EAAS;AAChC;AACA,GAAG;AACH,+CAA+C,0EAAS,4BAA4B,0EAAS,4BAA4B,0EAAS,oCAAoC,0EAAS;AAC/K;AACA;AACA,iDAAiD,0EAAS;AAC1D;AACA,kBAAkB,0EAAS,yBAAyB,0EAAS,yBAAyB,0EAAS;AAC/F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH,gDAAgD,0EAAS,6BAA6B,0EAAS,iCAAiC,0EAAS,uCAAuC,0EAAS,iCAAiC,0EAAS;AACnO;AACA;AACA,mCAAmC,0EAAS;AAC5C,6CAA6C,0EAAS,4BAA4B,0EAAS,4CAA4C,0EAAS,qEAAqE,0EAAS,6BAA6B,0EAAS;AACpQ;AACA,CAAC;AACD,qCAAqC,sEAAM;AAC3C,cAAc,0EAAS;AACvB,qBAAqB,0EAAS;AAC9B;AACA;AACA;AACA,wDAAwD,0EAAS,uCAAuC,0EAAS;AACjH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,0EAAS;AAC7B;AACA;AACA,sBAAsB,0EAAS,sBAAsB,0EAAS;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,0EAAS,sBAAsB,0EAAS,sBAAsB,0EAAS;AAC3E,wCAAwC,0EAAS,oCAAoC,0EAAS;AAC9F;AACA;AACA,IAAI,0EAAS,sBAAsB,0EAAS,sBAAsB,0EAAS;AAC3E,kEAAkE,0EAAS,gCAAgC,0EAAS;AACpH;AACA,CAAC;AACD,mCAAmC,sEAAM;AACzC,gEAAgE,0EAAS,oBAAoB,0EAAS;AACtG;AACA,IAAI,0EAAS,mBAAmB,0EAAS,oBAAoB,0EAAS;AACtE;AACA;AACA,IAAI,0EAAS,mBAAmB,0EAAS,oBAAoB,0EAAS;AACtE;AACA,uEAAuE,0EAAS,8BAA8B,0EAAS,mBAAmB,0EAAS,kCAAkC,0EAAS,mBAAmB,0EAAS;AAC1N,CAAC;AACD,wCAAwC,sEAAM;AAC9C,cAAc,0EAAS;AACvB,eAAe,0EAAS;AACxB;AACA;AACA;AACA;AACA;AACA,gIAAgI,0EAAS,4BAA4B,0EAAS;AAC9K,CAAC;AACD,oCAAoC,sEAAM;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,0EAAc;AACzC,uBAAuB,0EAAS;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB,0EAAS;AAClC,6CAA6C,0EAAS;AACtD;AACA;AACA,WAAW;AACX,CAAC;AACD,+BAA+B,sEAAM;AACrC;AACA,uDAAuD,0EAAS;AAChE;AACA,UAAU,wBAAwB;AAClC,uCAAuC,0EAAS;AAChD,iCAAiC,0EAAS;AAC1C;AACA,CAAC;AACD,gCAAgC,sEAAM;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,0EAAS;AAClD,2CAA2C,0EAAS;AACpD;AACA;AACA,CAAC;AACD;AACA,+BAA+B,sEAAM;AACrC,0CAA0C,sEAAM;AAChD;AACA,WAAW,kEAAO;AAClB;AACA,WAAW,kEAAO;AAClB;AACA,WAAW,kEAAO;AAClB;AACA,WAAW,kEAAO;AAClB;AACA;AACA,GAAG;AACH;AACA;AACA,uBAAuB,oDAAI;AAC3B;AACA,GAAG;AACH;AACA,GAAG,QAAQ,sDAAU;AACrB;AACA;AACA,MAAM,0EAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA,yCAAyC,kEAAO;AAChD;AACA;AACA;AACA,YAAY,OAAO,EAAE,wEAAa;AAClC,iBAAiB,0EAAc;AAC/B;AACA;AACA;AACA;AACA,oBAAoB,kBAAkB;AACtC;AACA;AACA;AACA;AACA,MAAM,+DAAG;AACT;AACA;AACA;AACA,QAAQ,+DAAG;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2FAA2F,0EAAS,oDAAoD,0EAAS,qDAAqD,0EAAS,6CAA6C,0EAAS;AACrR,IAAI,+DAAG;AACP;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA,8BAA8B,sEAAM;AACpC,CAAC;AACD,oCAAoC,sEAAM;AAC1C;AACA,CAAC;AACD,2BAA2B,sEAAM;AACjC,SAAS,0EAAS;AAClB,wBAAwB,0EAAS;AACjC;AACA;AACA,qBAAqB,qDAAM;AAC3B;AACA,6CAA6C,qDAAM,mDAAmD,qDAAM;AAC5G;AACA,EAAE,+DAAG;AACL,uCAAuC,GAAG;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gFAAgB;AAClB;AACA;AACA,OAAO,0BAA0B,EAAE,yBAAyB;AAC5D;AACA,CAAC;AACD,oCAAoC,sEAAM;AAC1C;AACA,CAAC;AACD,gCAAgC,sEAAM;AACtC,oBAAoB,8EAAc;AAClC;AACA;AACA,GAAG;AACH;AACA;AACA,cAAc,gBAAgB;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C;AAC3C,QAAQ;AACR;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,EAAE,+DAAG;AACL;AACA;AACA;AACA,IAAI,+DAAG;AACP;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,0EAAc;AACjD;AACA,OAAO;AACP;AACA;AACA,GAAG;AACH,EAAE,iFAAW;AACb,EAAE,+DAAG;AACL;AACA;AACA;AACA,MAAM,+DAAG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN,MAAM,+DAAG;AACT;AACA,GAAG;AACH;AACA;AACA;AACA,MAAM,+DAAG;AACT;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,+DAAG;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;;AAEA;AACA;AACA,UAAU,+EAAoB;AAC9B;AACA,eAAe,kEAAO;AACtB,GAAG;AACH;AACA,UAAU,yEAAc;AACxB,wBAAwB,sEAAM;AAC9B;AACA;AACA;AACA;AACA,GAAG;AACH;AAGE", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/mermaid/dist/chunks/mermaid.core/stateDiagram-DGXRK772.mjs"], "sourcesContent": ["import {\n  StateDB,\n  stateDiagram_default,\n  styles_default\n} from \"./chunk-AEK57VVT.mjs\";\nimport \"./chunk-RZ5BOZE2.mjs\";\nimport \"./chunk-TYCBKAJE.mjs\";\nimport \"./chunk-IIMUDSI4.mjs\";\nimport \"./chunk-VV3M67IP.mjs\";\nimport \"./chunk-HRU6DDCH.mjs\";\nimport \"./chunk-K557N5IZ.mjs\";\nimport \"./chunk-H2D2JQ3I.mjs\";\nimport \"./chunk-C3MQ5ANM.mjs\";\nimport {\n  utils_default\n} from \"./chunk-O4NI6UNU.mjs\";\nimport {\n  __name,\n  common_default,\n  configureSvgSize,\n  getConfig2 as getConfig,\n  log\n} from \"./chunk-YTJNT7DU.mjs\";\n\n// src/diagrams/state/stateRenderer.js\nimport { select } from \"d3\";\nimport { layout as dagreLayout } from \"dagre-d3-es/src/dagre/index.js\";\nimport * as graphlib from \"dagre-d3-es/src/graphlib/index.js\";\n\n// src/diagrams/state/shapes.js\nimport { line, curveBasis } from \"d3\";\n\n// src/diagrams/state/id-cache.js\nvar idCache = {};\nvar set = /* @__PURE__ */ __name((key, val) => {\n  idCache[key] = val;\n}, \"set\");\nvar get = /* @__PURE__ */ __name((k) => idCache[k], \"get\");\nvar keys = /* @__PURE__ */ __name(() => Object.keys(idCache), \"keys\");\nvar size = /* @__PURE__ */ __name(() => keys().length, \"size\");\nvar id_cache_default = {\n  get,\n  set,\n  keys,\n  size\n};\n\n// src/diagrams/state/shapes.js\nvar drawStartState = /* @__PURE__ */ __name((g) => g.append(\"circle\").attr(\"class\", \"start-state\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit), \"drawStartState\");\nvar drawDivider = /* @__PURE__ */ __name((g) => g.append(\"line\").style(\"stroke\", \"grey\").style(\"stroke-dasharray\", \"3\").attr(\"x1\", getConfig().state.textHeight).attr(\"class\", \"divider\").attr(\"x2\", getConfig().state.textHeight * 2).attr(\"y1\", 0).attr(\"y2\", 0), \"drawDivider\");\nvar drawSimpleState = /* @__PURE__ */ __name((g, stateDef) => {\n  const state = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 2 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const classBox = state.node().getBBox();\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", classBox.width + 2 * getConfig().state.padding).attr(\"height\", classBox.height + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return state;\n}, \"drawSimpleState\");\nvar drawDescrState = /* @__PURE__ */ __name((g, stateDef) => {\n  const addTspan = /* @__PURE__ */ __name(function(textEl, txt, isFirst2) {\n    const tSpan = textEl.append(\"tspan\").attr(\"x\", 2 * getConfig().state.padding).text(txt);\n    if (!isFirst2) {\n      tSpan.attr(\"dy\", getConfig().state.textHeight);\n    }\n  }, \"addTspan\");\n  const title = g.append(\"text\").attr(\"x\", 2 * getConfig().state.padding).attr(\"y\", getConfig().state.textHeight + 1.3 * getConfig().state.padding).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.descriptions[0]);\n  const titleBox = title.node().getBBox();\n  const titleHeight = titleBox.height;\n  const description = g.append(\"text\").attr(\"x\", getConfig().state.padding).attr(\n    \"y\",\n    titleHeight + getConfig().state.padding * 0.4 + getConfig().state.dividerMargin + getConfig().state.textHeight\n  ).attr(\"class\", \"state-description\");\n  let isFirst = true;\n  let isSecond = true;\n  stateDef.descriptions.forEach(function(descr) {\n    if (!isFirst) {\n      addTspan(description, descr, isSecond);\n      isSecond = false;\n    }\n    isFirst = false;\n  });\n  const descrLine = g.append(\"line\").attr(\"x1\", getConfig().state.padding).attr(\"y1\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"y2\", getConfig().state.padding + titleHeight + getConfig().state.dividerMargin / 2).attr(\"class\", \"descr-divider\");\n  const descrBox = description.node().getBBox();\n  const width = Math.max(descrBox.width, titleBox.width);\n  descrLine.attr(\"x2\", width + 3 * getConfig().state.padding);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding).attr(\"width\", width + 2 * getConfig().state.padding).attr(\"height\", descrBox.height + titleHeight + 2 * getConfig().state.padding).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"drawDescrState\");\nvar addTitleAndBox = /* @__PURE__ */ __name((g, stateDef, altBkg) => {\n  const pad = getConfig().state.padding;\n  const dblPad = 2 * getConfig().state.padding;\n  const orgBox = g.node().getBBox();\n  const orgWidth = orgBox.width;\n  const orgX = orgBox.x;\n  const title = g.append(\"text\").attr(\"x\", 0).attr(\"y\", getConfig().state.titleShift).attr(\"font-size\", getConfig().state.fontSize).attr(\"class\", \"state-title\").text(stateDef.id);\n  const titleBox = title.node().getBBox();\n  const titleWidth = titleBox.width + dblPad;\n  let width = Math.max(titleWidth, orgWidth);\n  if (width === orgWidth) {\n    width = width + dblPad;\n  }\n  let startX;\n  const graphBox = g.node().getBBox();\n  if (stateDef.doc) {\n  }\n  startX = orgX - pad;\n  if (titleWidth > orgWidth) {\n    startX = (orgWidth - width) / 2 + pad;\n  }\n  if (Math.abs(orgX - graphBox.x) < pad && titleWidth > orgWidth) {\n    startX = orgX - (titleWidth - orgWidth) / 2;\n  }\n  const lineY = 1 - getConfig().state.textHeight;\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\"y\", lineY).attr(\"class\", altBkg ? \"alt-composit\" : \"composit\").attr(\"width\", width).attr(\n    \"height\",\n    graphBox.height + getConfig().state.textHeight + getConfig().state.titleShift + 1\n  ).attr(\"rx\", \"0\");\n  title.attr(\"x\", startX + pad);\n  if (titleWidth <= orgWidth) {\n    title.attr(\"x\", orgX + (width - dblPad) / 2 - titleWidth / 2 + pad);\n  }\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", getConfig().state.textHeight * 3).attr(\"rx\", getConfig().state.radius);\n  g.insert(\"rect\", \":first-child\").attr(\"x\", startX).attr(\n    \"y\",\n    getConfig().state.titleShift - getConfig().state.textHeight - getConfig().state.padding\n  ).attr(\"width\", width).attr(\"height\", graphBox.height + 3 + 2 * getConfig().state.textHeight).attr(\"rx\", getConfig().state.radius);\n  return g;\n}, \"addTitleAndBox\");\nvar drawEndState = /* @__PURE__ */ __name((g) => {\n  g.append(\"circle\").attr(\"class\", \"end-state-outer\").attr(\"r\", getConfig().state.sizeUnit + getConfig().state.miniPadding).attr(\n    \"cx\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  ).attr(\n    \"cy\",\n    getConfig().state.padding + getConfig().state.sizeUnit + getConfig().state.miniPadding\n  );\n  return g.append(\"circle\").attr(\"class\", \"end-state-inner\").attr(\"r\", getConfig().state.sizeUnit).attr(\"cx\", getConfig().state.padding + getConfig().state.sizeUnit + 2).attr(\"cy\", getConfig().state.padding + getConfig().state.sizeUnit + 2);\n}, \"drawEndState\");\nvar drawForkJoinState = /* @__PURE__ */ __name((g, stateDef) => {\n  let width = getConfig().state.forkWidth;\n  let height = getConfig().state.forkHeight;\n  if (stateDef.parentId) {\n    let tmp = width;\n    width = height;\n    height = tmp;\n  }\n  return g.append(\"rect\").style(\"stroke\", \"black\").style(\"fill\", \"black\").attr(\"width\", width).attr(\"height\", height).attr(\"x\", getConfig().state.padding).attr(\"y\", getConfig().state.padding);\n}, \"drawForkJoinState\");\nvar _drawLongText = /* @__PURE__ */ __name((_text, x, y, g) => {\n  let textHeight = 0;\n  const textElem = g.append(\"text\");\n  textElem.style(\"text-anchor\", \"start\");\n  textElem.attr(\"class\", \"noteText\");\n  let text = _text.replace(/\\r\\n/g, \"<br/>\");\n  text = text.replace(/\\n/g, \"<br/>\");\n  const lines = text.split(common_default.lineBreakRegex);\n  let tHeight = 1.25 * getConfig().state.noteMargin;\n  for (const line2 of lines) {\n    const txt = line2.trim();\n    if (txt.length > 0) {\n      const span = textElem.append(\"tspan\");\n      span.text(txt);\n      if (tHeight === 0) {\n        const textBounds = span.node().getBBox();\n        tHeight += textBounds.height;\n      }\n      textHeight += tHeight;\n      span.attr(\"x\", x + getConfig().state.noteMargin);\n      span.attr(\"y\", y + textHeight + 1.25 * getConfig().state.noteMargin);\n    }\n  }\n  return { textWidth: textElem.node().getBBox().width, textHeight };\n}, \"_drawLongText\");\nvar drawNote = /* @__PURE__ */ __name((text, g) => {\n  g.attr(\"class\", \"state-note\");\n  const note = g.append(\"rect\").attr(\"x\", 0).attr(\"y\", getConfig().state.padding);\n  const rectElem = g.append(\"g\");\n  const { textWidth, textHeight } = _drawLongText(text, 0, 0, rectElem);\n  note.attr(\"height\", textHeight + 2 * getConfig().state.noteMargin);\n  note.attr(\"width\", textWidth + getConfig().state.noteMargin * 2);\n  return note;\n}, \"drawNote\");\nvar drawState = /* @__PURE__ */ __name(function(elem, stateDef) {\n  const id = stateDef.id;\n  const stateInfo = {\n    id,\n    label: stateDef.id,\n    width: 0,\n    height: 0\n  };\n  const g = elem.append(\"g\").attr(\"id\", id).attr(\"class\", \"stateGroup\");\n  if (stateDef.type === \"start\") {\n    drawStartState(g);\n  }\n  if (stateDef.type === \"end\") {\n    drawEndState(g);\n  }\n  if (stateDef.type === \"fork\" || stateDef.type === \"join\") {\n    drawForkJoinState(g, stateDef);\n  }\n  if (stateDef.type === \"note\") {\n    drawNote(stateDef.note.text, g);\n  }\n  if (stateDef.type === \"divider\") {\n    drawDivider(g);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length === 0) {\n    drawSimpleState(g, stateDef);\n  }\n  if (stateDef.type === \"default\" && stateDef.descriptions.length > 0) {\n    drawDescrState(g, stateDef);\n  }\n  const stateBox = g.node().getBBox();\n  stateInfo.width = stateBox.width + 2 * getConfig().state.padding;\n  stateInfo.height = stateBox.height + 2 * getConfig().state.padding;\n  id_cache_default.set(id, stateInfo);\n  return stateInfo;\n}, \"drawState\");\nvar edgeCount = 0;\nvar drawEdge = /* @__PURE__ */ __name(function(elem, path, relation) {\n  const getRelationType = /* @__PURE__ */ __name(function(type) {\n    switch (type) {\n      case StateDB.relationType.AGGREGATION:\n        return \"aggregation\";\n      case StateDB.relationType.EXTENSION:\n        return \"extension\";\n      case StateDB.relationType.COMPOSITION:\n        return \"composition\";\n      case StateDB.relationType.DEPENDENCY:\n        return \"dependency\";\n    }\n  }, \"getRelationType\");\n  path.points = path.points.filter((p) => !Number.isNaN(p.y));\n  const lineData = path.points;\n  const lineFunction = line().x(function(d) {\n    return d.x;\n  }).y(function(d) {\n    return d.y;\n  }).curve(curveBasis);\n  const svgPath = elem.append(\"path\").attr(\"d\", lineFunction(lineData)).attr(\"id\", \"edge\" + edgeCount).attr(\"class\", \"transition\");\n  let url = \"\";\n  if (getConfig().state.arrowMarkerAbsolute) {\n    url = window.location.protocol + \"//\" + window.location.host + window.location.pathname + window.location.search;\n    url = url.replace(/\\(/g, \"\\\\(\");\n    url = url.replace(/\\)/g, \"\\\\)\");\n  }\n  svgPath.attr(\n    \"marker-end\",\n    \"url(\" + url + \"#\" + getRelationType(StateDB.relationType.DEPENDENCY) + \"End)\"\n  );\n  if (relation.title !== void 0) {\n    const label = elem.append(\"g\").attr(\"class\", \"stateLabel\");\n    const { x, y } = utils_default.calcLabelPosition(path.points);\n    const rows = common_default.getRows(relation.title);\n    let titleHeight = 0;\n    const titleRows = [];\n    let maxWidth = 0;\n    let minX = 0;\n    for (let i = 0; i <= rows.length; i++) {\n      const title = label.append(\"text\").attr(\"text-anchor\", \"middle\").text(rows[i]).attr(\"x\", x).attr(\"y\", y + titleHeight);\n      const boundsTmp = title.node().getBBox();\n      maxWidth = Math.max(maxWidth, boundsTmp.width);\n      minX = Math.min(minX, boundsTmp.x);\n      log.info(boundsTmp.x, x, y + titleHeight);\n      if (titleHeight === 0) {\n        const titleBox = title.node().getBBox();\n        titleHeight = titleBox.height;\n        log.info(\"Title height\", titleHeight, y);\n      }\n      titleRows.push(title);\n    }\n    let boxHeight = titleHeight * rows.length;\n    if (rows.length > 1) {\n      const heightAdj = (rows.length - 1) * titleHeight * 0.5;\n      titleRows.forEach((title, i) => title.attr(\"y\", y + i * titleHeight - heightAdj));\n      boxHeight = titleHeight * rows.length;\n    }\n    const bounds = label.node().getBBox();\n    label.insert(\"rect\", \":first-child\").attr(\"class\", \"box\").attr(\"x\", x - maxWidth / 2 - getConfig().state.padding / 2).attr(\"y\", y - boxHeight / 2 - getConfig().state.padding / 2 - 3.5).attr(\"width\", maxWidth + getConfig().state.padding).attr(\"height\", boxHeight + getConfig().state.padding);\n    log.info(bounds);\n  }\n  edgeCount++;\n}, \"drawEdge\");\n\n// src/diagrams/state/stateRenderer.js\nvar conf;\nvar transformationLog = {};\nvar setConf = /* @__PURE__ */ __name(function() {\n}, \"setConf\");\nvar insertMarkers = /* @__PURE__ */ __name(function(elem) {\n  elem.append(\"defs\").append(\"marker\").attr(\"id\", \"dependencyEnd\").attr(\"refX\", 19).attr(\"refY\", 7).attr(\"markerWidth\", 20).attr(\"markerHeight\", 28).attr(\"orient\", \"auto\").append(\"path\").attr(\"d\", \"M 19,7 L9,13 L14,7 L9,1 Z\");\n}, \"insertMarkers\");\nvar draw = /* @__PURE__ */ __name(function(text, id, _version, diagObj) {\n  conf = getConfig().state;\n  const securityLevel = getConfig().securityLevel;\n  let sandboxElement;\n  if (securityLevel === \"sandbox\") {\n    sandboxElement = select(\"#i\" + id);\n  }\n  const root = securityLevel === \"sandbox\" ? select(sandboxElement.nodes()[0].contentDocument.body) : select(\"body\");\n  const doc = securityLevel === \"sandbox\" ? sandboxElement.nodes()[0].contentDocument : document;\n  log.debug(\"Rendering diagram \" + text);\n  const diagram2 = root.select(`[id='${id}']`);\n  insertMarkers(diagram2);\n  const rootDoc = diagObj.db.getRootDoc();\n  renderDoc(rootDoc, diagram2, void 0, false, root, doc, diagObj);\n  const padding = conf.padding;\n  const bounds = diagram2.node().getBBox();\n  const width = bounds.width + padding * 2;\n  const height = bounds.height + padding * 2;\n  const svgWidth = width * 1.75;\n  configureSvgSize(diagram2, height, svgWidth, conf.useMaxWidth);\n  diagram2.attr(\n    \"viewBox\",\n    `${bounds.x - conf.padding}  ${bounds.y - conf.padding} ` + width + \" \" + height\n  );\n}, \"draw\");\nvar getLabelWidth = /* @__PURE__ */ __name((text) => {\n  return text ? text.length * conf.fontSizeFactor : 1;\n}, \"getLabelWidth\");\nvar renderDoc = /* @__PURE__ */ __name((doc, diagram2, parentId, altBkg, root, domDocument, diagObj) => {\n  const graph = new graphlib.Graph({\n    compound: true,\n    multigraph: true\n  });\n  let i;\n  let edgeFreeDoc = true;\n  for (i = 0; i < doc.length; i++) {\n    if (doc[i].stmt === \"relation\") {\n      edgeFreeDoc = false;\n      break;\n    }\n  }\n  if (parentId) {\n    graph.setGraph({\n      rankdir: \"LR\",\n      multigraph: true,\n      compound: true,\n      // acyclicer: 'greedy',\n      ranker: \"tight-tree\",\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      isMultiGraph: true\n      // ranksep: 5,\n      // nodesep: 1\n    });\n  } else {\n    graph.setGraph({\n      rankdir: \"TB\",\n      multigraph: true,\n      compound: true,\n      // isCompound: true,\n      // acyclicer: 'greedy',\n      // ranker: 'longest-path'\n      ranksep: edgeFreeDoc ? 1 : conf.edgeLengthFactor,\n      nodeSep: edgeFreeDoc ? 1 : 50,\n      ranker: \"tight-tree\",\n      // ranker: 'network-simplex'\n      isMultiGraph: true\n    });\n  }\n  graph.setDefaultEdgeLabel(function() {\n    return {};\n  });\n  const states = diagObj.db.getStates();\n  const relations = diagObj.db.getRelations();\n  const keys2 = Object.keys(states);\n  let first = true;\n  for (const key of keys2) {\n    const stateDef = states[key];\n    if (parentId) {\n      stateDef.parentId = parentId;\n    }\n    let node;\n    if (stateDef.doc) {\n      let sub = diagram2.append(\"g\").attr(\"id\", stateDef.id).attr(\"class\", \"stateGroup\");\n      node = renderDoc(stateDef.doc, sub, stateDef.id, !altBkg, root, domDocument, diagObj);\n      if (first) {\n        sub = addTitleAndBox(sub, stateDef, altBkg);\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height + conf.padding / 2;\n        transformationLog[stateDef.id] = { y: conf.compositTitleSize };\n      } else {\n        let boxBounds = sub.node().getBBox();\n        node.width = boxBounds.width;\n        node.height = boxBounds.height;\n      }\n    } else {\n      node = drawState(diagram2, stateDef, graph);\n    }\n    if (stateDef.note) {\n      const noteDef = {\n        descriptions: [],\n        id: stateDef.id + \"-note\",\n        note: stateDef.note,\n        type: \"note\"\n      };\n      const note = drawState(diagram2, noteDef, graph);\n      if (stateDef.note.position === \"left of\") {\n        graph.setNode(node.id + \"-note\", note);\n        graph.setNode(node.id, node);\n      } else {\n        graph.setNode(node.id, node);\n        graph.setNode(node.id + \"-note\", note);\n      }\n      graph.setParent(node.id, node.id + \"-group\");\n      graph.setParent(node.id + \"-note\", node.id + \"-group\");\n    } else {\n      graph.setNode(node.id, node);\n    }\n  }\n  log.debug(\"Count=\", graph.nodeCount(), graph);\n  let cnt = 0;\n  relations.forEach(function(relation) {\n    cnt++;\n    log.debug(\"Setting edge\", relation);\n    graph.setEdge(\n      relation.id1,\n      relation.id2,\n      {\n        relation,\n        width: getLabelWidth(relation.title),\n        height: conf.labelHeight * common_default.getRows(relation.title).length,\n        labelpos: \"c\"\n      },\n      \"id\" + cnt\n    );\n  });\n  dagreLayout(graph);\n  log.debug(\"Graph after layout\", graph.nodes());\n  const svgElem = diagram2.node();\n  graph.nodes().forEach(function(v) {\n    if (v !== void 0 && graph.node(v) !== void 0) {\n      log.warn(\"Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\n        \"transform\",\n        \"translate(\" + (graph.node(v).x - graph.node(v).width / 2) + \",\" + (graph.node(v).y + (transformationLog[v] ? transformationLog[v].y : 0) - graph.node(v).height / 2) + \" )\"\n      );\n      root.select(\"#\" + svgElem.id + \" #\" + v).attr(\"data-x-shift\", graph.node(v).x - graph.node(v).width / 2);\n      const dividers = domDocument.querySelectorAll(\"#\" + svgElem.id + \" #\" + v + \" .divider\");\n      dividers.forEach((divider) => {\n        const parent = divider.parentElement;\n        let pWidth = 0;\n        let pShift = 0;\n        if (parent) {\n          if (parent.parentElement) {\n            pWidth = parent.parentElement.getBBox().width;\n          }\n          pShift = parseInt(parent.getAttribute(\"data-x-shift\"), 10);\n          if (Number.isNaN(pShift)) {\n            pShift = 0;\n          }\n        }\n        divider.setAttribute(\"x1\", 0 - pShift + 8);\n        divider.setAttribute(\"x2\", pWidth - pShift - 8);\n      });\n    } else {\n      log.debug(\"No Node \" + v + \": \" + JSON.stringify(graph.node(v)));\n    }\n  });\n  let stateBox = svgElem.getBBox();\n  graph.edges().forEach(function(e) {\n    if (e !== void 0 && graph.edge(e) !== void 0) {\n      log.debug(\"Edge \" + e.v + \" -> \" + e.w + \": \" + JSON.stringify(graph.edge(e)));\n      drawEdge(diagram2, graph.edge(e), graph.edge(e).relation);\n    }\n  });\n  stateBox = svgElem.getBBox();\n  const stateInfo = {\n    id: parentId ? parentId : \"root\",\n    label: parentId ? parentId : \"root\",\n    width: 0,\n    height: 0\n  };\n  stateInfo.width = stateBox.width + 2 * conf.padding;\n  stateInfo.height = stateBox.height + 2 * conf.padding;\n  log.debug(\"Doc rendered\", stateInfo, graph);\n  return stateInfo;\n}, \"renderDoc\");\nvar stateRenderer_default = {\n  setConf,\n  draw\n};\n\n// src/diagrams/state/stateDiagram.ts\nvar diagram = {\n  parser: stateDiagram_default,\n  get db() {\n    return new StateDB(1);\n  },\n  renderer: stateRenderer_default,\n  styles: styles_default,\n  init: /* @__PURE__ */ __name((cnf) => {\n    if (!cnf.state) {\n      cnf.state = {};\n    }\n    cnf.state.arrowMarkerAbsolute = cnf.arrowMarkerAbsolute;\n  }, \"init\")\n};\nexport {\n  diagram\n};\n"], "names": [], "sourceRoot": ""}