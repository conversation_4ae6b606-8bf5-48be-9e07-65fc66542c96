{"version": 3, "file": "6521.95f93bd416d53955c700.js?v=95f93bd416d53955c700", "mappings": ";;;;;AAAA,eAAe,KAAiD,oBAAoB,CAAyH,CAAC,iBAAiB,aAAa,OAAO,UAAU,cAAc,IAAI,+CAA+C,WAAW,IAAI,WAAW,GAAG,WAAW,GAAG,OAAO,gBAAgB,WAAW,IAAI,WAAW,GAAG,OAAO,KAAK,WAAW,IAAI,OAAO,EAAE,+DAA+D,SAAS,UAAU,sCAAsC,SAAS,kEAAkE,sBAAsB,EAAE,+DAA+D,kBAAkB,kEAAkE,yBAAyB,iBAAiB,8DAA8D,wBAAwB,MAAM,QAAQ,GAAG,4BAA4B,QAAQ,QAAQ,4BAA4B,mGAAmG,MAAM,WAAW,KAAK,YAAY,EAAE,aAAa,kBAAkB,0EAA0E,2CAA2C,SAAS,OAAO,YAAY,MAAM,YAAY,QAAQ,0BAA0B,EAAE,SAAS,oCAAoC,uBAAuB,WAAW,iCAAiC,gCAAgC,4BAA4B,QAAQ,6HAA6H,EAAE,YAAY,kBAAkB,6HAA6H,GAAG,YAAY,2BAA2B,0CAA0C,QAAQ,KAAK,EAAE,EAAE,qBAAqB,oBAAoB,YAAY,WAAW,KAAK,eAAe,qBAAqB,0DAA0D,uBAAuB,0DAA0D,mBAAmB,QAAQ,aAAa,kBAAkB,MAAM,cAAc,WAAW,+BAA+B,YAAY,YAAY,qCAAqC,SAAS,YAAY,QAAQ,sCAAsC,SAAS,yBAAyB,oCAAoC,EAAE,YAAY,uBAAuB,kBAAkB,gBAAgB,sBAAsB,MAAM,IAAI,cAAc,OAAO,kBAAkB,yEAAyE,sBAAsB,oBAAoB,EAAE,gCAAgC,YAAY,iBAAiB,sCAAsC,gHAAgH,UAAU,gCAAgC,MAAM;AACh+F", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@xterm/addon-web-links/lib/addon-web-links.js"], "sourcesContent": ["!function(e,t){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=t():\"function\"==typeof define&&define.amd?define([],t):\"object\"==typeof exports?exports.WebLinksAddon=t():e.WebLinksAddon=t()}(self,(()=>(()=>{\"use strict\";var e={6:(e,t)=>{function n(e){try{const t=new URL(e),n=t.password&&t.username?`${t.protocol}//${t.username}:${t.password}@${t.host}`:t.username?`${t.protocol}//${t.username}@${t.host}`:`${t.protocol}//${t.host}`;return e.toLocaleLowerCase().startsWith(n.toLocaleLowerCase())}catch(e){return!1}}Object.defineProperty(t,\"__esModule\",{value:!0}),t.LinkComputer=t.WebLinkProvider=void 0,t.WebLinkProvider=class{constructor(e,t,n,o={}){this._terminal=e,this._regex=t,this._handler=n,this._options=o}provideLinks(e,t){const n=o.computeLink(e,this._regex,this._terminal,this._handler);t(this._addCallbacks(n))}_addCallbacks(e){return e.map((e=>(e.leave=this._options.leave,e.hover=(t,n)=>{if(this._options.hover){const{range:o}=e;this._options.hover(t,n,o)}},e)))}};class o{static computeLink(e,t,r,i){const s=new RegExp(t.source,(t.flags||\"\")+\"g\"),[a,c]=o._getWindowedLineStrings(e-1,r),l=a.join(\"\");let d;const p=[];for(;d=s.exec(l);){const e=d[0];if(!n(e))continue;const[t,s]=o._mapStrIdx(r,c,0,d.index),[a,l]=o._mapStrIdx(r,t,s,e.length);if(-1===t||-1===s||-1===a||-1===l)continue;const h={start:{x:s+1,y:t+1},end:{x:l,y:a+1}};p.push({range:h,text:e,activate:i})}return p}static _getWindowedLineStrings(e,t){let n,o=e,r=e,i=0,s=\"\";const a=[];if(n=t.buffer.active.getLine(e)){const e=n.translateToString(!0);if(n.isWrapped&&\" \"!==e[0]){for(i=0;(n=t.buffer.active.getLine(--o))&&i<2048&&(s=n.translateToString(!0),i+=s.length,a.push(s),n.isWrapped&&-1===s.indexOf(\" \")););a.reverse()}for(a.push(e),i=0;(n=t.buffer.active.getLine(++r))&&n.isWrapped&&i<2048&&(s=n.translateToString(!0),i+=s.length,a.push(s),-1===s.indexOf(\" \")););}return[a,o]}static _mapStrIdx(e,t,n,o){const r=e.buffer.active,i=r.getNullCell();let s=n;for(;o;){const e=r.getLine(t);if(!e)return[-1,-1];for(let n=s;n<e.length;++n){e.getCell(n,i);const s=i.getChars();if(i.getWidth()&&(o-=s.length||1,n===e.length-1&&\"\"===s)){const e=r.getLine(t+1);e&&e.isWrapped&&(e.getCell(0,i),2===i.getWidth()&&(o+=1))}if(o<0)return[t,n]}t++,s=0}return[t,s]}}t.LinkComputer=o}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,n),i.exports}var o={};return(()=>{var e=o;Object.defineProperty(e,\"__esModule\",{value:!0}),e.WebLinksAddon=void 0;const t=n(6),r=/(https?|HTTPS?):[/]{2}[^\\s\"'!*(){}|\\\\\\^<>`]*[^\\s\"':,.!?{}|\\\\\\^~\\[\\]`()<>]/;function i(e,t){const n=window.open();if(n){try{n.opener=null}catch{}n.location.href=t}else console.warn(\"Opening link blocked as opener could not be cleared\")}e.WebLinksAddon=class{constructor(e=i,t={}){this._handler=e,this._options=t}activate(e){this._terminal=e;const n=this._options,o=n.urlRegex||r;this._linkProvider=this._terminal.registerLinkProvider(new t.WebLinkProvider(this._terminal,o,this._handler,n))}dispose(){this._linkProvider?.dispose()}}})(),o})()));\n//# sourceMappingURL=addon-web-links.js.map"], "names": [], "sourceRoot": ""}