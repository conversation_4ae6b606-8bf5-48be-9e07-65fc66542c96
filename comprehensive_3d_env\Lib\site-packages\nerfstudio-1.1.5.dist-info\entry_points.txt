[console_scripts]
ns-dev-sync-viser-message-defs = nerfstudio.scripts.viewer.sync_viser_message_defs:entrypoint
ns-dev-test = nerfstudio.scripts.github.run_actions:entrypoint
ns-download-data = nerfstudio.scripts.downloads.download_data:entrypoint
ns-eval = nerfstudio.scripts.eval:entrypoint
ns-export = nerfstudio.scripts.exporter:entrypoint
ns-install-cli = nerfstudio.scripts.completions.install:entrypoint
ns-process-data = nerfstudio.scripts.process_data:entrypoint
ns-render = nerfstudio.scripts.render:entrypoint
ns-train = nerfstudio.scripts.train:entrypoint
ns-viewer = nerfstudio.scripts.viewer.run_viewer:entrypoint
