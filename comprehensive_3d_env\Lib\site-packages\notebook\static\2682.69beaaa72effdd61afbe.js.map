{"version": 3, "file": "2682.69beaaa72effdd61afbe.js?v=69beaaa72effdd61afbe", "mappings": ";;;;;;;;;;AAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,qBAAqB;AACrB,6BAA6B;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yBAAyB;AACzB,kCAAkC;AAClC,UAAU;AACV;AACA;AACA,UAAU;AACV;AACA;AACA;AACA,gDAAgD;AAChD,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN,oBAAoB;AACpB;AACA;AACA;AACA,MAAM;AACN;AACA;AACA,MAAM;AACN;AACA,MAAM,qDAAqD,8BAA8B;AACzF;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN,qBAAqB;AACrB,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA,MAAM;AACN;AACA,MAAM;AACN;AACA;AACA;AACA,GAAG;AACH;AACA,qBAAqB,WAAW;AAChC,GAAG;AACH", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/toml.js"], "sourcesContent": ["export const toml = {\n  name: \"toml\",\n  startState: function () {\n    return {\n      inString: false,\n      stringType: \"\",\n      lhs: true,\n      inArray: 0\n    };\n  },\n  token: function (stream, state) {\n    //check for state changes\n    if (!state.inString && ((stream.peek() == '\"') || (stream.peek() == \"'\"))) {\n      state.stringType = stream.peek();\n      stream.next(); // Skip quote\n      state.inString = true; // Update state\n    }\n    if (stream.sol() && state.inArray === 0) {\n      state.lhs = true;\n    }\n    //return state\n    if (state.inString) {\n      while (state.inString && !stream.eol()) {\n        if (stream.peek() === state.stringType) {\n          stream.next(); // Skip quote\n          state.inString = false; // Clear flag\n        } else if (stream.peek() === '\\\\') {\n          stream.next();\n          stream.next();\n        } else {\n          stream.match(/^.[^\\\\\\\"\\']*/);\n        }\n      }\n      return state.lhs ? \"property\" : \"string\"; // Token style\n    } else if (state.inArray && stream.peek() === ']') {\n      stream.next();\n      state.inArray--;\n      return 'bracket';\n    } else if (state.lhs && stream.peek() === '[' && stream.skipTo(']')) {\n      stream.next();//skip closing ]\n      // array of objects has an extra open & close []\n      if (stream.peek() === ']') stream.next();\n      return \"atom\";\n    } else if (stream.peek() === \"#\") {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (stream.eatSpace()) {\n      return null;\n    } else if (state.lhs && stream.eatWhile(function (c) { return c != '=' && c != ' '; })) {\n      return \"property\";\n    } else if (state.lhs && stream.peek() === \"=\") {\n      stream.next();\n      state.lhs = false;\n      return null;\n    } else if (!state.lhs && stream.match(/^\\d\\d\\d\\d[\\d\\-\\:\\.T]*Z/)) {\n      return 'atom'; //date\n    } else if (!state.lhs && (stream.match('true') || stream.match('false'))) {\n      return 'atom';\n    } else if (!state.lhs && stream.peek() === '[') {\n      state.inArray++;\n      stream.next();\n      return 'bracket';\n    } else if (!state.lhs && stream.match(/^\\-?\\d+(?:\\.\\d+)?/)) {\n      return 'number';\n    } else if (!stream.eatSpace()) {\n      stream.next();\n    }\n    return null;\n  },\n  languageData: {\n    commentTokens: { line: '#' },\n  },\n};\n"], "names": [], "sourceRoot": ""}