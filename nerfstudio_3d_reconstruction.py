#!/usr/bin/env python3
"""
NeRFStudio-based 3D Reconstruction Pipeline
Uses NeRFStudio's built-in capabilities for high-quality 3D reconstruction from dashcam footage.
"""

import os
import sys
import cv2
import numpy as np
import torch
from pathlib import Path
import argparse
import logging
import time
import json
import subprocess
import shutil

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_frames_from_video(video_path: str, output_dir: str, max_frames: int = 100, quality: str = "medium"):
    """Extract frames from video for NeRFStudio processing"""
    logger.info(f"📹 Extracting frames from {video_path}...")
    
    frames_dir = Path(output_dir) / "images"
    frames_dir.mkdir(parents=True, exist_ok=True)
    
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    logger.info(f"Video: {fps} FPS, {total_frames} total frames")
    
    # Adjust frame extraction based on quality
    if quality == "low":
        frame_interval = max(1, total_frames // min(max_frames, 50))
    elif quality == "medium":
        frame_interval = max(1, total_frames // min(max_frames, 100))
    else:  # high
        frame_interval = max(1, total_frames // min(max_frames, 200))
    
    extracted_count = 0
    
    for frame_idx in range(0, total_frames, frame_interval):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        
        if ret:
            # Apply basic preprocessing for better NeRF results
            if quality in ["medium", "high"]:
                # Enhance contrast and reduce noise
                lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
                l, a, b = cv2.split(lab)
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
                l = clahe.apply(l)
                frame = cv2.merge([l, a, b])
                frame = cv2.cvtColor(frame, cv2.COLOR_LAB2BGR)
            
            frame_path = frames_dir / f"frame_{extracted_count:06d}.jpg"
            cv2.imwrite(str(frame_path), frame, [cv2.IMWRITE_JPEG_QUALITY, 95])
            extracted_count += 1
            
            if extracted_count >= max_frames:
                break
    
    cap.release()
    logger.info(f"✅ Extracted {extracted_count} frames to {frames_dir}")
    return frames_dir, extracted_count

def run_nerfstudio_data_processing(frames_dir: Path, output_dir: Path):
    """Process data using NeRFStudio's built-in data processing"""
    logger.info("📐 Processing data with NeRFStudio...")
    
    processed_dir = output_dir / "nerfstudio_data"
    
    try:
        # Use NeRFStudio's data processing for images
        cmd = [
            "ns-process-data", "images",
            "--data", str(frames_dir),
            "--output-dir", str(processed_dir),
            "--matching-method", "exhaustive",
            "--sfm-tool", "any",
            "--feature-type", "sift",
            "--matcher-type", "NN",
            "--num-downscales", "3"
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        if result.stdout:
            logger.info("NeRFStudio processing output:")
            logger.info(result.stdout)
        
        logger.info("✅ NeRFStudio data processing completed")
        return processed_dir
        
    except subprocess.CalledProcessError as e:
        logger.error(f"NeRFStudio data processing failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        
        # Fallback: create basic transforms.json
        logger.info("🔄 Falling back to basic camera setup...")
        return create_basic_transforms(frames_dir, output_dir)
    
    except FileNotFoundError:
        logger.error("ns-process-data command not found. Using fallback method.")
        return create_basic_transforms(frames_dir, output_dir)

def create_basic_transforms(frames_dir: Path, output_dir: Path):
    """Create basic transforms.json as fallback"""
    logger.info("📐 Creating basic camera transforms...")
    
    processed_dir = output_dir / "nerfstudio_data"
    processed_dir.mkdir(exist_ok=True)
    
    # Copy images
    images_dir = processed_dir / "images"
    if images_dir.exists():
        shutil.rmtree(images_dir)
    shutil.copytree(frames_dir, images_dir)
    
    # Get list of images
    image_files = sorted(list(images_dir.glob("*.jpg")))
    
    if not image_files:
        raise ValueError("No images found")
    
    # Read first image to get dimensions
    first_image = cv2.imread(str(image_files[0]))
    height, width = first_image.shape[:2]
    
    # Create transforms.json for NeRFStudio
    transforms = {
        "camera_angle_x": 0.8575560450553894,
        "camera_angle_y": 0.5033799409866333,
        "fl_x": width * 0.7,
        "fl_y": width * 0.7,
        "cx": width / 2,
        "cy": height / 2,
        "w": width,
        "h": height,
        "aabb_scale": 16,
        "frames": []
    }
    
    # Create camera poses for forward motion (typical dashcam)
    for i, image_file in enumerate(image_files):
        # Simple forward motion with slight variations
        progress = i / len(image_files)
        
        # Forward motion along Z-axis with slight camera movement
        x_pos = 0.2 * np.sin(progress * 2 * np.pi)  # Slight side-to-side
        y_pos = 1.5  # Dashcam height
        z_pos = -3.0 + progress * 6.0  # Forward motion
        
        # Small rotation variations
        angle_y = 0.1 * np.sin(progress * 4 * np.pi)  # Slight turning
        angle_x = -0.05  # Slight downward angle
        
        # Create transformation matrix
        cos_y, sin_y = np.cos(angle_y), np.sin(angle_y)
        cos_x, sin_x = np.cos(angle_x), np.sin(angle_x)
        
        # Rotation around Y then X
        transform_matrix = [
            [cos_y, sin_x * sin_y, cos_x * sin_y, x_pos],
            [0, cos_x, -sin_x, y_pos],
            [-sin_y, sin_x * cos_y, cos_x * cos_y, z_pos],
            [0, 0, 0, 1]
        ]
        
        frame_data = {
            "file_path": f"./images/{image_file.name}",
            "transform_matrix": transform_matrix
        }
        
        transforms["frames"].append(frame_data)
    
    # Save transforms
    transforms_path = processed_dir / "transforms.json"
    with open(transforms_path, 'w') as f:
        json.dump(transforms, f, indent=2)
    
    logger.info(f"✅ Created basic transforms: {transforms_path}")
    return processed_dir

def train_nerf_model(data_dir: Path, output_dir: Path, model_type: str = "nerfacto", quality: str = "medium"):
    """Train NeRF model using NeRFStudio"""
    logger.info(f"🌟 Training {model_type} model...")
    
    # Adjust training parameters based on quality
    if quality == "low":
        max_num_iterations = 5000
        steps_per_eval_image = 500
    elif quality == "medium":
        max_num_iterations = 15000
        steps_per_eval_image = 500
    else:  # high
        max_num_iterations = 30000
        steps_per_eval_image = 500
    
    try:
        cmd = [
            "ns-train", model_type,
            "--data", str(data_dir),
            "--output-dir", str(output_dir / "training_outputs"),
            f"--max-num-iterations", str(max_num_iterations),
            f"--steps-per-eval-image", str(steps_per_eval_image),
            "--viewer.quit-on-train-completion", "True"
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        logger.info(f"Training will take approximately {max_num_iterations//1000} minutes...")
        
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        if result.stdout:
            logger.info("Training completed successfully!")
            # Extract important information from output
            lines = result.stdout.split('\n')
            for line in lines[-20:]:  # Show last 20 lines
                if any(keyword in line.lower() for keyword in ['psnr', 'loss', 'saved', 'config']):
                    logger.info(line.strip())
        
        # Find the trained model directory
        training_outputs = output_dir / "training_outputs"
        if training_outputs.exists():
            model_dirs = [d for d in training_outputs.iterdir() if d.is_dir()]
            if model_dirs:
                latest_model = max(model_dirs, key=lambda x: x.stat().st_mtime)
                logger.info(f"✅ Model saved to: {latest_model}")
                return latest_model
        
        return training_outputs
        
    except subprocess.CalledProcessError as e:
        logger.error(f"NeRF training failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return None

def export_mesh(model_dir: Path, output_dir: Path):
    """Export mesh from trained NeRF model"""
    logger.info("📦 Exporting mesh from trained model...")
    
    try:
        # Find config file
        config_files = list(model_dir.glob("**/config.yml"))
        if not config_files:
            logger.error("No config file found in model directory")
            return None
        
        config_file = config_files[0]
        mesh_output = output_dir / "exported_mesh.ply"
        
        cmd = [
            "ns-export", "poisson",
            "--load-config", str(config_file),
            "--output-dir", str(output_dir),
            "--target-num-faces", "50000",
            "--num-pixels-per-side", "2048",
            "--normal-method", "open3d"
        ]
        
        logger.info(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        if result.stdout:
            logger.info("Mesh export output:")
            logger.info(result.stdout)
        
        # Find exported mesh
        exported_files = list(output_dir.glob("*.ply"))
        if exported_files:
            mesh_file = exported_files[0]
            logger.info(f"✅ Mesh exported to: {mesh_file}")
            return mesh_file
        else:
            logger.warning("No mesh file found after export")
            return None
            
    except subprocess.CalledProcessError as e:
        logger.error(f"Mesh export failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return None

def main():
    parser = argparse.ArgumentParser(description="NeRFStudio 3D Reconstruction Pipeline")
    parser.add_argument("input_video", help="Path to input dashcam video")
    parser.add_argument("--output-dir", default="nerfstudio_reconstruction", help="Output directory")
    parser.add_argument("--quality", choices=["low", "medium", "high"], default="medium", help="Reconstruction quality")
    parser.add_argument("--model", choices=["nerfacto", "instant-ngp", "splatfacto"], default="nerfacto", help="NeRF model type")
    parser.add_argument("--max-frames", type=int, default=100, help="Maximum frames to extract")
    
    args = parser.parse_args()
    
    if not Path(args.input_video).exists():
        print(f"❌ Video file not found: {args.input_video}")
        sys.exit(1)
    
    print("🚀 NERFSTUDIO 3D RECONSTRUCTION PIPELINE")
    print("=" * 50)
    print(f"Input video: {args.input_video}")
    print(f"Output directory: {args.output_dir}")
    print(f"Quality: {args.quality}")
    print(f"Model: {args.model}")
    print(f"Max frames: {args.max_frames}")
    print()
    
    start_time = time.time()
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    try:
        # Step 1: Extract frames
        frames_dir, frame_count = extract_frames_from_video(
            args.input_video, args.output_dir, args.max_frames, args.quality
        )
        
        # Step 2: Process data with NeRFStudio
        processed_dir = run_nerfstudio_data_processing(frames_dir, output_dir)
        
        # Step 3: Train NeRF model
        model_dir = train_nerf_model(processed_dir, output_dir, args.model, args.quality)
        
        # Step 4: Export mesh
        mesh_file = None
        if model_dir:
            mesh_file = export_mesh(model_dir, output_dir)
        
        # Step 5: Generate report
        processing_time = time.time() - start_time
        
        report = {
            "input_video": args.input_video,
            "frames_extracted": frame_count,
            "model_type": args.model,
            "quality": args.quality,
            "processing_time": processing_time,
            "model_directory": str(model_dir) if model_dir else None,
            "exported_mesh": str(mesh_file) if mesh_file else None,
            "status": "success" if model_dir else "partial_success"
        }
        
        report_path = output_dir / "reconstruction_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print("\n🎉 RECONSTRUCTION COMPLETED!")
        print(f"⏱️  Processing time: {processing_time:.1f} seconds ({processing_time/60:.1f} minutes)")
        print(f"📁 Output directory: {output_dir}")
        print(f"🎯 Frames processed: {frame_count}")
        print(f"🤖 Model type: {args.model}")
        if model_dir:
            print(f"📂 Model directory: {model_dir}")
        if mesh_file:
            print(f"📦 Exported mesh: {mesh_file}")
        print(f"📊 Report: {report_path}")
        print()
        print("Next steps:")
        print("- View the trained model using NeRFStudio viewer")
        print("- Open the exported mesh in Blender, MeshLab, or CloudCompare")
        print("- Check the reconstruction report for detailed metrics")
        
        if model_dir:
            print(f"\nTo view the model interactively:")
            print(f"ns-viewer --load-config {model_dir}/config.yml")
        
    except Exception as e:
        print(f"❌ RECONSTRUCTION FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
