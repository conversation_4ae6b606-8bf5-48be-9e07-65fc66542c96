{"version": 3, "file": "2702.bc49dbd258cca77aeea4.js?v=bc49dbd258cca77aeea4", "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/legacy-modes/mode/brainfuck.js"], "sourcesContent": ["var reserve = \"><+-.,[]\".split(\"\");\n/*\n  comments can be either:\n  placed behind lines\n\n  +++    this is a comment\n\n  where reserved characters cannot be used\n  or in a loop\n  [\n  this is ok to use [ ] and stuff\n  ]\n  or preceded by #\n*/\nexport const brainfuck = {\n  name: \"brainfuck\",\n  startState: function() {\n    return {\n      commentLine: false,\n      left: 0,\n      right: 0,\n      commentLoop: false\n    }\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null\n    if(stream.sol()){\n      state.commentLine = false;\n    }\n    var ch = stream.next().toString();\n    if(reserve.indexOf(ch) !== -1){\n      if(state.commentLine === true){\n        if(stream.eol()){\n          state.commentLine = false;\n        }\n        return \"comment\";\n      }\n      if(ch === \"]\" || ch === \"[\"){\n        if(ch === \"[\"){\n          state.left++;\n        }\n        else{\n          state.right++;\n        }\n        return \"bracket\";\n      }\n      else if(ch === \"+\" || ch === \"-\"){\n        return \"keyword\";\n      }\n      else if(ch === \"<\" || ch === \">\"){\n        return \"atom\";\n      }\n      else if(ch === \".\" || ch === \",\"){\n        return \"def\";\n      }\n    }\n    else{\n      state.commentLine = true;\n      if(stream.eol()){\n        state.commentLine = false;\n      }\n      return \"comment\";\n    }\n    if(stream.eol()){\n      state.commentLine = false;\n    }\n  }\n};\n"], "names": [], "sourceRoot": ""}