!!python/object:nerfstudio.engine.trainer.TrainerConfig
_target: !!python/name:nerfstudio.engine.trainer.Trainer ''
data: &id001 !!python/object/apply:pathlib.WindowsPath
- nerfstudio_reconstruction
- nerfstudio_data
experiment_name: nerfstudio_data
gradient_accumulation_steps: {}
load_checkpoint: null
load_config: null
load_dir: null
load_scheduler: true
load_step: null
log_gradients: false
logging: !!python/object:nerfstudio.configs.base_config.LoggingConfig
  local_writer: !!python/object:nerfstudio.configs.base_config.LocalWriterConfig
    _target: !!python/name:nerfstudio.utils.writer.LocalWriter ''
    enable: true
    max_log_size: 10
    stats_to_track: !!python/tuple
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Train Iter (time)
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Train Rays / Sec
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Test PSNR
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Vis Rays / Sec
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - Test Rays / Sec
    - !!python/object/apply:nerfstudio.utils.writer.EventName
      - ETA (time)
  max_buffer_size: 20
  profiler: basic
  relative_log_dir: !!python/object/apply:pathlib.WindowsPath []
  steps_per_log: 10
machine: !!python/object:nerfstudio.configs.base_config.MachineConfig
  device_type: cuda
  dist_url: auto
  machine_rank: 0
  num_devices: 1
  num_machines: 1
  seed: 42
max_num_iterations: 10000
method_name: instant-ngp
mixed_precision: true
optimizers:
  fields:
    optimizer: !!python/object:nerfstudio.engine.optimizers.AdamOptimizerConfig
      _target: !!python/name:torch.optim.adam.Adam ''
      eps: 1.0e-15
      lr: 0.01
      max_norm: null
      weight_decay: 0
    scheduler: !!python/object:nerfstudio.engine.schedulers.ExponentialDecaySchedulerConfig
      _target: !!python/name:nerfstudio.engine.schedulers.ExponentialDecayScheduler ''
      lr_final: 0.0001
      lr_pre_warmup: 1.0e-08
      max_steps: 200000
      ramp: cosine
      warmup_steps: 0
output_dir: !!python/object/apply:pathlib.WindowsPath
- nerfstudio_reconstruction
- training_outputs
pipeline: !!python/object:nerfstudio.pipelines.dynamic_batch.DynamicBatchPipelineConfig
  _target: !!python/name:nerfstudio.pipelines.dynamic_batch.DynamicBatchPipeline ''
  datamanager: !!python/object:nerfstudio.data.datamanagers.base_datamanager.VanillaDataManagerConfig
    _target: !!python/name:nerfstudio.data.datamanagers.base_datamanager.VanillaDataManager ''
    cache_images_type: float32
    camera_optimizer: null
    camera_res_scale_factor: 1.0
    collate_fn: !!python/name:nerfstudio.data.utils.nerfstudio_collate.nerfstudio_collate ''
    data: *id001
    dataparser: !!python/object:nerfstudio.data.dataparsers.nerfstudio_dataparser.NerfstudioDataParserConfig
      _target: !!python/name:nerfstudio.data.dataparsers.nerfstudio_dataparser.Nerfstudio ''
      auto_scale_poses: true
      center_method: poses
      data: !!python/object/apply:pathlib.WindowsPath []
      depth_unit_scale_factor: 0.001
      downscale_factor: null
      eval_interval: 8
      eval_mode: fraction
      load_3D_points: false
      mask_color: null
      orientation_method: up
      scale_factor: 1.0
      scene_scale: 1.0
      train_split_fraction: 0.9
    eval_image_indices: !!python/tuple
    - 0
    eval_num_images_to_sample_from: .inf
    eval_num_rays_per_batch: 4096
    eval_num_times_to_repeat_images: .inf
    images_on_gpu: false
    masks_on_gpu: false
    patch_size: 1
    pixel_sampler: !!python/object:nerfstudio.data.pixel_samplers.PixelSamplerConfig
      _target: !!python/name:nerfstudio.data.pixel_samplers.PixelSampler ''
      fisheye_crop_radius: null
      ignore_mask: false
      is_equirectangular: false
      keep_full_image: false
      max_num_iterations: 100
      num_rays_per_batch: 4096
      rejection_sample_mask: true
    train_num_images_to_sample_from: .inf
    train_num_rays_per_batch: 4096
    train_num_times_to_repeat_images: .inf
  max_num_samples_per_ray: 1024
  model: !!python/object:nerfstudio.models.instant_ngp.InstantNGPModelConfig
    _target: !!python/name:nerfstudio.models.instant_ngp.NGPModel ''
    alpha_thre: 0.01
    background_color: random
    collider_params: null
    cone_angle: 0.004
    disable_scene_contraction: false
    enable_collider: false
    eval_num_rays_per_chunk: 8192
    far_plane: 1000.0
    grid_levels: 4
    grid_resolution: 128
    log2_hashmap_size: 19
    loss_coefficients:
      rgb_loss_coarse: 1.0
      rgb_loss_fine: 1.0
    max_res: 2048
    near_plane: 0.05
    prompt: null
    render_step_size: null
    use_appearance_embedding: false
    use_gradient_scaling: false
  target_num_samples: 262144
project_name: nerfstudio-project
prompt: null
relative_model_dir: !!python/object/apply:pathlib.WindowsPath
- nerfstudio_models
save_only_latest_checkpoint: true
start_paused: false
steps_per_eval_all_images: 25000
steps_per_eval_batch: 500
steps_per_eval_image: 500
steps_per_save: 2000
timestamp: 2025-07-09_122232
use_grad_scaler: false
viewer: !!python/object:nerfstudio.configs.base_config.ViewerConfig
  camera_frustum_scale: 0.1
  default_composite_depth: true
  image_format: jpeg
  jpeg_quality: 75
  make_share_url: false
  max_num_display_images: 512
  num_rays_per_chunk: 4096
  quit_on_train_completion: true
  relative_log_filename: viewer_log_filename.txt
  websocket_host: 0.0.0.0
  websocket_port: null
  websocket_port_default: 7007
vis: viewer
